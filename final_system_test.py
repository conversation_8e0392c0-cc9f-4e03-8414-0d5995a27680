#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

def final_system_test():
    """اختبار نهائي شامل لجميع صفحات النظام"""
    base_url = "http://127.0.0.1:5000"
    
    print("🚀 الاختبار النهائي الشامل لنظام إدارة تأجير المعدات")
    print("=" * 70)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 الخادم: {base_url}")
    print("=" * 70)
    
    # إنشاء session للحفاظ على تسجيل الدخول
    session = requests.Session()
    
    # 1. اختبار تسجيل الدخول
    print("\n1️⃣ اختبار تسجيل الدخول...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    try:
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تسجيل الدخول نجح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return
    
    # 2. اختبار الصفحات الأساسية
    print("\n2️⃣ اختبار الصفحات الأساسية...")
    
    pages = [
        ("/dashboard", "لوحة التحكم"),
        ("/equipment", "صفحة المعدات"),
        ("/customers", "صفحة العملاء"),
        ("/offices", "صفحة المكاتب"),
        ("/new_rental", "صفحة إنشاء الإيجار"),
        ("/inventory", "صفحة المخزون"),
        ("/external_equipment", "المعدات الخارجية"),
        ("/rentals", "صفحة الإيجارات"),
    ]
    
    page_results = {}
    
    for url, name in pages:
        try:
            response = session.get(f"{base_url}{url}")
            
            if response.status_code == 200:
                content = response.text
                
                # تحليل محتوى الصفحة
                analysis = {
                    'status': 'success',
                    'content_length': len(content),
                    'has_table': '<table' in content and 'tbody' in content,
                    'has_data': any(keyword in content for keyword in ['كاميرا', 'أحمد', 'محمد', 'استوديو']),
                    'row_count': 0
                }
                
                # عد الصفوف حسب نوع الصفحة
                if 'equipment' in url:
                    analysis['row_count'] = content.count('<tr data-category=')
                elif 'customers' in url:
                    analysis['row_count'] = content.count('<tr data-customer=')
                elif 'offices' in url and url == '/offices':
                    analysis['row_count'] = content.count('<tr data-office=')
                
                page_results[url] = analysis
                print(f"✅ {name}: {len(content)} حرف - {analysis['row_count']} صف")
                
            elif response.status_code == 302:
                page_results[url] = {'status': 'redirect'}
                print(f"🔄 {name}: إعادة توجيه")
                
            else:
                page_results[url] = {'status': 'error', 'code': response.status_code}
                print(f"❌ {name}: خطأ {response.status_code}")
                
        except Exception as e:
            page_results[url] = {'status': 'exception', 'error': str(e)}
            print(f"❌ {name}: خطأ - {e}")
    
    # 3. اختبار API endpoints
    print("\n3️⃣ اختبار API endpoints...")
    
    apis = [
        ("/api/equipment/all", "جميع المعدات"),
        ("/api/equipment/available", "المعدات المتاحة"),
        ("/api/customers", "جميع العملاء"),
        ("/api/external-equipment/available", "المعدات الخارجية المتاحة"),
        ("/api/offices/3/equipment", "معدات المكتب 3"),
    ]
    
    api_results = {}
    
    for url, name in apis:
        try:
            response = session.get(f"{base_url}{url}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    count = 0
                    
                    if isinstance(data, dict):
                        if 'equipment' in data:
                            count = len(data['equipment'])
                        elif 'customers' in data:
                            count = len(data['customers'])
                        elif 'count' in data:
                            count = data['count']
                    elif isinstance(data, list):
                        count = len(data)
                    
                    api_results[url] = {'status': 'success', 'count': count}
                    print(f"✅ {name}: {count} عنصر")
                    
                except json.JSONDecodeError:
                    api_results[url] = {'status': 'invalid_json'}
                    print(f"❌ {name}: استجابة JSON غير صحيحة")
            else:
                api_results[url] = {'status': 'error', 'code': response.status_code}
                print(f"❌ {name}: خطأ {response.status_code}")
                
        except Exception as e:
            api_results[url] = {'status': 'exception', 'error': str(e)}
            print(f"❌ {name}: خطأ - {e}")
    
    # 4. اختبار الصفحات التفصيلية
    print("\n4️⃣ اختبار الصفحات التفصيلية...")
    
    detail_pages = [
        ("/offices/1", "تفاصيل المكتب 1"),
        ("/offices/3", "تفاصيل المكتب 3"),
    ]
    
    detail_results = {}
    
    for url, name in detail_pages:
        try:
            response = session.get(f"{base_url}{url}")
            
            if response.status_code == 200:
                content = response.text
                detail_results[url] = {
                    'status': 'success',
                    'content_length': len(content),
                    'has_office_name': any(office in content for office in ['استوديو', 'مكتب', 'معدات'])
                }
                print(f"✅ {name}: {len(content)} حرف")
            else:
                detail_results[url] = {'status': 'error', 'code': response.status_code}
                print(f"❌ {name}: خطأ {response.status_code}")
                
        except Exception as e:
            detail_results[url] = {'status': 'exception', 'error': str(e)}
            print(f"❌ {name}: خطأ - {e}")
    
    # 5. تحليل النتائج وإنشاء التقرير
    print("\n5️⃣ تحليل النتائج...")
    
    total_tests = len(pages) + len(apis) + len(detail_pages)
    successful_tests = 0
    
    # عد الاختبارات الناجحة
    for result in page_results.values():
        if result.get('status') in ['success', 'redirect']:
            successful_tests += 1
    
    for result in api_results.values():
        if result.get('status') == 'success':
            successful_tests += 1
    
    for result in detail_results.values():
        if result.get('status') == 'success':
            successful_tests += 1
    
    success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
    
    # 6. التقرير النهائي
    print("\n" + "=" * 70)
    print("📊 التقرير النهائي للاختبار الشامل")
    print("=" * 70)
    
    print(f"📈 إجمالي الاختبارات: {total_tests}")
    print(f"✅ الاختبارات الناجحة: {successful_tests}")
    print(f"❌ الاختبارات الفاشلة: {total_tests - successful_tests}")
    print(f"🎯 معدل النجاح: {success_rate:.1f}%")
    
    # تفاصيل الفئات
    print(f"\n📋 تفاصيل الفئات:")
    
    page_success = sum(1 for r in page_results.values() if r.get('status') in ['success', 'redirect'])
    print(f"   🔸 الصفحات الأساسية: {page_success}/{len(pages)} ({(page_success/len(pages)*100):.1f}%)")
    
    api_success = sum(1 for r in api_results.values() if r.get('status') == 'success')
    print(f"   🔸 API Endpoints: {api_success}/{len(apis)} ({(api_success/len(apis)*100):.1f}%)")
    
    detail_success = sum(1 for r in detail_results.values() if r.get('status') == 'success')
    print(f"   🔸 الصفحات التفصيلية: {detail_success}/{len(detail_pages)} ({(detail_success/len(detail_pages)*100):.1f}%)")
    
    # الصفحات التي تعرض البيانات
    print(f"\n📊 الصفحات التي تعرض البيانات:")
    for url, result in page_results.items():
        if result.get('status') == 'success':
            name = next(name for u, name in pages if u == url)
            rows = result.get('row_count', 0)
            has_data = result.get('has_data', False)
            status = "✅ يعرض البيانات" if rows > 0 or has_data else "⚠️ لا يعرض صفوف"
            print(f"   • {name}: {rows} صف - {status}")
    
    # API التي تعطي بيانات
    print(f"\n🔗 API التي تعطي بيانات:")
    for url, result in api_results.items():
        if result.get('status') == 'success':
            name = next(name for u, name in apis if u == url)
            count = result.get('count', 0)
            print(f"   • {name}: {count} عنصر")
    
    # تقييم عام
    if success_rate >= 90:
        status = "🌟 ممتاز"
        color = "🟢"
    elif success_rate >= 80:
        status = "✅ جيد جداً"
        color = "🟡"
    elif success_rate >= 70:
        status = "⚠️ جيد"
        color = "🟠"
    else:
        status = "❌ يحتاج تحسين"
        color = "🔴"
    
    print(f"\n🏆 التقييم العام: {status} {color}")
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    return {
        'success_rate': success_rate,
        'total_tests': total_tests,
        'successful_tests': successful_tests,
        'page_results': page_results,
        'api_results': api_results,
        'detail_results': detail_results
    }

if __name__ == "__main__":
    final_system_test()
