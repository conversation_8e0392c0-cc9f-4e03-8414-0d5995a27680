#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة صلاحيات الموظفين
Employee Permissions Management Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List
from user_manager import UserManager, UserRole, Permission

class EmployeePermissionsDialog:
    """نافذة إدارة صلاحيات الموظفين"""
    
    def __init__(self, parent, user_manager: UserManager):
        self.parent = parent
        self.user_manager = user_manager
        self.result = None
        
        # التحقق من الصلاحية
        if not self.user_manager.has_permission(Permission.USERS_EDIT):
            messagebox.showerror("خطأ", "ليس لديك صلاحية إدارة صلاحيات المستخدمين")
            return
        
        self.create_dialog()
        self.load_employees()
    
    def create_dialog(self):
        """إنشاء نافذة الحوار"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("إدارة صلاحيات الموظفين")
        self.dialog.geometry("800x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إدارة صلاحيات الموظفين", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إطار اختيار الموظف
        employee_frame = ttk.LabelFrame(main_frame, text="اختيار الموظف", padding="10")
        employee_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(employee_frame, text="الموظف:").pack(side=tk.LEFT, padx=(0, 10))
        
        self.employee_var = tk.StringVar()
        self.employee_combo = ttk.Combobox(employee_frame, textvariable=self.employee_var,
                                          state="readonly", width=40)
        self.employee_combo.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.employee_combo.bind('<<ComboboxSelected>>', self.on_employee_selected)
        
        # إطار الصلاحيات
        permissions_frame = ttk.LabelFrame(main_frame, text="الصلاحيات", padding="10")
        permissions_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # إنشاء إطارين: الأساسية والإضافية
        basic_frame = ttk.LabelFrame(permissions_frame, text="الصلاحيات الأساسية (غير قابلة للتغيير)")
        basic_frame.pack(fill=tk.X, pady=(0, 10))
        
        additional_frame = ttk.LabelFrame(permissions_frame, text="صلاحيات إضافية")
        additional_frame.pack(fill=tk.BOTH, expand=True)
        
        # الصلاحيات الأساسية
        basic_permissions = [
            (Permission.RENTALS_VIEW, "عرض الإيجارات"),
            (Permission.RENTALS_ADD, "إضافة إيجارات"),
            (Permission.RENTALS_EDIT, "تعديل الإيجارات"),
            (Permission.RENTALS_RETURN, "إرجاع المعدات"),
            (Permission.CUSTOMERS_VIEW, "عرض العملاء"),
            (Permission.CUSTOMERS_ADD, "إضافة عملاء"),
            (Permission.CUSTOMERS_EDIT, "تعديل العملاء"),
            (Permission.REPORTS_VIEW, "عرض التقارير"),
            (Permission.EQUIPMENT_VIEW, "عرض المعدات")
        ]
        
        for i, (permission, name) in enumerate(basic_permissions):
            row = i // 3
            col = i % 3
            
            check_var = tk.BooleanVar(value=True)
            check = ttk.Checkbutton(basic_frame, text=name, variable=check_var, state='disabled')
            check.grid(row=row, column=col, sticky=tk.W, padx=10, pady=2)
        
        # الصلاحيات الإضافية
        self.additional_permissions = [
            (Permission.EQUIPMENT_ADD, "إضافة معدات"),
            (Permission.EQUIPMENT_EDIT, "تعديل المعدات"),
            (Permission.EQUIPMENT_DELETE, "حذف المعدات"),
            (Permission.CUSTOMERS_DELETE, "حذف العملاء"),
            (Permission.RENTALS_DELETE, "حذف الإيجارات"),
            (Permission.REPORTS_EXPORT, "تصدير التقارير")
        ]
        
        self.permission_vars = {}
        
        for i, (permission, name) in enumerate(self.additional_permissions):
            row = i // 2
            col = i % 2
            
            var = tk.BooleanVar()
            self.permission_vars[permission] = var
            
            check = ttk.Checkbutton(additional_frame, text=name, variable=var,
                                   command=lambda p=permission: self.on_permission_changed(p))
            check.grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="حفظ التغييرات", 
                  command=self.save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إعادة تعيين", 
                  command=self.reset_permissions).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إغلاق", 
                  command=self.close).pack(side=tk.RIGHT)
        
        # متغيرات التتبع
        self.current_employee_id = None
        self.original_permissions = set()
    
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        try:
            users = self.user_manager.get_users()
            employees = [user for user in users if user['role'] == UserRole.EMPLOYEE.value]
            
            employee_list = []
            self.employees_data = {}
            
            for employee in employees:
                display_name = f"{employee['full_name']} ({employee['username']})"
                employee_list.append(display_name)
                self.employees_data[display_name] = employee
            
            self.employee_combo['values'] = employee_list
            
            if employee_list:
                self.employee_combo.set(employee_list[0])
                self.on_employee_selected()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل قائمة الموظفين: {str(e)}")
    
    def on_employee_selected(self, event=None):
        """معالج اختيار موظف"""
        selected = self.employee_var.get()
        if not selected or selected not in self.employees_data:
            return
        
        employee = self.employees_data[selected]
        self.current_employee_id = employee['id']
        
        # الحصول على صلاحيات الموظف الحالية
        current_permissions = set(self.user_manager.get_user_permissions(self.current_employee_id))
        self.original_permissions = current_permissions.copy()
        
        # تحديث حالة الصلاحيات الإضافية
        for permission, var in self.permission_vars.items():
            var.set(permission in current_permissions)
    
    def on_permission_changed(self, permission: Permission):
        """معالج تغيير صلاحية"""
        if not self.current_employee_id:
            return
        
        is_granted = self.permission_vars[permission].get()
        
        if is_granted:
            # منح الصلاحية
            success, message = self.user_manager.grant_permission(self.current_employee_id, permission)
            if not success:
                messagebox.showerror("خطأ", f"فشل في منح الصلاحية: {message}")
                self.permission_vars[permission].set(False)
        else:
            # سحب الصلاحية
            success, message = self.user_manager.revoke_permission(self.current_employee_id, permission)
            if not success:
                messagebox.showerror("خطأ", f"فشل في سحب الصلاحية: {message}")
                self.permission_vars[permission].set(True)
    
    def save_changes(self):
        """حفظ التغييرات"""
        if not self.current_employee_id:
            messagebox.showwarning("تحذير", "يرجى اختيار موظف أولاً")
            return
        
        messagebox.showinfo("نجح", "تم حفظ التغييرات بنجاح")
        self.update_original_permissions()
    
    def reset_permissions(self):
        """إعادة تعيين الصلاحيات للحالة الأصلية"""
        if not self.current_employee_id:
            return
        
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الصلاحيات للحالة الأصلية؟"):
            # إعادة تعيين الصلاحيات
            current_permissions = set(self.user_manager.get_user_permissions(self.current_employee_id))
            
            # سحب الصلاحيات الإضافية
            for permission in self.additional_permissions:
                perm, _ = permission
                if perm in current_permissions and perm not in self.original_permissions:
                    self.user_manager.revoke_permission(self.current_employee_id, perm)
            
            # منح الصلاحيات المفقودة
            for permission in self.original_permissions:
                if permission not in current_permissions:
                    self.user_manager.grant_permission(self.current_employee_id, permission)
            
            # تحديث الواجهة
            self.on_employee_selected()
            messagebox.showinfo("نجح", "تم إعادة تعيين الصلاحيات بنجاح")
    
    def update_original_permissions(self):
        """تحديث الصلاحيات الأصلية"""
        if self.current_employee_id:
            self.original_permissions = set(self.user_manager.get_user_permissions(self.current_employee_id))
    
    def close(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
        return self.result

class PermissionsSummaryDialog:
    """نافذة ملخص الصلاحيات"""
    
    def __init__(self, parent, user_manager: UserManager):
        self.parent = parent
        self.user_manager = user_manager
        
        self.create_dialog()
        self.load_permissions_summary()
    
    def create_dialog(self):
        """إنشاء نافذة الحوار"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("ملخص صلاحيات المستخدمين")
        self.dialog.geometry("900x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="ملخص صلاحيات المستخدمين", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # جدول الصلاحيات
        columns = ['المستخدم', 'الدور', 'عدد الصلاحيات', 'الصلاحيات الإضافية']
        
        self.tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=200)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # زر الإغلاق
        ttk.Button(main_frame, text="إغلاق", command=self.close).pack(pady=(10, 0))
    
    def load_permissions_summary(self):
        """تحميل ملخص الصلاحيات"""
        try:
            users = self.user_manager.get_users()
            
            for user in users:
                user_permissions = self.user_manager.get_user_permissions(user['id'])
                role_permissions = self.user_manager.get_role_permissions(UserRole(user['role']))
                
                # الصلاحيات الإضافية
                additional_permissions = set(user_permissions) - set(role_permissions)
                additional_count = len(additional_permissions)
                additional_text = f"+{additional_count}" if additional_count > 0 else "لا توجد"
                
                role_display = self.user_manager.get_role_display_name(UserRole(user['role']))
                
                self.tree.insert('', tk.END, values=[
                    user['full_name'],
                    role_display,
                    len(user_permissions),
                    additional_text
                ])
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل ملخص الصلاحيات: {str(e)}")
    
    def close(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
