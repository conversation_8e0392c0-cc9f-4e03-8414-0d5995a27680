#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

def test_equipment_display():
    """اختبار عرض المعدات في جميع الصفحات"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 اختبار عرض المعدات في النظام")
    print("=" * 60)
    
    # اختبار 1: صفحة المعدات الرئيسية
    print("\n1️⃣ اختبار صفحة المعدات الرئيسية...")
    try:
        response = requests.get(f"{base_url}/equipment")
        if response.status_code == 200:
            content = response.text
            if "معدة" in content and "table" in content:
                print("✅ صفحة المعدات تعمل وتحتوي على جدول")
            else:
                print("⚠️ صفحة المعدات تعمل لكن قد لا تحتوي على معدات")
        elif response.status_code == 302:
            print("🔄 صفحة المعدات تحتاج تسجيل دخول")
        else:
            print(f"❌ خطأ في صفحة المعدات: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة المعدات: {e}")
    
    # اختبار 2: صفحة إنشاء الإيجار
    print("\n2️⃣ اختبار صفحة إنشاء الإيجار...")
    try:
        response = requests.get(f"{base_url}/new_rental")
        if response.status_code == 200:
            content = response.text
            if "المعدات المتاحة" in content and "المعدات المختارة" in content:
                print("✅ صفحة إنشاء الإيجار تعمل وتحتوي على أقسام المعدات")
            else:
                print("⚠️ صفحة إنشاء الإيجار تعمل لكن قد لا تحتوي على أقسام المعدات")
        elif response.status_code == 302:
            print("🔄 صفحة إنشاء الإيجار تحتاج تسجيل دخول")
        else:
            print(f"❌ خطأ في صفحة إنشاء الإيجار: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة إنشاء الإيجار: {e}")
    
    # اختبار 3: صفحة المكاتب
    print("\n3️⃣ اختبار صفحة المكاتب...")
    try:
        response = requests.get(f"{base_url}/offices")
        if response.status_code == 200:
            content = response.text
            if "المكاتب والموردين" in content:
                print("✅ صفحة المكاتب تعمل")
            else:
                print("⚠️ صفحة المكاتب تعمل لكن قد تحتوي على مشاكل")
        elif response.status_code == 302:
            print("🔄 صفحة المكاتب تحتاج تسجيل دخول")
        else:
            print(f"❌ خطأ في صفحة المكاتب: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بصفحة المكاتب: {e}")

def test_api_endpoints():
    """اختبار API endpoints للمعدات"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n🔌 اختبار API endpoints...")
    print("=" * 60)
    
    endpoints = [
        ("/api/equipment/available", "المعدات المتاحة"),
        ("/api/external-equipment/available", "المعدات الخارجية"),
        ("/api/customers", "العملاء"),
    ]
    
    for endpoint, name in endpoints:
        print(f"\n🔍 اختبار {name}...")
        try:
            response = requests.get(f"{base_url}{endpoint}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        count = len(data)
                    elif isinstance(data, dict):
                        count = data.get('count', len(data.get('equipment', data.get('customers', []))))
                    else:
                        count = 0
                    print(f"✅ {name}: {count} عنصر")
                except:
                    print(f"✅ {name}: يعمل (لكن البيانات غير واضحة)")
            elif response.status_code == 302:
                print(f"🔄 {name}: يحتاج تسجيل دخول")
            else:
                print(f"❌ {name}: خطأ {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: خطأ في الاتصال - {e}")

def test_database_content():
    """اختبار محتوى قاعدة البيانات"""
    print("\n💾 اختبار محتوى قاعدة البيانات...")
    print("=" * 60)
    
    try:
        import sqlite3
        from config.settings import get_config
        
        config = get_config()
        conn = sqlite3.connect(config.DATABASE_PATH)
        cursor = conn.cursor()
        
        # فحص المعدات
        cursor.execute('SELECT COUNT(*) FROM equipment')
        equipment_count = cursor.fetchone()[0]
        print(f"📦 المعدات الداخلية: {equipment_count}")
        
        # فحص المعدات الخارجية
        cursor.execute('SELECT COUNT(*) FROM external_equipment')
        external_count = cursor.fetchone()[0]
        print(f"🏢 المعدات الخارجية: {external_count}")
        
        # فحص المكاتب
        cursor.execute('SELECT COUNT(*) FROM offices')
        offices_count = cursor.fetchone()[0]
        print(f"🏛️ المكاتب: {offices_count}")
        
        # فحص العملاء
        cursor.execute('SELECT COUNT(*) FROM customers')
        customers_count = cursor.fetchone()[0]
        print(f"👥 العملاء: {customers_count}")
        
        # فحص ربط المعدات الخارجية بالمكاتب
        cursor.execute('SELECT COUNT(*) FROM external_equipment WHERE office_id IS NOT NULL')
        linked_equipment = cursor.fetchone()[0]
        print(f"🔗 المعدات الخارجية المربوطة بمكاتب: {linked_equipment}")
        
        conn.close()
        
        if equipment_count > 0 and external_count > 0 and offices_count > 0:
            print("✅ قاعدة البيانات تحتوي على بيانات كافية للاختبار")
        else:
            print("⚠️ قاعدة البيانات قد تحتاج إلى بيانات إضافية")
            
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def main():
    print("🚀 بدء اختبار إصلاحات عرض المعدات")
    print("=" * 60)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_database_content()
    test_equipment_display()
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    print("📋 ملاحظة: إذا كانت الصفحات تحتاج تسجيل دخول، فهذا طبيعي")
    print("🌐 للوصول للنظام: http://127.0.0.1:5000")
    print("🔐 المستخدم: admin | كلمة المرور: admin123")

if __name__ == "__main__":
    main()
