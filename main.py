#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main application for Camera and Lens Rental Management System
Desktop application using Python + Tkinter + SQLite with Arabic support
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
import sys
import os
from datetime import date

# Import our modules
from database import DatabaseManager
from forms import (EquipmentDialog, CustomerDialog, RentalDialog,
                  DataGrid, ReturnDialog, ReportsDialog, NotificationPanel)
from image_utils import image_handler

class RentalManagementApp:
    """Main application class for the rental management system"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("النظام النهائي - إدارة تأجير المعدات")
        self.root.geometry("1200x800")
        
        # Initialize database
        self.db = DatabaseManager()
        
        # Configure Arabic font support
        self.setup_fonts()
        
        # Create main interface
        self.create_menu()
        self.create_main_interface()
        
        # Load initial data
        self.refresh_all_data()
    
    def setup_fonts(self):
        """Configure fonts for Arabic text support"""
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            # Fallback fonts
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
        
        # Configure default font for the application
        self.root.option_add("*Font", self.arabic_font)
    
    def create_menu(self):
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إنشاء بيانات تجريبية", command=self.create_sample_data)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.root.quit)
        
        # Equipment menu
        equipment_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المعدات", menu=equipment_menu)
        equipment_menu.add_command(label="إضافة معدة", command=self.add_equipment)
        equipment_menu.add_command(label="تعديل معدة", command=self.edit_equipment)
        equipment_menu.add_command(label="حذف معدة", command=self.delete_equipment)
        
        # Customer menu
        customer_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="العملاء", menu=customer_menu)
        customer_menu.add_command(label="إضافة عميل", command=self.add_customer)
        customer_menu.add_command(label="تعديل عميل", command=self.edit_customer)
        customer_menu.add_command(label="حذف عميل", command=self.delete_customer)
        
        # Rental menu
        rental_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإيجارات", menu=rental_menu)
        rental_menu.add_command(label="إيجار واحد", command=self.new_rental)
        rental_menu.add_command(label="إيجار متعدد", command=self.new_multi_rental)
        rental_menu.add_separator()
        rental_menu.add_command(label="إرجاع معدة", command=self.return_equipment)
        
        # Reports menu
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="المعدات المؤجرة حالياً", command=lambda: self.notebook.select(3))
        reports_menu.add_command(label="المعدات المتاحة", command=lambda: self.notebook.select(0))
        reports_menu.add_command(label="تاريخ الإيجارات", command=lambda: self.notebook.select(4))
        reports_menu.add_separator()
        reports_menu.add_command(label="تقارير مفصلة", command=self.show_detailed_reports)

        # Notifications menu
        notifications_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التنبيهات", menu=notifications_menu)
        notifications_menu.add_command(label="عرض التنبيهات", command=lambda: self.notebook.select(5))
        notifications_menu.add_command(label="تحديث الحالات المتأخرة", command=self.update_overdue_status)
    
    def create_main_interface(self):
        """Create main tabbed interface"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Equipment tab
        self.equipment_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.equipment_frame, text="المعدات")
        self.create_equipment_tab()
        
        # Customers tab
        self.customers_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.customers_frame, text="العملاء")
        self.create_customers_tab()
        
        # New Rental tab
        self.new_rental_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.new_rental_frame, text="إيجار جديد")
        self.create_new_rental_tab()

        # Multi Rentals tab
        self.multi_rentals_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.multi_rentals_frame, text="الإيجارات المتعددة")
        self.create_multi_rentals_tab()

        # Active Rentals tab
        self.active_rentals_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.active_rentals_frame, text="الإيجارات النشطة")
        self.create_active_rentals_tab()
        
        # Rental History tab
        self.rental_history_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.rental_history_frame, text="تاريخ الإيجارات")
        self.create_rental_history_tab()

        # Notifications tab
        self.notifications_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.notifications_frame, text="التنبيهات")
        self.create_notifications_tab()
    
    def create_equipment_tab(self):
        """Create equipment management tab"""
        # Toolbar
        toolbar = ttk.Frame(self.equipment_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(toolbar, text="إضافة معدة", command=self.add_equipment).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تعديل", command=self.edit_equipment).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="حذف", command=self.delete_equipment).pack(side=tk.LEFT, padx=(0, 5))

        # Category filter buttons
        filter_frame = ttk.Frame(toolbar)
        filter_frame.pack(side=tk.LEFT, padx=(20, 0))

        ttk.Label(filter_frame, text="فلترة حسب النوع:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_frame, text="الكل", command=lambda: self.filter_equipment(None)).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(filter_frame, text="كاميرات", command=lambda: self.filter_equipment("كاميرات")).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(filter_frame, text="عدسات", command=lambda: self.filter_equipment("عدسات")).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(filter_frame, text="معدات إضاءة", command=lambda: self.filter_equipment("معدات إضاءة")).pack(side=tk.LEFT, padx=(0, 2))

        ttk.Button(toolbar, text="تحديث", command=self.refresh_equipment).pack(side=tk.RIGHT)

        # Equipment data grid
        equipment_columns = [
            {'id': 'id', 'text': 'المعرف', 'width': 60},
            {'id': 'name', 'text': 'اسم المعدة', 'width': 180},
            {'id': 'model', 'text': 'الموديل', 'width': 120},
            {'id': 'serial_number', 'text': 'الرقم التسلسلي', 'width': 120},
            {'id': 'category', 'text': 'النوع', 'width': 100},
            {'id': 'status', 'text': 'الحالة', 'width': 80},
            {'id': 'created_date', 'text': 'تاريخ الإضافة', 'width': 120}
        ]

        self.equipment_grid = DataGrid(self.equipment_frame, equipment_columns,
                                     self.on_equipment_double_click)
        self.equipment_grid.pack(fill=tk.BOTH, expand=True)

        # Store current filter
        self.current_equipment_filter = None
    
    def create_customers_tab(self):
        """Create customers management tab"""
        # Toolbar
        toolbar = ttk.Frame(self.customers_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="إضافة عميل", command=self.add_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تعديل", command=self.edit_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="حذف", command=self.delete_customer).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", command=self.refresh_customers).pack(side=tk.RIGHT)
        
        # Customers data grid
        customer_columns = [
            {'id': 'id', 'text': 'المعرف', 'width': 60},
            {'id': 'name', 'text': 'اسم العميل', 'width': 180},
            {'id': 'phone', 'text': 'رقم الهاتف', 'width': 130},
            {'id': 'national_id', 'text': 'الرقم القومي', 'width': 120},
            {'id': 'created_date', 'text': 'تاريخ الإضافة', 'width': 120}
        ]
        
        self.customers_grid = DataGrid(self.customers_frame, customer_columns,
                                     self.on_customer_double_click)
        self.customers_grid.pack(fill=tk.BOTH, expand=True)
    
    def create_new_rental_tab(self):
        """Create new rental tab"""
        # Instructions
        instructions = ttk.Label(self.new_rental_frame, 
                               text="لإنشاء إيجار جديد، استخدم القائمة 'الإيجارات' -> 'إيجار جديد' أو اضغط على الزر أدناه",
                               font=self.arabic_font)
        instructions.pack(pady=20)
        
        # New rental button
        ttk.Button(self.new_rental_frame, text="إنشاء إيجار جديد", 
                  command=self.new_rental).pack(pady=10)
        
        # Quick stats frame
        stats_frame = ttk.LabelFrame(self.new_rental_frame, text="إحصائيات سريعة")
        stats_frame.pack(fill=tk.X, padx=20, pady=20)
        
        self.stats_labels = {}
        stats_info = [
            ('total_equipment', 'إجمالي المعدات'),
            ('available_equipment', 'المعدات المتاحة'),
            ('rented_equipment', 'المعدات المؤجرة'),
            ('total_customers', 'إجمالي العملاء'),
            ('active_rentals', 'الإيجارات النشطة'),
            ('pending_returns', 'تنبيهات الإرجاع')
        ]
        
        for i, (key, label) in enumerate(stats_info):
            row = i // 2
            col = i % 2
            
            ttk.Label(stats_frame, text=f"{label}:").grid(row=row, column=col*2, 
                                                         sticky=tk.W, padx=10, pady=5)
            self.stats_labels[key] = ttk.Label(stats_frame, text="0", 
                                              font=self.arabic_font_bold)
            self.stats_labels[key].grid(row=row, column=col*2+1, sticky=tk.W, 
                                       padx=10, pady=5)

    def create_multi_rentals_tab(self):
        """Create multi rentals management tab"""
        # Toolbar
        toolbar = ttk.Frame(self.multi_rentals_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(toolbar, text="إيجار متعدد جديد", command=self.new_multi_rental).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="عرض التفاصيل", command=self.view_multi_rental_details).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="إرجاع الإيجار", command=self.return_multi_rental).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", command=self.refresh_multi_rentals).pack(side=tk.RIGHT)

        # Multi rentals data grid
        multi_rental_columns = [
            {'id': 'id', 'text': 'رقم المجموعة', 'width': 100},
            {'id': 'customer_name', 'text': 'اسم العميل', 'width': 150},
            {'id': 'customer_phone', 'text': 'رقم الهاتف', 'width': 120},
            {'id': 'items_count', 'text': 'عدد المعدات', 'width': 100},
            {'id': 'rental_date', 'text': 'تاريخ الإيجار', 'width': 100},
            {'id': 'return_date', 'text': 'تاريخ الإرجاع', 'width': 100},
            {'id': 'total_price', 'text': 'إجمالي السعر', 'width': 100},
            {'id': 'status', 'text': 'الحالة', 'width': 80}
        ]

        self.multi_rentals_grid = DataGrid(self.multi_rentals_frame, multi_rental_columns,
                                         self.on_multi_rental_double_click)
        self.multi_rentals_grid.pack(fill=tk.BOTH, expand=True)

    def create_active_rentals_tab(self):
        """Create active rentals tab"""
        # Toolbar
        toolbar = ttk.Frame(self.active_rentals_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="إرجاع معدة", command=self.return_equipment).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", command=self.refresh_active_rentals).pack(side=tk.RIGHT)
        
        # Active rentals data grid
        rental_columns = [
            {'id': 'id', 'text': 'رقم الإيجار', 'width': 80},
            {'id': 'customer_name', 'text': 'اسم العميل', 'width': 150},
            {'id': 'customer_phone', 'text': 'رقم الهاتف', 'width': 120},
            {'id': 'equipment_name', 'text': 'اسم المعدة', 'width': 150},
            {'id': 'equipment_model', 'text': 'الموديل', 'width': 120},
            {'id': 'rental_date', 'text': 'تاريخ الإيجار', 'width': 100},
            {'id': 'return_date', 'text': 'تاريخ الإرجاع المتوقع', 'width': 120},
            {'id': 'price', 'text': 'السعر', 'width': 80}
        ]
        
        self.active_rentals_grid = DataGrid(self.active_rentals_frame, rental_columns,
                                          self.on_rental_double_click)
        self.active_rentals_grid.pack(fill=tk.BOTH, expand=True)
    
    def create_rental_history_tab(self):
        """Create rental history tab"""
        # Toolbar
        toolbar = ttk.Frame(self.rental_history_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="تحديث", command=self.refresh_rental_history).pack(side=tk.RIGHT)
        
        # Rental history data grid
        history_columns = [
            {'id': 'id', 'text': 'رقم الإيجار', 'width': 80},
            {'id': 'customer_name', 'text': 'اسم العميل', 'width': 150},
            {'id': 'equipment_name', 'text': 'اسم المعدة', 'width': 150},
            {'id': 'equipment_model', 'text': 'الموديل', 'width': 120},
            {'id': 'rental_date', 'text': 'تاريخ الإيجار', 'width': 100},
            {'id': 'return_date', 'text': 'تاريخ الإرجاع المتوقع', 'width': 120},
            {'id': 'actual_return_date', 'text': 'تاريخ الإرجاع الفعلي', 'width': 120},
            {'id': 'status', 'text': 'الحالة', 'width': 80},
            {'id': 'price', 'text': 'السعر', 'width': 80}
        ]
        
        self.rental_history_grid = DataGrid(self.rental_history_frame, history_columns)
        self.rental_history_grid.pack(fill=tk.BOTH, expand=True)

    def create_notifications_tab(self):
        """Create notifications and alerts tab"""
        # Instructions
        instructions = ttk.Label(self.notifications_frame,
                               text="تنبيهات إرجاع المعدات - يتم التحديث تلقائياً كل 5 دقائق",
                               font=self.arabic_font)
        instructions.pack(pady=10)

        # Create notification panel
        self.notification_panel = NotificationPanel(self.notifications_frame, self.db)
        self.notification_panel.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Event handlers for equipment
    def add_equipment(self):
        """Add new equipment"""
        dialog = EquipmentDialog(self.root)
        result = dialog.show()

        if result:
            if self.db.add_equipment(result['name'], result['model'],
                                   result['serial_number'], result['category'], result['status']):
                messagebox.showinfo("نجح", "تم إضافة المعدة بنجاح")
                self.refresh_equipment()
                self.update_stats()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة المعدة")

    def edit_equipment(self):
        """Edit selected equipment"""
        selected = self.equipment_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار معدة للتعديل")
            return

        dialog = EquipmentDialog(self.root, selected)
        result = dialog.show()

        if result:
            if self.db.update_equipment(selected['id'], result['name'], result['model'],
                                      result['serial_number'], result['category'], result['status']):
                messagebox.showinfo("نجح", "تم تحديث المعدة بنجاح")
                self.refresh_equipment()
                self.update_stats()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث المعدة")

    def delete_equipment(self):
        """Delete selected equipment"""
        selected = self.equipment_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار معدة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف المعدة '{selected['name']}'؟"):
            if self.db.delete_equipment(selected['id']):
                messagebox.showinfo("نجح", "تم حذف المعدة بنجاح")
                self.refresh_equipment()
                self.update_stats()
            else:
                messagebox.showerror("خطأ", "فشل في حذف المعدة")

    def on_equipment_double_click(self, equipment_data):
        """Handle double-click on equipment"""
        self.edit_equipment()

    def filter_equipment(self, category):
        """Filter equipment by category"""
        self.current_equipment_filter = category
        self.refresh_equipment()

    # Event handlers for customers
    def add_customer(self):
        """Add new customer"""
        dialog = CustomerDialog(self.root)
        result = dialog.show()

        if result:
            # Handle photo saving
            photo_path = None
            if result.get('photo_path'):
                # First add customer to get ID, then save photo
                if self.db.add_customer(result['name'], result['phone'], result.get('national_id')):
                    # Get the newly created customer ID
                    customers = self.db.search_customers(result['phone'])
                    if customers:
                        customer_id = customers[0]['id']
                        # Save photo with customer ID
                        if result['photo_path'] != customers[0].get('photo_path'):  # New photo selected
                            photo_path = image_handler.save_customer_photo(result['photo_path'], customer_id)
                            if photo_path:
                                # Update customer with photo path
                                self.db.update_customer(customer_id, result['name'], result['phone'], photo_path)

                    messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                    self.refresh_customers()
                    self.update_stats()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة العميل")
            else:
                # No photo, simple add
                if self.db.add_customer(result['name'], result['phone'], result.get('national_id')):
                    messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                    self.refresh_customers()
                    self.update_stats()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة العميل")

    def edit_customer(self):
        """Edit selected customer"""
        selected = self.customers_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        dialog = CustomerDialog(self.root, selected)
        result = dialog.show()

        if result:
            photo_path = result.get('photo_path')

            # Handle photo changes
            if photo_path and photo_path != selected.get('photo_path'):
                # New photo selected
                if photo_path != selected.get('photo_path'):  # Different from current
                    # Delete old photo if exists
                    if selected.get('photo_path'):
                        image_handler.delete_customer_photo(selected['photo_path'])

                    # Save new photo
                    new_photo_path = image_handler.save_customer_photo(photo_path, selected['id'])
                    photo_path = new_photo_path
            elif not photo_path and selected.get('photo_path'):
                # Photo was removed
                image_handler.delete_customer_photo(selected['photo_path'])
                photo_path = None
            else:
                # Keep existing photo
                photo_path = selected.get('photo_path')

            if self.db.update_customer(selected['id'], result['name'], result['phone'], result.get('national_id'), photo_path):
                messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
                self.refresh_customers()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث العميل")

    def delete_customer(self):
        """Delete selected customer"""
        selected = self.customers_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف العميل '{selected['name']}'؟"):
            if self.db.delete_customer(selected['id']):
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.refresh_customers()
                self.update_stats()
            else:
                messagebox.showerror("خطأ", "فشل في حذف العميل")

    def on_customer_double_click(self, customer_data):
        """Handle double-click on customer"""
        self.edit_customer()

    # Event handlers for rentals
    def new_rental(self):
        """Create new rental"""
        customers = self.db.get_customers()
        # Get all equipment but dialog will filter to available only
        all_equipment = self.db.get_equipment()
        available_equipment = [eq for eq in all_equipment if eq['status'] == 'Available']

        if not customers:
            messagebox.showwarning("تحذير", "لا يوجد عملاء. يرجى إضافة عميل أولاً")
            return

        if not available_equipment:
            messagebox.showwarning("تحذير", "لا توجد معدات متاحة للإيجار حالياً")
            return

        # Show availability summary
        total_equipment = len(all_equipment)
        available_count = len(available_equipment)
        rented_count = len([eq for eq in all_equipment if eq['status'] == 'Rented'])
        maintenance_count = len([eq for eq in all_equipment if eq['status'] == 'Maintenance'])

        availability_msg = f"المعدات المتاحة: {available_count} من أصل {total_equipment}\n"
        availability_msg += f"مؤجرة: {rented_count} | في الصيانة: {maintenance_count}"

        # Show availability info before opening dialog
        if messagebox.askyesno("معلومات التوفر", f"{availability_msg}\n\nهل تريد المتابعة لإنشاء إيجار جديد؟"):
            dialog = RentalDialog(self.root, customers, all_equipment)
            result = dialog.show()

            if result:
                if self.db.add_rental(result['customer_id'], result['equipment_id'],
                                    result['rental_date'], result['return_date'], result['price']):
                    messagebox.showinfo("نجح", "تم إنشاء الإيجار بنجاح")
                    self.refresh_all_data()
                else:
                    messagebox.showerror("خطأ", "فشل في إنشاء الإيجار")

    def new_multi_rental(self):
        """Create new multi rental"""
        messagebox.showinfo("قيد التطوير",
                           "ميزة الإيجار المتعدد متاحة في تطبيق الويب.\n\n"
                           "يرجى استخدام تطبيق الويب لإنشاء إيجارات متعددة.")

    def view_multi_rental_details(self):
        """View multi rental details"""
        selected = self.multi_rentals_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار إيجار متعدد لعرض التفاصيل")
            return

        # الحصول على تفاصيل الإيجار المتعدد
        rental_items = self.db.get_rental_items(selected['id'])

        if not rental_items:
            messagebox.showwarning("تحذير", "لا توجد تفاصيل لهذا الإيجار")
            return

        # إنشاء نافذة التفاصيل
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل الإيجار المتعدد #{selected['id']}")
        details_window.geometry("800x600")
        details_window.transient(self.root)
        details_window.grab_set()

        # معلومات العميل
        info_frame = ttk.LabelFrame(details_window, text="معلومات الإيجار")
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(info_frame, text=f"العميل: {selected['customer_name']}").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Label(info_frame, text=f"الهاتف: {selected['customer_phone']}").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Label(info_frame, text=f"تاريخ الإيجار: {selected['rental_date']}").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Label(info_frame, text=f"تاريخ الإرجاع: {selected['return_date']}").pack(anchor=tk.W, padx=5, pady=2)
        ttk.Label(info_frame, text=f"إجمالي السعر: {selected['total_price']} ج.م").pack(anchor=tk.W, padx=5, pady=2)

        # تفاصيل المعدات
        items_frame = ttk.LabelFrame(details_window, text="المعدات المؤجرة")
        items_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # جدول المعدات
        items_columns = [
            {'id': 'equipment_name', 'text': 'اسم المعدة', 'width': 150},
            {'id': 'equipment_model', 'text': 'الموديل', 'width': 120},
            {'id': 'equipment_category', 'text': 'الفئة', 'width': 100},
            {'id': 'rental_price', 'text': 'السعر اليومي', 'width': 100},
            {'id': 'days_count', 'text': 'عدد الأيام', 'width': 80},
            {'id': 'subtotal', 'text': 'المجموع الفرعي', 'width': 100},
            {'id': 'status', 'text': 'الحالة', 'width': 80}
        ]

        items_grid = DataGrid(items_frame, items_columns)
        items_grid.pack(fill=tk.BOTH, expand=True)

        # تحميل البيانات
        items_grid.load_data(rental_items)

        # زر الإغلاق
        ttk.Button(details_window, text="إغلاق", command=details_window.destroy).pack(pady=10)

    def return_multi_rental(self):
        """Return multi rental"""
        selected = self.multi_rentals_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار إيجار متعدد للإرجاع")
            return

        if selected['status'] != 'Active':
            messagebox.showwarning("تحذير", "هذا الإيجار غير نشط")
            return

        if messagebox.askyesno("تأكيد الإرجاع",
                              f"هل أنت متأكد من إرجاع جميع معدات الإيجار #{selected['id']}؟"):

            # الحصول على عناصر الإيجار
            rental_items = self.db.get_rental_items(selected['id'])

            # إعداد قائمة الإرجاع
            returned_items = []
            for item in rental_items:
                if item['status'] == 'Active':
                    returned_items.append({
                        'item_id': item['id'],
                        'condition': 'Good',
                        'notes': ''
                    })

            if returned_items:
                success = self.db.return_multi_rental(selected['id'], returned_items)
                if success:
                    messagebox.showinfo("نجح", "تم إرجاع جميع المعدات بنجاح")
                    self.refresh_all_data()
                else:
                    messagebox.showerror("خطأ", "فشل في إرجاع الإيجار المتعدد")
            else:
                messagebox.showinfo("معلومات", "جميع المعدات مُرجعة بالفعل")

    def on_multi_rental_double_click(self, rental_data):
        """Handle double-click on multi rental"""
        self.view_multi_rental_details()

    def return_equipment(self):
        """Process equipment return"""
        selected = self.active_rentals_grid.get_selected_item()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار إيجار لإرجاع المعدة")
            return

        dialog = ReturnDialog(self.root, selected)
        result = dialog.show()

        if result:
            if self.db.return_equipment(result['rental_id'], result['actual_return_date']):
                messagebox.showinfo("نجح", "تم إرجاع المعدة بنجاح")
                self.refresh_all_data()
            else:
                messagebox.showerror("خطأ", "فشل في إرجاع المعدة")

    def on_rental_double_click(self, rental_data):
        """Handle double-click on rental"""
        self.return_equipment()

    # Data refresh methods
    def refresh_equipment(self):
        """Refresh equipment data"""
        if hasattr(self, 'current_equipment_filter') and self.current_equipment_filter:
            equipment_data = self.db.get_equipment(category=self.current_equipment_filter)
        else:
            equipment_data = self.db.get_equipment()
        self.equipment_grid.load_data(equipment_data)

    def refresh_customers(self):
        """Refresh customers data"""
        customers_data = self.db.get_customers()
        self.customers_grid.load_data(customers_data)

    def refresh_active_rentals(self):
        """Refresh active rentals data"""
        active_rentals = self.db.get_rentals(status='Active')
        self.active_rentals_grid.load_data(active_rentals)

    def refresh_multi_rentals(self):
        """Refresh multi rentals data"""
        multi_rentals_data = self.db.get_multi_rentals()
        self.multi_rentals_grid.load_data(multi_rentals_data)

    def refresh_rental_history(self):
        """Refresh rental history data"""
        rental_history = self.db.get_rental_history()
        self.rental_history_grid.load_data(rental_history)

    def refresh_all_data(self):
        """Refresh all data grids"""
        self.refresh_equipment()
        self.refresh_customers()
        self.refresh_active_rentals()
        self.refresh_multi_rentals()
        self.refresh_rental_history()
        self.update_stats()

    def update_stats(self):
        """Update statistics on the new rental tab"""
        try:
            # Get statistics
            all_equipment = self.db.get_equipment()
            available_equipment = self.db.get_equipment(status='Available')
            rented_equipment = self.db.get_equipment(status='Rented')
            all_customers = self.db.get_customers()
            active_rentals = self.db.get_rentals(status='Active')

            # Get notification count
            notifications = self.db.get_return_notifications()
            pending_returns = notifications['total_count']

            # Update labels
            self.stats_labels['total_equipment'].config(text=str(len(all_equipment)))
            self.stats_labels['available_equipment'].config(text=str(len(available_equipment)))
            self.stats_labels['rented_equipment'].config(text=str(len(rented_equipment)))
            self.stats_labels['total_customers'].config(text=str(len(all_customers)))
            self.stats_labels['active_rentals'].config(text=str(len(active_rentals)))

            # Update notification count with color coding
            if pending_returns > 0:
                self.stats_labels['pending_returns'].config(text=str(pending_returns), foreground="red")
            else:
                self.stats_labels['pending_returns'].config(text="0", foreground="green")

        except Exception as e:
            print(f"Error updating stats: {e}")

    def update_overdue_status(self):
        """Update overdue rental status"""
        try:
            updated_count = self.db.auto_update_overdue_status()
            if updated_count > 0:
                messagebox.showinfo("تحديث", f"تم تحديث {updated_count} إيجار إلى حالة متأخر")
                self.refresh_all_data()
                # Refresh notifications if the tab exists
                if hasattr(self, 'notification_panel'):
                    self.notification_panel.refresh_notifications()
            else:
                messagebox.showinfo("تحديث", "لا توجد إيجارات تحتاج تحديث")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الحالات: {str(e)}")

    def create_sample_data(self):
        """Create sample data for testing"""
        if messagebox.askyesno("تأكيد", "هل تريد إنشاء بيانات تجريبية؟\nسيتم إضافة معدات وعملاء تجريبيين"):
            self.db.create_sample_data()
            self.refresh_all_data()
            messagebox.showinfo("نجح", "تم إنشاء البيانات التجريبية بنجاح")

    def show_detailed_reports(self):
        """Show detailed reports dialog"""
        dialog = ReportsDialog(self.root, self.db)
        dialog.show()

    def run(self):
        """Start the application"""
        self.root.mainloop()

def main():
    """Main function to start the application"""
    try:
        app = RentalManagementApp()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
