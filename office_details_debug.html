<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معدات الضوء الذهبي - تفاصيل المكتب</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        /* Sidebar Styles */
        #sidebar-wrapper {
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            transition: all 0.3s;
        }

        #sidebar-wrapper.toggled {
            width: 60px;
        }

        #sidebar-wrapper .sidebar-heading {
            padding: 1rem;
            font-size: 1.2rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        #sidebar-wrapper .list-group-item {
            border: none;
            padding: 0.75rem 1rem;
            transition: all 0.3s;
        }

        #sidebar-wrapper .list-group-item:hover {
            background-color: rgba(255,255,255,0.1) !important;
        }

        #sidebar-wrapper .list-group-item.active {
            background-color: rgba(255,255,255,0.2) !important;
        }

        /* Page Content */
        #page-content-wrapper {
            margin-right: 280px;
            transition: all 0.3s;
        }

        #page-content-wrapper.toggled {
            margin-right: 60px;
        }

        /* Top Header */
        .top-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #dee2e6;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            #sidebar-wrapper {
                width: 0;
                overflow: hidden;
                z-index: 1050;
            }

            #sidebar-wrapper.toggled {
                width: 280px;
                position: fixed;
                top: 0;
                right: 0;
                height: 100vh;
                box-shadow: -2px 0 5px rgba(0,0,0,0.1);
            }

            #page-content-wrapper {
                margin-right: 0;
                width: 100%;
            }

            #page-content-wrapper.toggled {
                margin-right: 0;
            }

            /* تحسين الجداول على الشاشات الصغيرة */
            .table-responsive {
                font-size: 0.85rem;
            }

            .table th, .table td {
                padding: 0.5rem 0.25rem;
                white-space: nowrap;
            }

            /* تحسين البطاقات على الشاشات الصغيرة */
            .card {
                margin-bottom: 1rem;
            }

            .btn-group {
                flex-direction: column;
            }

            .btn-group .btn {
                margin-bottom: 0.25rem;
            }
        }

        /* تحسينات للأجهزة اللوحية */
        @media (min-width: 769px) and (max-width: 1024px) {
            #sidebar-wrapper {
                width: 220px;
            }

            #page-content-wrapper {
                margin-right: 220px;
            }

            .table th, .table td {
                padding: 0.6rem 0.4rem;
            }
        }

        /* تحسينات للشاشات الكبيرة */
        @media (min-width: 1200px) {
            .container-fluid {
                max-width: 1400px;
                margin: 0 auto;
            }
        }

        /* تحسين النصوص العربية على الشاشات الصغيرة */
        @media (max-width: 576px) {
            h1, h2, h3 {
                font-size: 1.2rem;
            }

            .btn {
                font-size: 0.85rem;
                padding: 0.4rem 0.8rem;
            }

            .badge {
                font-size: 0.7rem;
            }

            /* إخفاء بعض الأعمدة في الجداول على الشاشات الصغيرة جداً */
            .table .d-none-mobile {
                display: none !important;
            }
        }

        /* Submenu Styles */
        .submenu {
            background-color: rgba(0,0,0,0.1);
            border-radius: 5px;
            margin: 0.5rem 0;
        }

        .submenu .list-group-item {
            padding: 0.5rem 1rem 0.5rem 2rem;
            font-size: 0.9rem;
        }

        /* تحسينات القائمة الجانبية الجديدة */
        .inventory-section {
            margin: 10px 0;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .inventory-section:hover {
            background: rgba(255,255,255,0.05) !important;
        }

        .inventory-header {
            padding: 12px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .inventory-header:hover {
            background: rgba(255,255,255,0.1);
        }

        .transition-icon {
            transition: transform 0.3s ease;
        }

        .inventory-submenu.show .transition-icon {
            transform: rotate(180deg);
        }

        .inventory-submenu {
            background: rgba(0,0,0,0.15);
            border-radius: 0 0 10px 10px;
            margin-top: 5px;
            padding: 10px 0;
        }

        .submenu-item {
            padding: 10px 20px 10px 40px !important;
            margin: 2px 10px;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .submenu-item:hover {
            background: rgba(255,255,255,0.15) !important;
            transform: translateX(5px);
        }

        .submenu-item.current-page {
            background: rgba(255,255,255,0.25) !important;
            border-left: 3px solid #ffc107 !important;
        }

        .submenu-item-small {
            padding: 6px 20px 6px 50px !important;
            margin: 1px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .submenu-item-small:hover {
            background: rgba(255,255,255,0.1) !important;
        }

        .submenu-divider {
            height: 1px;
            background: rgba(255,255,255,0.1);
            margin: 10px 20px;
        }

        .submenu-quick-links {
            margin-top: 10px;
        }

        .badge {
            font-size: 0.7rem;
            padding: 2px 6px;
        }

        /* تأثيرات إضافية */
        .inventory-section .fas {
            transition: all 0.3s ease;
        }

        .inventory-section:hover .fas {
            transform: scale(1.1);
        }

        .submenu-item::before {
            content: '';
            position: absolute;
            left: 25px;
            top: 50%;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translateY(-50%);
            transition: all 0.3s ease;
        }

        .submenu-item:hover::before {
            background: #ffc107;
            transform: translateY(-50%) scale(1.5);
        }

        /* User Info at Bottom */
        .user-info {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 1rem;
            border-top: 1px solid rgba(255,255,255,0.1);
            background-color: rgba(0,0,0,0.1);
        }

        /* Animation for collapsible menus */
        .collapse {
            transition: all 0.3s ease;
        }

        /* Active page indicator */
        .list-group-item.current-page {
            background-color: rgba(255,255,255,0.2) !important;
            border-right: 4px solid #fff;
        }
    </style>
    
    
</head>
<body>
    
    <!-- New Sidebar Layout -->
    <div class="d-flex" id="wrapper">
        <!-- Sidebar -->
        <div class="bg-primary text-white" id="sidebar-wrapper">
            <div class="sidebar-heading text-center py-3 fw-bold border-bottom">
                <i class="fas fa-camera me-2"></i>
                <span class="sidebar-text">نظام إدارة المعدات</span>
            </div>
            
            <div class="list-group list-group-flush my-3">
                <!-- الرئيسية -->
                <a href="/dashboard" class="list-group-item list-group-item-action bg-transparent text-white ">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    <span class="sidebar-text">لوحة التحكم</span>
                </a>
                
                <!-- الإيجارات -->
                <div class="list-group-item bg-transparent text-white">
                    <div class="d-flex align-items-center" data-bs-toggle="collapse" data-bs-target="#rentalsSubmenu" style="cursor: pointer;">
                        <i class="fas fa-handshake me-3"></i>
                        <span class="sidebar-text">الإيجارات</span>
                        <i class="fas fa-chevron-down ms-auto sidebar-text"></i>
                    </div>
                    <div class="collapse submenu" id="rentalsSubmenu">
                        <a href="/rentals" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-list me-2"></i>
                            <span class="sidebar-text">إدارة الإيجارات</span>
                        </a>
                        <a href="/new_rental" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-plus-circle me-2 text-success"></i>
                            <span class="sidebar-text">إنشاء إيجار جديد</span>
                        </a>
                        <a href="/return_equipment" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-undo me-2 text-warning"></i>
                            <span class="sidebar-text">إرجاع المعدات</span>
                        </a>
                        
                        <a href="/reports" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-file-invoice me-2"></i>
                            <span class="sidebar-text">الفواتير والتقارير</span>
                        </a>
                        
                    </div>
                </div>
                
                <!-- 📦 المخزون -->
                <div class="list-group-item bg-transparent text-white inventory-section">
                    <div class="d-flex align-items-center inventory-header" data-bs-toggle="collapse" data-bs-target="#inventorySubmenu" style="cursor: pointer;">
                        <i class="fas fa-boxes me-3 text-warning"></i>
                        <span class="sidebar-text fw-bold">📦 المخزون</span>
                        <i class="fas fa-chevron-down ms-auto sidebar-text transition-icon"></i>
                    </div>
                    <div class="collapse submenu inventory-submenu" id="inventorySubmenu">
                        <!-- عرض جميع المعدات -->
                        <a href="/inventory" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 submenu-item ">
                            <i class="fas fa-th-large me-2 text-primary"></i>
                            <span class="sidebar-text">عرض جميع المعدات</span>
                            <small class="badge bg-primary ms-auto">الكل</small>
                        </a>

                        <!-- إضافة معدة جديدة -->
                        <a href="/add_equipment_enhanced" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 submenu-item ">
                            <i class="fas fa-plus-circle me-2 text-success"></i>
                            <span class="sidebar-text">إضافة معدة جديدة</span>
                            <small class="text-success ms-auto">+</small>
                        </a>

                        <!-- المعدات المفقودة -->
                        <a href="/lost_equipment" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 submenu-item ">
                            <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                            <span class="sidebar-text">المعدات المفقودة</span>
                            <small class="badge bg-danger ms-auto">!</small>
                        </a>

                        <!-- المعدات المستقطبة من الخارج -->
                        <a href="/external_equipment" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 submenu-item ">
                            <i class="fas fa-building me-2 text-info"></i>
                            <span class="sidebar-text">المعدات المستقطبة من الخارج</span>
                            <small class="badge bg-info ms-auto">خارجي</small>
                        </a>

                        <!-- استقطاب معدات -->
                        <a href="/offices" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 submenu-item ">
                            <i class="fas fa-handshake me-2 text-warning"></i>
                            <span class="sidebar-text">استقطاب معدات</span>
                            <small class="text-warning ms-auto">📋</small>
                        </a>

                        <!-- فاصل -->
                        <div class="submenu-divider"></div>

                        <!-- روابط سريعة -->
                        <div class="submenu-quick-links">
                            <small class="text-muted px-3 py-1 d-block">روابط سريعة</small>
                            <a href="/equipment" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-1 submenu-item-small ">
                                <i class="fas fa-camera me-2 text-secondary"></i>
                                <span class="sidebar-text small">المعدات الرئيسية</span>
                            </a>
                            <a href="/inventory_items" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-1 submenu-item-small ">
                                <i class="fas fa-list me-2 text-secondary"></i>
                                <span class="sidebar-text small">عناصر المخزون</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- العملاء -->
                <a href="/customers" class="list-group-item list-group-item-action bg-transparent text-white ">
                    <i class="fas fa-users me-3"></i>
                    <span class="sidebar-text">العملاء</span>
                </a>

                <!-- المكاتب -->
                <a href="/offices" class="list-group-item list-group-item-action bg-transparent text-white current-page">
                    <i class="fas fa-building me-3"></i>
                    <span class="sidebar-text">المكاتب</span>
                </a>

                <!-- التقارير -->
                
                <div class="list-group-item bg-transparent text-white">
                    <div class="d-flex align-items-center" data-bs-toggle="collapse" data-bs-target="#reportsSubmenu" style="cursor: pointer;">
                        <i class="fas fa-chart-bar me-3"></i>
                        <span class="sidebar-text">التقارير</span>
                        <i class="fas fa-chevron-down ms-auto sidebar-text"></i>
                    </div>
                    <div class="collapse submenu" id="reportsSubmenu">
                        <a href="/rental_reports" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-chart-line me-2"></i>
                            <span class="sidebar-text">تقارير الإيجارات الشاملة</span>
                        </a>
                        <a href="/reports" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-chart-area me-2"></i>
                            <span class="sidebar-text">التقارير العامة</span>
                        </a>
                        <a href="/equipment_reports" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-chart-bar me-2"></i>
                            <span class="sidebar-text">تقارير المعدات المفصلة</span>
                        </a>
                    </div>
                </div>
                
                
                
                <!-- الإدارة -->
                <div class="list-group-item bg-transparent text-white">
                    <div class="d-flex align-items-center" data-bs-toggle="collapse" data-bs-target="#adminSubmenu" style="cursor: pointer;">
                        <i class="fas fa-cog me-3"></i>
                        <span class="sidebar-text">الإدارة</span>
                        <i class="fas fa-chevron-down ms-auto sidebar-text"></i>
                    </div>
                    <div class="collapse submenu" id="adminSubmenu">
                        <a href="/admin_users" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-users-cog me-2"></i>
                            <span class="sidebar-text">إدارة المستخدمين</span>
                        </a>
                        <a href="/company_settings_page" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-building me-2"></i>
                            <span class="sidebar-text">إعدادات الشركة</span>
                        </a>
                        <a href="/manage_permissions" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-user-shield me-2"></i>
                            <span class="sidebar-text">إدارة الصلاحيات المتقدمة</span>
                        </a>
                        <a href="/backup_page" class="list-group-item list-group-item-action bg-transparent text-white border-0 py-2 ">
                            <i class="fas fa-shield-alt me-2"></i>
                            <span class="sidebar-text">النسخ الاحتياطي</span>
                        </a>
                    </div>
                </div>
                
            </div>
            
            <!-- User Info at Bottom -->
            <div class="user-info">
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle w-100 text-start" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-2"></i>
                        <span class="sidebar-text">
                            مدير النظام الرئيسي<br>
                            <small>مدير النظام</small>
                        </span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="printEquipmentList()">
                            <i class="fas fa-print me-2"></i>طباعة قائمة المعدات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Page Content -->
        <div id="page-content-wrapper" class="flex-fill">
            <!-- Top Header -->
            <nav class="top-header d-flex justify-content-between align-items-center">
                <div>
                    <button class="btn btn-outline-light btn-sm" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span class="ms-3 fw-bold">لوحة التحكم</span>
                </div>
                
                <div class="d-flex align-items-center">
                    <span class="me-3">
                        <i class="fas fa-clock me-1"></i>
                        <span id="currentDateTime"></span>
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-circle text-success me-1"></i>متصل
                    </span>
                </div>
            </nav>

            <!-- Main Content Area -->
            <div class="container-fluid p-4">
                <!-- Flash Messages -->
                
                    
                

                <!-- Page Content -->
                
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/offices">المكاتب</a></li>
                            <li class="breadcrumb-item active">معدات الضوء الذهبي</li>
                        </ol>
                    </nav>
                    <h2 class="text-primary mb-1">
                        <i class="fas fa-building me-2"></i>
                        معدات الضوء الذهبي
                    </h2>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="showPaymentModal()">
                        <i class="fas fa-money-bill me-2"></i>
                        إضافة دفعة
                    </button>
                    <a href="/add_equipment_enhanced?office_id=3" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة معدة
                    </a>
                </div>
            </div>

            <!-- Office Info Card -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات المكتب</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الهاتف:</strong> 01012345678</p>
                                    <p><strong>العنوان:</strong> الإسكندرية - سموحة، طريق الحرية</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>تاريخ الإضافة:</strong> </p>
                                    <p><strong>آخر تحديث:</strong> </p>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <strong>ملاحظات:</strong>
                                <p class="text-muted">متخصص في معدات الإضاءة والصوت</p>
                            </div>
                            
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">الملخص المالي</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-12 mb-3">
                                    <h4 class="text-danger">0.0 ج.م</h4>
                                    <small class="text-muted">إجمالي المستحقات</small>
                                </div>
                                <div class="col-12 mb-3">
                                    <h4 class="text-success">0.0 ج.م</h4>
                                    <small class="text-muted">إجمالي المدفوع</small>
                                </div>
                                <div class="col-12">
                                    
                                    <h4 class="text-success">
                                        0.0 ج.م
                                    </h4>
                                    <small class="text-muted">الرصيد المتبقي</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Equipment Section -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">المعدات المستقطبة (1)</h5>
                    <div>
                        <span class="badge bg-success me-2">
                            متاح: 0
                        </span>
                        <span class="badge bg-warning">
                            مؤجر: 0
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    
                    <div class="row">
                        
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 border-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0"></h6>
                                        <span class="badge bg-warning">
                                            مؤجر
                                        </span>
                                    </div>
                                    <p class="card-text">
                                        <small class="text-muted"></small><br>
                                        <strong>الموديل:</strong> <br>
                                        <strong>الرقم التسلسلي:</strong> <br>
                                        <strong class="text-primary"> ج.م/يوم</strong>
                                    </p>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-warning" onclick="returnEquipment(1, '')" title="إرجاع المعدة">
                                            <i class="fas fa-undo me-1"></i>
                                            إرجاع
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                </div>
            </div>

            <!-- Transactions Section -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">كشف الحساب (0)</h5>
                </div>
                <div class="card-body">
                    
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد معاملات مالية</h6>
                        <button class="btn btn-success mt-2" onclick="showPaymentModal()">
                            إضافة دفعة جديدة
                        </button>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة معاملة مالية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="paymentForm">
                    <div class="mb-3">
                        <label class="form-label">نوع المعاملة</label>
                        <select class="form-select" id="transactionType" required>
                            <option value="payment">دفعة (تقليل الرصيد المستحق)</option>
                            <option value="charge">رسوم إضافية (زيادة الرصيد المستحق)</option>
                            <option value="adjustment">تعديل الرصيد</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">المبلغ (ج.م)</label>
                        <input type="number" class="form-control" id="transactionAmount" step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="transactionDescription" rows="3" placeholder="وصف المعاملة..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="addTransaction()">إضافة المعاملة</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Equipment Modal -->
<div class="modal fade" id="addEquipmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة معدة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addEquipmentForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المعدة</label>
                            <input type="text" class="form-control" id="equipmentName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الفئة</label>
                            <select class="form-select" id="equipmentCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="كاميرات">كاميرات</option>
                                <option value="عدسات">عدسات</option>
                                <option value="بطاريات">بطاريات</option>
                                <option value="كابلات">كابلات</option>
                                <option value="حوامل">حوامل</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الموديل</label>
                            <input type="text" class="form-control" id="equipmentModel">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الرقم التسلسلي</label>
                            <input type="text" class="form-control" id="equipmentSerial">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">السعر اليومي (ج.م)</label>
                            <input type="number" class="form-control" id="equipmentPrice" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" id="equipmentStatus">
                                <option value="Available">متاح</option>
                                <option value="Maintenance">صيانة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="equipmentDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addEquipment()">إضافة المعدة</button>
            </div>
        </div>
    </div>
</div>

<script>
    const officeId = 3;

    // عرض نافذة الدفع
    function showPaymentModal() {
        const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
        paymentModal.show();
    }

    // عرض نافذة إضافة معدة
    function showAddEquipmentModal() {
        const equipmentModal = new bootstrap.Modal(document.getElementById('addEquipmentModal'));
        equipmentModal.show();
    }

    // إضافة معاملة مالية
    function addTransaction() {
        const data = {
            office_id: officeId,
            transaction_type: document.getElementById('transactionType').value,
            amount: document.getElementById('transactionAmount').value,
            description: document.getElementById('transactionDescription').value
        };
        
        if (!data.amount || data.amount <= 0) {
            alert('يرجى إدخال مبلغ صحيح');
            return;
        }
        
        fetch('/api/add-office-transaction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('تم إضافة المعاملة بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }

    // إضافة معدة جديدة
    function addEquipment() {
        const data = {
            office_id: officeId,
            name: document.getElementById('equipmentName').value,
            category: document.getElementById('equipmentCategory').value,
            model: document.getElementById('equipmentModel').value,
            serial_number: document.getElementById('equipmentSerial').value,
            price: document.getElementById('equipmentPrice').value,
            status: document.getElementById('equipmentStatus').value,
            description: document.getElementById('equipmentDescription').value
        };

        if (!data.name || !data.category || !data.price) {
            alert('يرجى ملء الحقول المطلوبة');
            return;
        }

        fetch('/api/add-office-equipment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('تم إضافة المعدة بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }

    // إرجاع معدة
    function returnEquipment(equipmentId, equipmentName) {
        if (!confirm(`هل أنت متأكد من إرجاع المعدة "${equipmentName}"؟\nسيتم إزالة ربطها بهذا المكتب وإعادتها للمخزون الرئيسي.`)) {
            return;
        }

        const notes = prompt('ملاحظات الإرجاع (اختياري):') || '';

        const data = {
            equipment_id: equipmentId,
            notes: notes
        };

        fetch('/api/return-office-equipment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('تم إرجاع المعدة بنجاح');
                location.reload();
            } else {
                alert('خطأ: ' + result.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
    }
</script>

            </div>
        </div>
    </div>
    

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Sidebar Toggle
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('sidebar-wrapper').classList.toggle('toggled');
            document.getElementById('page-content-wrapper').classList.toggle('toggled');
        });

        // Update Current Date Time
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            };
            const timeString = now.toLocaleDateString('ar-EG', options);
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = timeString;
            }
        }

        // Update time every minute
        setInterval(updateDateTime, 60000);
        updateDateTime();

        // Auto-expand current menu
        document.addEventListener('DOMContentLoaded', function() {
            // Find current page and expand its parent menu
            const currentPageItem = document.querySelector('.current-page');
            if (currentPageItem) {
                const parentCollapse = currentPageItem.closest('.collapse');
                if (parentCollapse) {
                    parentCollapse.classList.add('show');
                }
            }

            // Enhanced inventory menu interactions
            initializeInventoryMenu();
        });

        // تحسينات القائمة الجانبية
        function initializeInventoryMenu() {
            const inventoryHeader = document.querySelector('.inventory-header');
            const inventorySubmenu = document.querySelector('.inventory-submenu');
            const transitionIcon = document.querySelector('.transition-icon');

            if (inventoryHeader && inventorySubmenu) {
                // إضافة تأثيرات عند فتح/إغلاق القائمة
                inventorySubmenu.addEventListener('show.bs.collapse', function() {
                    if (transitionIcon) {
                        transitionIcon.style.transform = 'rotate(180deg)';
                    }
                    inventoryHeader.style.background = 'rgba(255,255,255,0.15)';
                });

                inventorySubmenu.addEventListener('hide.bs.collapse', function() {
                    if (transitionIcon) {
                        transitionIcon.style.transform = 'rotate(0deg)';
                    }
                    inventoryHeader.style.background = '';
                });

                // إضافة تأثيرات للعناصر الفرعية
                const submenuItems = document.querySelectorAll('.submenu-item');
                submenuItems.forEach((item, index) => {
                    item.style.animationDelay = `${index * 0.1}s`;

                    item.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateX(5px)';
                        this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                    });

                    item.addEventListener('mouseleave', function() {
                        this.style.transform = 'translateX(0)';
                        this.style.boxShadow = '';
                    });
                });

                // تأثير النقر على العناصر
                submenuItems.forEach(item => {
                    item.addEventListener('click', function(e) {
                        // إضافة تأثير النقر
                        this.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            this.style.transform = '';
                        }, 150);
                    });
                });
            }

            // إضافة عدادات ديناميكية للشارات
            updateInventoryBadges();
        }

        // تحديث عدادات القائمة الجانبية
        function updateInventoryBadges() {
            // يمكن إضافة استدعاءات AJAX هنا لتحديث العدادات
            // مثل عدد المعدات المفقودة، المعدات الخارجية، إلخ
        }

        // Alert function for notifications
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const container = document.querySelector('.container-fluid');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);

                // Auto-dismiss after 5 seconds
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
        }

        // Print Equipment List Function
        function printEquipmentList() {
            window.open('/equipment/print', '_blank');
        }

        // Invoice Functions
        function printRentalInvoice(rentalId) {
            window.open(`/rental/${rentalId}/invoice`, '_blank');
        }

        function printMultiRentalInvoice(rentalGroupId) {
            window.open(`/multi-rental/${rentalGroupId}/invoice`, '_blank');
        }

        // إظهار modal اختيار الإيجار للإرجاع
        function showReturnEquipmentModal() {
            // إنشاء modal
            const modalHtml = `
                <div class="modal fade" id="returnEquipmentModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-warning text-dark">
                                <h5 class="modal-title">
                                    <i class="fas fa-undo me-2"></i>
                                    اختيار إيجار للإرجاع
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">رقم الإيجار:</label>
                                    <input type="number" class="form-control" id="rentalIdInput"
                                           placeholder="أدخل رقم الإيجار" min="1">
                                </div>
                                <div class="text-muted">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        يمكنك العثور على رقم الإيجار في صفحة إدارة الإيجارات
                                    </small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-warning" onclick="goToReturnPage()">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    الانتقال لصفحة الإرجاع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة modal موجود مسبقاً
            const existingModal = document.getElementById('returnEquipmentModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة modal جديد
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // إظهار modal
            const modal = new bootstrap.Modal(document.getElementById('returnEquipmentModal'));
            modal.show();

            // التركيز على حقل الإدخال
            setTimeout(() => {
                document.getElementById('rentalIdInput').focus();
            }, 500);

            // السماح بالضغط على Enter
            document.getElementById('rentalIdInput').addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    goToReturnPage();
                }
            });
        }

        // الانتقال لصفحة الإرجاع
        function goToReturnPage() {
            const rentalId = document.getElementById('rentalIdInput').value;

            if (!rentalId || rentalId < 1) {
                alert('يرجى إدخال رقم إيجار صحيح');
                return;
            }

            // إغلاق modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('returnEquipmentModal'));
            modal.hide();

            // الانتقال لصفحة الإرجاع
            window.location.href = `/return-equipment/${rentalId}`;
        }
    </script>

    
</body>
</html>