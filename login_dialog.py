#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة تسجيل الدخول ونظام المستخدمين
Login Dialog and User Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
from typing import Optional, Dict

from user_manager import UserManager, UserRole, Permission
from permissions_dialog import EmployeePermissionsDialog, PermissionsSummaryDialog

class LoginDialog:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, parent=None, user_manager=None):
        """تهيئة نافذة تسجيل الدخول"""
        self.parent = parent
        self.user_manager = user_manager if user_manager else UserManager()
        self.result = None
        
        # إنشاء النافذة
        if parent:
            self.dialog = tk.Toplevel(parent)
            self.dialog.transient(parent)
            self.dialog.grab_set()
        else:
            self.dialog = tk.Tk()

        self.dialog.title("تسجيل الدخول - نظام إدارة تأجير الكاميرات")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)

        # جعل النافذة في المقدمة دائماً
        self.dialog.attributes('-topmost', True)
        self.dialog.lift()
        self.dialog.focus_force()

        # إجبار النافذة على الظهور
        self.dialog.update()
        self.dialog.deiconify()

        # توسيط النافذة
        self.center_window()

        # التأكد من ظهور النافذة
        self.dialog.after(100, lambda: self.dialog.lift())
        self.dialog.after(200, lambda: self.dialog.focus_force())
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=11)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=12, weight="bold")
            self.title_font = font.Font(family="Arial Unicode MS", size=16, weight="bold")
        except:
            self.arabic_font = font.Font(family="Arial", size=11)
            self.arabic_font_bold = font.Font(family="Arial", size=12, weight="bold")
            self.title_font = font.Font(family="Arial", size=16, weight="bold")
        
        # إنشاء الواجهة
        self.create_interface()
        
        # ربط مفاتيح الاختصار
        self.dialog.bind('<Return>', lambda e: self.login())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
        # التركيز على حقل اسم المستخدم
        self.dialog.after(100, lambda: self.username_entry.focus_set())
        self.dialog.after(200, lambda: self.username_entry.select_range(0, tk.END))
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة تسجيل الدخول"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="تسجيل الدخول", 
                               font=self.title_font)
        title_label.pack(pady=(0, 20))
        
        # معلومات النظام
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        system_info = """نظام إدارة تأجير الكاميرات والعدسات
الإصدار 2.0 مع نظام المستخدمين"""
        
        ttk.Label(info_frame, text=system_info, font=self.arabic_font,
                 justify=tk.CENTER).pack()
        
        # نموذج تسجيل الدخول
        form_frame = ttk.LabelFrame(main_frame, text="بيانات الدخول", padding="15")
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # اسم المستخدم
        ttk.Label(form_frame, text="اسم المستخدم:", 
                 font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(form_frame, textvariable=self.username_var,
                                       font=self.arabic_font, width=25)
        self.username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # كلمة المرور
        ttk.Label(form_frame, text="كلمة المرور:", 
                 font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(form_frame, textvariable=self.password_var,
                                       font=self.arabic_font, show="*", width=25)
        self.password_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        
        # تكوين الأعمدة
        form_frame.columnconfigure(1, weight=1)
        
        # رسالة الحالة
        self.status_label = ttk.Label(main_frame, text="", 
                                     font=self.arabic_font, foreground="red")
        self.status_label.pack(pady=5)
        
        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.login_button = ttk.Button(button_frame, text="دخول", 
                                      command=self.login)
        self.login_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Button(button_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.RIGHT)
        
        # معلومات المستخدم الافتراضي
        default_info_frame = ttk.Frame(main_frame)
        default_info_frame.pack(fill=tk.X, pady=(20, 0))
        
        default_text = """المستخدم الافتراضي:
اسم المستخدم: admin
كلمة المرور: admin123"""
        
        ttk.Label(default_info_frame, text=default_text, 
                 font=self.arabic_font, foreground="gray",
                 justify=tk.CENTER).pack()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        if not username:
            self.show_status("يرجى إدخال اسم المستخدم", "red")
            self.username_entry.focus()
            return
        
        if not password:
            self.show_status("يرجى إدخال كلمة المرور", "red")
            self.password_entry.focus()
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.set_buttons_state(False)
        self.show_status("جاري التحقق...", "blue")
        
        # تسجيل الدخول مباشرة (بدون threading لتجنب مشاكل tkinter)
        try:
            success, message, user_data = self.user_manager.authenticate(username, password)
            self.on_login_complete(success, message, user_data)
        except Exception as e:
            self.show_status(f"خطأ في تسجيل الدخول: {str(e)}", "red")
            self.set_buttons_state(True)
    
    def on_login_complete(self, success: bool, message: str, user_data: Optional[Dict]):
        """معالج اكتمال تسجيل الدخول"""
        self.set_buttons_state(True)

        if success:
            self.show_status("تم تسجيل الدخول بنجاح", "green")
            self.result = user_data
            # إزالة خاصية البقاء في المقدمة
            self.dialog.attributes('-topmost', False)
            # إغلاق النافذة فوراً بدلاً من الانتظار
            self.dialog.destroy()
        else:
            self.show_status(message, "red")
            self.password_var.set("")  # مسح كلمة المرور
            self.password_entry.focus()
    
    def cancel(self):
        """إلغاء تسجيل الدخول"""
        self.result = None
        self.dialog.destroy()
    
    def show_status(self, message: str, color: str = "black"):
        """عرض رسالة الحالة"""
        self.status_label.config(text=message, foreground=color)
    
    def set_buttons_state(self, enabled: bool):
        """تعيين حالة الأزرار"""
        state = tk.NORMAL if enabled else tk.DISABLED
        self.login_button.config(state=state)
        self.username_entry.config(state=state)
        self.password_entry.config(state=state)
    
    def show(self) -> Optional[Dict]:
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class UserManagementDialog:
    """نافذة إدارة المستخدمين"""
    
    def __init__(self, parent, user_manager: UserManager):
        """تهيئة نافذة إدارة المستخدمين"""
        self.parent = parent
        self.user_manager = user_manager
        
        # التحقق من الصلاحية
        if not self.user_manager.has_permission(Permission.USERS_VIEW):
            messagebox.showerror("خطأ", "ليس لديك صلاحية عرض المستخدمين")
            return
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إدارة المستخدمين")
        self.dialog.geometry("900x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{parent.winfo_rootx() + 50}+{parent.winfo_rooty() + 50}")
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحديث البيانات
        self.refresh_users()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المستخدمين"""
        # إنشاء دفتر التبويبات
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب المستخدمين
        self.create_users_tab()
        
        # تبويب سجل النشاطات
        self.create_activity_log_tab()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        users_frame = ttk.Frame(self.notebook)
        self.notebook.add(users_frame, text="المستخدمين")
        
        # شريط الأدوات
        toolbar = ttk.Frame(users_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        if self.user_manager.has_permission(Permission.USERS_ADD):
            ttk.Button(toolbar, text="إضافة مستخدم", 
                      command=self.add_user).pack(side=tk.LEFT, padx=(0, 5))
        
        if self.user_manager.has_permission(Permission.USERS_EDIT):
            ttk.Button(toolbar, text="تعديل", 
                      command=self.edit_user).pack(side=tk.LEFT, padx=(0, 5))
        
        if self.user_manager.has_permission(Permission.USERS_DELETE):
            ttk.Button(toolbar, text="حذف",
                      command=self.delete_user).pack(side=tk.LEFT, padx=(0, 5))

        if self.user_manager.has_permission(Permission.USERS_EDIT):
            ttk.Button(toolbar, text="إدارة الصلاحيات",
                      command=self.manage_permissions).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar, text="تحديث",
                  command=self.refresh_users).pack(side=tk.RIGHT)
        
        # قائمة المستخدمين
        columns = ("id", "username", "full_name", "role", "email", "status", "last_login")
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show="headings")
        
        # تكوين الأعمدة
        self.users_tree.heading("id", text="المعرف")
        self.users_tree.heading("username", text="اسم المستخدم")
        self.users_tree.heading("full_name", text="الاسم الكامل")
        self.users_tree.heading("role", text="الدور")
        self.users_tree.heading("email", text="البريد الإلكتروني")
        self.users_tree.heading("status", text="الحالة")
        self.users_tree.heading("last_login", text="آخر دخول")
        
        self.users_tree.column("id", width=60)
        self.users_tree.column("username", width=120)
        self.users_tree.column("full_name", width=150)
        self.users_tree.column("role", width=100)
        self.users_tree.column("email", width=180)
        self.users_tree.column("status", width=80)
        self.users_tree.column("last_login", width=150)
        
        # شريط التمرير
        users_scrollbar = ttk.Scrollbar(users_frame, orient=tk.VERTICAL, 
                                       command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)
        
        self.users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        users_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def create_activity_log_tab(self):
        """إنشاء تبويب سجل النشاطات"""
        activity_frame = ttk.Frame(self.notebook)
        self.notebook.add(activity_frame, text="سجل النشاطات")
        
        # شريط الأدوات
        toolbar = ttk.Frame(activity_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar, text="تحديث", 
                  command=self.refresh_activity_log).pack(side=tk.RIGHT)
        
        # قائمة النشاطات
        columns = ("timestamp", "username", "action", "details")
        self.activity_tree = ttk.Treeview(activity_frame, columns=columns, show="headings")
        
        # تكوين الأعمدة
        self.activity_tree.heading("timestamp", text="الوقت")
        self.activity_tree.heading("username", text="المستخدم")
        self.activity_tree.heading("action", text="النشاط")
        self.activity_tree.heading("details", text="التفاصيل")
        
        self.activity_tree.column("timestamp", width=150)
        self.activity_tree.column("username", width=120)
        self.activity_tree.column("action", width=120)
        self.activity_tree.column("details", width=300)
        
        # شريط التمرير
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient=tk.VERTICAL, 
                                          command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        self.activity_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        activity_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # تحديث سجل النشاطات
        self.refresh_activity_log()
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="إغلاق",
                  command=self.dialog.destroy).pack(side=tk.RIGHT)

    def refresh_users(self):
        """تحديث قائمة المستخدمين"""
        try:
            # مسح القائمة الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # إضافة المستخدمين
            users = self.user_manager.get_users()

            for user in users:
                # تنسيق الدور
                role_display = self.user_manager.get_role_display_name(UserRole(user['role']))

                # تنسيق الحالة
                status = "نشط" if user['is_active'] else "معطل"

                # تنسيق آخر دخول
                last_login = user['last_login'] if user['last_login'] else "لم يسجل دخول"

                self.users_tree.insert("", tk.END, values=(
                    user['id'],
                    user['username'],
                    user['full_name'],
                    role_display,
                    user['email'] or "",
                    status,
                    last_login
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث قائمة المستخدمين: {str(e)}")

    def refresh_activity_log(self):
        """تحديث سجل النشاطات"""
        try:
            # مسح القائمة الحالية
            for item in self.activity_tree.get_children():
                self.activity_tree.delete(item)

            # إضافة النشاطات
            activities = self.user_manager.get_user_activity_log(limit=200)

            for activity in activities:
                self.activity_tree.insert("", tk.END, values=(
                    activity['timestamp'],
                    activity['username'],
                    activity['action'],
                    activity['details']
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث سجل النشاطات: {str(e)}")

    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = AddUserDialog(self.dialog, self.user_manager)
        result = dialog.show()

        if result:
            self.refresh_users()

    def edit_user(self):
        """تعديل مستخدم محدد"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]

        # الحصول على بيانات المستخدم
        users = self.user_manager.get_users()
        selected_user = None

        for user in users:
            if user['id'] == user_id:
                selected_user = user
                break

        if selected_user:
            dialog = EditUserDialog(self.dialog, self.user_manager, selected_user)
            result = dialog.show()

            if result:
                self.refresh_users()

    def delete_user(self):
        """حذف مستخدم محدد"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف المستخدم '{username}'؟"):
            success, message = self.user_manager.delete_user(user_id)

            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_users()
            else:
                messagebox.showerror("خطأ", message)

    def manage_permissions(self):
        """إدارة صلاحيات الموظفين"""
        dialog = EmployeePermissionsDialog(self.dialog, self.user_manager)
        dialog.show()

    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()

class AddUserDialog:
    """نافذة إضافة مستخدم جديد"""

    def __init__(self, parent, user_manager: UserManager):
        self.parent = parent
        self.user_manager = user_manager
        self.result = False

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة مستخدم جديد")
        self.dialog.geometry("400x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry(f"+{parent.winfo_rootx() + 100}+{parent.winfo_rooty() + 100}")

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء الواجهة
        self.create_interface()

    def create_interface(self):
        """إنشاء واجهة إضافة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # نموذج البيانات
        form_frame = ttk.LabelFrame(main_frame, text="بيانات المستخدم", padding="15")
        form_frame.pack(fill=tk.X, pady=(0, 20))

        # اسم المستخدم
        ttk.Label(form_frame, text="اسم المستخدم:",
                 font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)

        self.username_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.username_var,
                 font=self.arabic_font, width=25).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # كلمة المرور
        ttk.Label(form_frame, text="كلمة المرور:",
                 font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=5)

        self.password_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.password_var,
                 font=self.arabic_font, show="*", width=25).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # الاسم الكامل
        ttk.Label(form_frame, text="الاسم الكامل:",
                 font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=5)

        self.full_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.full_name_var,
                 font=self.arabic_font, width=25).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # الدور
        ttk.Label(form_frame, text="الدور:",
                 font=self.arabic_font).grid(row=3, column=0, sticky=tk.W, pady=5)

        self.role_var = tk.StringVar()
        role_combo = ttk.Combobox(form_frame, textvariable=self.role_var,
                                 state="readonly", width=22)

        # إضافة الأدوار المتاحة
        roles = [
            (UserRole.EMPLOYEE, "موظف")
        ]

        # إضافة دور المدير فقط للمدير الحالي
        if self.user_manager.current_user['role'] == UserRole.ADMIN:
            roles.insert(0, (UserRole.ADMIN, "مدير النظام"))

        role_combo['values'] = [role[1] for role in roles]
        self.role_mapping = {role[1]: role[0] for role in roles}

        role_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
        role_combo.set("موظف")  # القيمة الافتراضية

        # البريد الإلكتروني
        ttk.Label(form_frame, text="البريد الإلكتروني:",
                 font=self.arabic_font).grid(row=4, column=0, sticky=tk.W, pady=5)

        self.email_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.email_var,
                 font=self.arabic_font, width=25).grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # رقم الهاتف
        ttk.Label(form_frame, text="رقم الهاتف:",
                 font=self.arabic_font).grid(row=5, column=0, sticky=tk.W, pady=5)

        self.phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.phone_var,
                 font=self.arabic_font, width=25).grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # تكوين الأعمدة
        form_frame.columnconfigure(1, weight=1)

        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="إضافة",
                  command=self.add_user).pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(button_frame, text="إلغاء",
                  command=self.cancel).pack(side=tk.RIGHT)

    def add_user(self):
        """إضافة المستخدم"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        full_name = self.full_name_var.get().strip()
        role_display = self.role_var.get()
        email = self.email_var.get().strip()
        phone = self.phone_var.get().strip()

        if not username or not password or not full_name or not role_display:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
            return

        role = self.role_mapping.get(role_display)
        if not role:
            messagebox.showerror("خطأ", "يرجى اختيار دور صحيح")
            return

        success, message = self.user_manager.add_user(username, password, full_name, role, email, phone)

        if success:
            messagebox.showinfo("نجح", message)
            self.result = True
            self.dialog.destroy()
        else:
            messagebox.showerror("خطأ", message)

    def cancel(self):
        """إلغاء الإضافة"""
        self.result = False
        self.dialog.destroy()

    def show(self) -> bool:
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result

class EditUserDialog:
    """نافذة تعديل المستخدم"""

    def __init__(self, parent, user_manager: UserManager, user_data: Dict):
        self.parent = parent
        self.user_manager = user_manager
        self.user_data = user_data
        self.result = False

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"تعديل المستخدم: {user_data['username']}")
        self.dialog.geometry("400x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry(f"+{parent.winfo_rootx() + 100}+{parent.winfo_rooty() + 100}")

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_data()

    def create_interface(self):
        """إنشاء واجهة تعديل المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # نموذج البيانات
        form_frame = ttk.LabelFrame(main_frame, text="تعديل بيانات المستخدم", padding="15")
        form_frame.pack(fill=tk.X, pady=(0, 20))

        # الاسم الكامل
        ttk.Label(form_frame, text="الاسم الكامل:",
                 font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)

        self.full_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.full_name_var,
                 font=self.arabic_font, width=25).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # الدور
        ttk.Label(form_frame, text="الدور:",
                 font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=5)

        self.role_var = tk.StringVar()
        self.role_combo = ttk.Combobox(form_frame, textvariable=self.role_var,
                                      state="readonly", width=22)

        # إضافة الأدوار المتاحة
        roles = [
            (UserRole.EMPLOYEE, "موظف")
        ]

        # إضافة دور المدير فقط للمدير الحالي
        if self.user_manager.current_user['role'] == UserRole.ADMIN:
            roles.insert(0, (UserRole.ADMIN, "مدير النظام"))

        self.role_combo['values'] = [role[1] for role in roles]
        self.role_mapping = {role[1]: role[0] for role in roles}

        self.role_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # البريد الإلكتروني
        ttk.Label(form_frame, text="البريد الإلكتروني:",
                 font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=5)

        self.email_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.email_var,
                 font=self.arabic_font, width=25).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # رقم الهاتف
        ttk.Label(form_frame, text="رقم الهاتف:",
                 font=self.arabic_font).grid(row=3, column=0, sticky=tk.W, pady=5)

        self.phone_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.phone_var,
                 font=self.arabic_font, width=25).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))

        # الحالة
        ttk.Label(form_frame, text="الحالة:",
                 font=self.arabic_font).grid(row=4, column=0, sticky=tk.W, pady=5)

        self.is_active_var = tk.BooleanVar()
        ttk.Checkbutton(form_frame, text="نشط",
                       variable=self.is_active_var).grid(row=4, column=1, sticky=tk.W, pady=5, padx=(10, 0))

        # تكوين الأعمدة
        form_frame.columnconfigure(1, weight=1)

        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="حفظ",
                  command=self.save_changes).pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(button_frame, text="إلغاء",
                  command=self.cancel).pack(side=tk.RIGHT)

    def load_data(self):
        """تحميل بيانات المستخدم"""
        self.full_name_var.set(self.user_data['full_name'])
        self.email_var.set(self.user_data['email'] or "")
        self.phone_var.set(self.user_data['phone'] or "")
        self.is_active_var.set(self.user_data['is_active'])

        # تحديد الدور
        role_display = self.user_manager.get_role_display_name(UserRole(self.user_data['role']))
        self.role_var.set(role_display)

    def save_changes(self):
        """حفظ التغييرات"""
        full_name = self.full_name_var.get().strip()
        role_display = self.role_var.get()
        email = self.email_var.get().strip()
        phone = self.phone_var.get().strip()
        is_active = self.is_active_var.get()

        if not full_name or not role_display:
            messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
            return

        role = self.role_mapping.get(role_display)
        if not role:
            messagebox.showerror("خطأ", "يرجى اختيار دور صحيح")
            return

        success, message = self.user_manager.update_user(
            self.user_data['id'], full_name, role, email, phone, is_active
        )

        if success:
            messagebox.showinfo("نجح", message)
            self.result = True
            self.dialog.destroy()
        else:
            messagebox.showerror("خطأ", message)

    def cancel(self):
        """إلغاء التعديل"""
        self.result = False
        self.dialog.destroy()

    def show(self) -> bool:
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result
