#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database module for Camera and Lens Rental Management System
Handles SQLite database operations, schema management, and CRUD operations
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """Manages SQLite database operations for the rental system"""
    
    def __init__(self, db_path: str = None):
        """Initialize database manager with database path"""
        if db_path is None:
            # تحديد مسار قاعدة البيانات في مجلد database
            current_dir = os.path.dirname(os.path.abspath(__file__))
            system_dir = os.path.dirname(current_dir)
            db_dir = os.path.join(system_dir, "database")

            # إنشاء مجلد database إذا لم يكن موجوداً
            if not os.path.exists(db_dir):
                os.makedirs(db_dir)
                print(f"تم إنشاء مجلد قاعدة البيانات: {db_dir}")

            db_path = os.path.join(db_dir, "rental_system.db")

        self.db_path = db_path
        print(f"مسار قاعدة البيانات: {self.db_path}")
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with UTF-8 support"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign key constraints
            return conn
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            print(f"مسار قاعدة البيانات: {self.db_path}")
            raise
    
    def init_database(self):
        """Initialize database with required tables"""
        try:
            print("بدء تهيئة قاعدة البيانات...")
            conn = self.get_connection()
            cursor = conn.cursor()
            print("تم الاتصال بقاعدة البيانات بنجاح")
            # Equipment table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS equipment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    model TEXT NOT NULL,
                    serial_number TEXT UNIQUE NOT NULL,
                    category TEXT NOT NULL DEFAULT 'كاميرات',
                    status TEXT NOT NULL DEFAULT 'Available',
                    price REAL DEFAULT 0.0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CHECK (status IN ('Available', 'Rented', 'Maintenance', 'Damaged', 'Lost')),
                    CHECK (category IN ('كاميرات', 'عدسات', 'معدات إضاءة'))
                )
            ''')

            # إضافة حقل السعر للمعدات الموجودة إذا لم يكن موجوداً
            try:
                cursor.execute("ALTER TABLE equipment ADD COLUMN price REAL DEFAULT 0.0")
                print("تم إضافة حقل السعر لجدول المعدات")
            except sqlite3.OperationalError:
                # الحقل موجود بالفعل
                pass

            # إضافة حقل سعر الإيجار اليومي للمعدات
            try:
                cursor.execute('ALTER TABLE equipment ADD COLUMN daily_rental_price REAL DEFAULT 0.0')
                print("تم إضافة حقل سعر الإيجار اليومي للمعدات")
            except sqlite3.OperationalError:
                # الحقل موجود بالفعل
                pass

            # إضافة حقول الدفع والخصم لجدول الإيجارات المتعددة
            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN discount_amount REAL DEFAULT 0.0')
                print("تم إضافة حقل مبلغ الخصم")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN paid_amount REAL DEFAULT 0.0')
                print("تم إضافة حقل المبلغ المدفوع")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN remaining_amount REAL DEFAULT 0.0')
                print("تم إضافة حقل المبلغ المتبقي")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN payment_status TEXT DEFAULT "Pending"')
                print("تم إضافة حقل حالة الدفع")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN actual_return_date DATE')
                print("تم إضافة حقل تاريخ الإرجاع الفعلي")
            except sqlite3.OperationalError:
                pass

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN rental_number TEXT')
                print("تم إضافة حقل رقم الإيجار")
            except sqlite3.OperationalError:
                pass

            # إنشاء جدول دفعات الإيجار
            try:
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS rental_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        rental_group_id INTEGER,
                        amount REAL NOT NULL,
                        payment_method TEXT DEFAULT 'نقدي',
                        payment_date DATE NOT NULL,
                        notes TEXT DEFAULT '',
                        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (rental_group_id) REFERENCES rental_groups (id)
                    )
                ''')
                print("تم إنشاء جدول دفعات الإيجار")
            except sqlite3.OperationalError as e:
                print(f"جدول دفعات الإيجار موجود بالفعل: {e}")

            # جدول المكاتب والموردين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS offices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    address TEXT,
                    phone TEXT,
                    email TEXT,
                    contact_person TEXT,
                    office_type TEXT NOT NULL DEFAULT 'supplier',
                    status TEXT NOT NULL DEFAULT 'Active',
                    notes TEXT DEFAULT '',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CHECK (office_type IN ('supplier', 'partner', 'branch')),
                    CHECK (status IN ('Active', 'Inactive'))
                )
            ''')

            # جدول استقطاب المعدات من الخارج
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS external_equipment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_name TEXT NOT NULL,
                    equipment_model TEXT,
                    equipment_category TEXT NOT NULL,
                    supplier_name TEXT NOT NULL,
                    supplier_phone TEXT,
                    supplier_address TEXT,
                    office_id INTEGER,
                    daily_rental_cost REAL NOT NULL DEFAULT 0.0,
                    customer_daily_price REAL NOT NULL DEFAULT 0.0,
                    profit_margin REAL DEFAULT 0.0,
                    rental_start_date DATE NOT NULL,
                    rental_end_date DATE NOT NULL,
                    customer_id INTEGER NOT NULL,
                    status TEXT NOT NULL DEFAULT 'Active',
                    notes TEXT DEFAULT '',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (office_id) REFERENCES offices (id),
                    CHECK (status IN ('Active', 'Completed', 'Cancelled'))
                )
            ''')

            # جدول المعدات المفقودة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS lost_equipment (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    equipment_id INTEGER NOT NULL,
                    rental_id INTEGER,
                    rental_group_id INTEGER,
                    customer_id INTEGER NOT NULL,
                    lost_date DATE NOT NULL,
                    rental_date DATE NOT NULL,
                    expected_return_date DATE NOT NULL,
                    rental_price REAL NOT NULL DEFAULT 0.0,
                    compensation_amount REAL DEFAULT 0.0,
                    compensation_paid REAL DEFAULT 0.0,
                    status TEXT NOT NULL DEFAULT 'Lost',
                    notes TEXT DEFAULT '',
                    reported_by TEXT DEFAULT '',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                    FOREIGN KEY (rental_id) REFERENCES rentals (id),
                    FOREIGN KEY (rental_group_id) REFERENCES rental_groups (id),
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    CHECK (status IN ('Lost', 'Found', 'Compensated', 'Written_Off'))
                )
            ''')

            # جدول سجل إرجاع المعدات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS equipment_return_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rental_id INTEGER NOT NULL,
                    equipment_id INTEGER NOT NULL,
                    return_date DATE NOT NULL,
                    condition TEXT NOT NULL,
                    notes TEXT DEFAULT '',
                    returned_by INTEGER,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (rental_id) REFERENCES rentals (id),
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                    FOREIGN KEY (returned_by) REFERENCES users (id)
                )
            ''')

            # جدول سجل الإيجارات (أرشيف 30 يوم)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rental_archive (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_rental_id INTEGER,
                    original_rental_group_id INTEGER,
                    rental_type TEXT NOT NULL,
                    customer_id INTEGER NOT NULL,
                    equipment_data TEXT NOT NULL,
                    rental_date DATE NOT NULL,
                    return_date DATE,
                    expected_return_date DATE NOT NULL,
                    actual_return_date DATE,
                    total_price REAL NOT NULL DEFAULT 0.0,
                    status TEXT NOT NULL,
                    archive_reason TEXT NOT NULL,
                    archive_date DATE NOT NULL,
                    notes TEXT DEFAULT '',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    CHECK (rental_type IN ('individual', 'multi')),
                    CHECK (status IN ('Completed', 'Lost', 'Cancelled', 'Overdue')),
                    CHECK (archive_reason IN ('Returned', 'Lost', 'Cancelled', 'Auto_Archive'))
                )
            ''')
            
            # Customers table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT NOT NULL,
                    national_id TEXT UNIQUE,
                    photo_path TEXT,
                    id_photo_path TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Rentals table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rentals (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER NOT NULL,
                    equipment_id INTEGER NOT NULL,
                    rental_date DATE NOT NULL,
                    return_date DATE NOT NULL,
                    price REAL NOT NULL,
                    status TEXT NOT NULL DEFAULT 'Active',
                    actual_return_date DATE,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                    CHECK (status IN ('Active', 'Returned', 'Overdue'))
                )
            ''')
            
            # Add category column to existing equipment table if it doesn't exist
            cursor.execute("PRAGMA table_info(equipment)")
            equipment_columns = [column[1] for column in cursor.fetchall()]

            if 'category' not in equipment_columns:
                cursor.execute('''
                    ALTER TABLE equipment
                    ADD COLUMN category TEXT NOT NULL DEFAULT 'كاميرات'
                ''')
                print("Added category column to equipment table")

            # Add photo_path column to existing customers table if it doesn't exist
            cursor.execute("PRAGMA table_info(customers)")
            customer_columns = [column[1] for column in cursor.fetchall()]

            if 'photo_path' not in customer_columns:
                cursor.execute('''
                    ALTER TABLE customers
                    ADD COLUMN photo_path TEXT
                ''')
                print("Added photo_path column to customers table")

            # Add notes column to existing rentals table if it doesn't exist
            cursor.execute("PRAGMA table_info(rentals)")
            rental_columns = [column[1] for column in cursor.fetchall()]

            if 'notes' not in rental_columns:
                cursor.execute('''
                    ALTER TABLE rentals
                    ADD COLUMN notes TEXT
                ''')
                print("Added notes column to rentals table")

            # إضافة الأعمدة الجديدة للعملاء
            if 'national_id' not in customer_columns:
                try:
                    cursor.execute('''
                        ALTER TABLE customers
                        ADD COLUMN national_id TEXT
                    ''')
                    print("Added national_id column to customers table")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e).lower():
                        print(f"خطأ في إضافة عمود الرقم القومي: {e}")

            if 'id_photo_path' not in customer_columns:
                try:
                    cursor.execute('''
                        ALTER TABLE customers
                        ADD COLUMN id_photo_path TEXT
                    ''')
                    print("Added id_photo_path column to customers table")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e).lower():
                        print(f"خطأ في إضافة عمود صورة البطاقة: {e}")

            # إنشاء جداول المخزن
            self.create_inventory_tables_in_init(cursor)

            # إنشاء جدول إعدادات الشركة
            self.create_company_settings_table(cursor)

            # إنشاء جدول الصلاحيات المتقدم
            self.create_advanced_permissions_table(cursor)

            # إنشاء جداول الإيجارات المتعددة
            self.create_multi_rental_tables(cursor)

            # إنشاء جدول المستخدمين إذا لم يكن موجوداً
            self.create_users_table(cursor)

            # إنشاء جداول الترقيم التسلسلي
            self.create_sequence_tables(cursor)

            conn.commit()
            print("تم تهيئة قاعدة البيانات بنجاح")
            print("Database initialized successfully")

        except sqlite3.Error as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            print(f"Database initialization error: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def create_users_table(self, cursor):
        """إنشاء جدول المستخدمين"""
        # التحقق من وجود الجدول أولاً
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            # إنشاء الجدول إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'employee',
                    full_name TEXT,
                    email TEXT,
                    phone TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    failed_login_attempts INTEGER DEFAULT 0,
                    CHECK (role IN ('admin', 'manager', 'employee'))
                )
            ''')
            print("تم إنشاء جدول المستخدمين")
        else:
            # التحقق من الأعمدة الموجودة وإضافة المفقودة
            cursor.execute("PRAGMA table_info(users)")
            existing_columns = [column[1] for column in cursor.fetchall()]

            # إضافة الأعمدة المفقودة
            if 'password' not in existing_columns:
                try:
                    cursor.execute('ALTER TABLE users ADD COLUMN password TEXT')
                    print("تم إضافة عمود password")
                except sqlite3.OperationalError:
                    pass

            if 'role' not in existing_columns:
                try:
                    cursor.execute('ALTER TABLE users ADD COLUMN role TEXT DEFAULT "employee"')
                    print("تم إضافة عمود role")
                except sqlite3.OperationalError:
                    pass

            if 'phone' not in existing_columns:
                try:
                    cursor.execute('ALTER TABLE users ADD COLUMN phone TEXT')
                    print("تم إضافة عمود phone")
                except sqlite3.OperationalError:
                    pass

            if 'failed_login_attempts' not in existing_columns:
                try:
                    cursor.execute('ALTER TABLE users ADD COLUMN failed_login_attempts INTEGER DEFAULT 0')
                    print("تم إضافة عمود failed_login_attempts")
                except sqlite3.OperationalError:
                    pass

        # إنشاء المستخدم الافتراضي الأساسي (admin) إذا لم يكن موجوداً
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            # كلمة مرور مشفرة لـ Admin@2024
            import hashlib
            password_hash = hashlib.sha256('Admin@2024'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ('admin', password_hash, 'admin', 'مدير النظام الرئيسي'))
            print("تم إنشاء المستخدم الافتراضي: admin")

        # إنشاء مستخدم مدير إضافي
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('manager',))
        if cursor.fetchone()[0] == 0:
            import hashlib
            password_hash = hashlib.sha256('Manager@2024'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ('manager', password_hash, 'admin', 'مدير النظام'))
            print("تم إنشاء المستخدم الافتراضي: manager")

        # إنشاء مستخدم مدير فرعي
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('supervisor',))
        if cursor.fetchone()[0] == 0:
            import hashlib
            password_hash = hashlib.sha256('Super@2024'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ('supervisor', password_hash, 'manager', 'مدير فرعي'))
            print("تم إنشاء المستخدم الافتراضي: supervisor")

        # إنشاء مستخدم موظف افتراضي
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('employee',))
        if cursor.fetchone()[0] == 0:
            import hashlib
            password_hash = hashlib.sha256('Employee@2024'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ('employee', password_hash, 'employee', 'موظف النظام'))
            print("تم إنشاء المستخدم الافتراضي: employee")

        # إنشاء مستخدم موظف إضافي
        cursor.execute('SELECT COUNT(*) FROM users WHERE username = ?', ('staff',))
        if cursor.fetchone()[0] == 0:
            import hashlib
            password_hash = hashlib.sha256('Staff@2024'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO users (username, password, role, full_name)
                VALUES (?, ?, ?, ?)
            ''', ('staff', password_hash, 'employee', 'موظف'))
            print("تم إنشاء المستخدم الافتراضي: staff")

    def create_inventory_tables_in_init(self, cursor):
        """إنشاء جداول المخزن ضمن عملية التهيئة"""

        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول فئات قطع الغيار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول قطع الغيار والإكسسوارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category_id INTEGER NOT NULL,
                brand TEXT,
                model TEXT,
                part_number TEXT,
                description TEXT,
                unit_price REAL DEFAULT 0.0,
                rental_price REAL DEFAULT 0.0,
                min_stock_level INTEGER DEFAULT 5,
                current_stock INTEGER DEFAULT 0,
                supplier_id INTEGER,
                expiry_date DATE,
                battery_level INTEGER, -- للبطاريات (0-100)
                battery_condition TEXT, -- ممتاز، جيد، متوسط، ضعيف
                weight_capacity REAL, -- للترايبودات (بالكيلوغرام)
                memory_size INTEGER, -- لكروت الذاكرة (بالجيجابايت)
                memory_speed TEXT, -- سرعة كرت الذاكرة
                compatible_equipment TEXT, -- المعدات المتوافقة معها
                notes TEXT,
                status TEXT DEFAULT 'Active',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES inventory_categories (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                CHECK (status IN ('Active', 'Discontinued', 'Out of Stock')),
                CHECK (battery_condition IN ('ممتاز', 'جيد', 'متوسط', 'ضعيف') OR battery_condition IS NULL)
            )
        ''')

        # جدول حركة المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                unit_cost REAL,
                total_cost REAL,
                supplier_id INTEGER,
                reference_number TEXT,
                notes TEXT,
                movement_date DATE DEFAULT (date('now')),
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                CHECK (movement_type IN ('Purchase', 'Sale', 'Rental', 'Return', 'Adjustment', 'Damage', 'Loss'))
            )
        ''')

        # جدول ربط قطع الغيار بالإيجارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rental_inventory_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rental_id INTEGER NOT NULL,
                item_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 1,
                rental_price REAL NOT NULL DEFAULT 0.0,
                returned_quantity INTEGER DEFAULT 0,
                status TEXT DEFAULT 'Active',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rental_id) REFERENCES rentals (id),
                FOREIGN KEY (item_id) REFERENCES inventory_items (id),
                CHECK (status IN ('Active', 'Returned', 'Damaged', 'Lost'))
            )
        ''')

        # إدراج الفئات الافتراضية
        default_categories = [
            ('بطاريات', 'بطاريات الكاميرات والمعدات'),
            ('شواحن', 'شواحن البطاريات'),
            ('ترايبودات', 'حوامل ثلاثية الأرجل'),
            ('كروت ذاكرة', 'بطاقات التخزين'),
            ('فلاتر العدسات', 'فلاتر التصوير'),
            ('أغطية العدسات', 'أغطية حماية العدسات'),
            ('حقائب الحمل', 'حقائب وحافظات المعدات'),
            ('كابلات وأسلاك', 'كابلات التوصيل والنقل'),
            ('إكسسوارات أخرى', 'قطع غيار وإكسسوارات متنوعة')
        ]

        for category_name, description in default_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO inventory_categories (name, description)
                VALUES (?, ?)
            ''', (category_name, description))

    def create_company_settings_table(self, cursor):
        """إنشاء جدول إعدادات الشركة"""

        # جدول إعدادات الشركة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS company_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'text',
                description TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إدراج الإعدادات الافتراضية
        default_settings = [
            ('company_name', 'شركة تأجير المعدات', 'text', 'اسم الشركة'),
            ('company_logo', '', 'file', 'شعار الشركة'),
            ('company_address', 'العنوان غير محدد', 'text', 'عنوان الشركة'),
            ('company_phone', '01234567890', 'text', 'هاتف الشركة'),
            ('company_phone2', '', 'text', 'هاتف الشركة الثاني'),
            ('company_email', '<EMAIL>', 'email', 'بريد الشركة الإلكتروني'),
            ('company_website', '', 'url', 'موقع الشركة الإلكتروني'),
            ('report_header_color', '#2c3e50', 'color', 'لون رأس التقارير'),
            ('report_footer_text', 'شكراً لتعاملكم معنا', 'text', 'نص تذييل التقارير'),
            ('currency_symbol', 'ج.م', 'text', 'رمز العملة'),
            ('tax_rate', '14', 'number', 'معدل الضريبة (%)'),
            ('rental_terms', 'يرجى إرجاع المعدات في الموعد المحدد', 'textarea', 'شروط الإيجار')
        ]

        for key, value, setting_type, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO company_settings (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            ''', (key, value, setting_type, description))

    def create_advanced_permissions_table(self, cursor):
        """إنشاء جدول الصلاحيات المتقدم"""

        # جدول الصلاحيات المتقدم
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission_key TEXT NOT NULL,
                permission_value BOOLEAN DEFAULT 0,
                granted_by INTEGER,
                granted_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (granted_by) REFERENCES users (id),
                UNIQUE(user_id, permission_key)
            )
        ''')

        # جدول تعريف الصلاحيات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS permission_definitions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                permission_key TEXT UNIQUE NOT NULL,
                permission_name TEXT NOT NULL,
                permission_description TEXT,
                category TEXT DEFAULT 'general',
                is_manager_only BOOLEAN DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إدراج تعريفات الصلاحيات الافتراضية
        default_permissions = [
            # صلاحيات المعدات
            ('equipment_view', 'عرض المعدات', 'إمكانية عرض قائمة المعدات', 'equipment', 0),
            ('equipment_add', 'إضافة معدات', 'إمكانية إضافة معدات جديدة', 'equipment', 0),
            ('equipment_edit', 'تعديل معدات', 'إمكانية تعديل بيانات المعدات', 'equipment', 0),
            ('equipment_delete', 'حذف معدات', 'إمكانية حذف المعدات', 'equipment', 1),
            ('equipment_status', 'تغيير حالة المعدات', 'إمكانية تغيير حالة المعدات', 'equipment', 0),

            # صلاحيات العملاء
            ('customers_view', 'عرض العملاء', 'إمكانية عرض قائمة العملاء', 'customers', 0),
            ('customers_add', 'إضافة عملاء', 'إمكانية إضافة عملاء جدد', 'customers', 0),
            ('customers_edit', 'تعديل عملاء', 'إمكانية تعديل بيانات العملاء', 'customers', 0),
            ('customers_delete', 'حذف عملاء', 'إمكانية حذف العملاء', 'customers', 1),

            # صلاحيات الإيجارات
            ('rentals_view', 'عرض الإيجارات', 'إمكانية عرض قائمة الإيجارات', 'rentals', 0),
            ('rentals_create', 'إنشاء إيجارات', 'إمكانية إنشاء إيجارات جديدة', 'rentals', 0),
            ('rentals_edit', 'تعديل إيجارات', 'إمكانية تعديل الإيجارات المحفوظة', 'rentals', 0),
            ('rentals_cancel', 'إلغاء إيجارات', 'إمكانية إلغاء الإيجارات', 'rentals', 0),
            ('rentals_return', 'استلام إرجاع', 'إمكانية تسجيل إرجاع المعدات', 'rentals', 0),

            # صلاحيات التقارير
            ('reports_view', 'عرض التقارير', 'إمكانية عرض التقارير والإحصائيات', 'reports', 0),
            ('reports_export', 'تصدير التقارير', 'إمكانية تصدير التقارير', 'reports', 0),
            ('reports_print', 'طباعة التقارير', 'إمكانية طباعة التقارير', 'reports', 0),

            # صلاحيات المخزن
            ('inventory_view', 'عرض المخزن', 'إمكانية عرض المخزن والقطع الاحتياطية', 'inventory', 0),
            ('inventory_add', 'إضافة للمخزن', 'إمكانية إضافة عناصر للمخزن', 'inventory', 0),
            ('inventory_edit', 'تعديل المخزن', 'إمكانية تعديل عناصر المخزن', 'inventory', 0),
            ('inventory_delete', 'حذف من المخزن', 'إمكانية حذف عناصر من المخزن', 'inventory', 1),

            # صلاحيات النظام
            ('system_settings', 'إعدادات النظام', 'إمكانية الوصول لإعدادات النظام', 'system', 1),
            ('user_management', 'إدارة المستخدمين', 'إمكانية إدارة المستخدمين والصلاحيات', 'system', 1),
            ('company_settings', 'إعدادات الشركة', 'إمكانية تعديل إعدادات الشركة', 'system', 1),
            ('backup_restore', 'النسخ الاحتياطي', 'إمكانية إنشاء واستعادة النسخ الاحتياطية', 'system', 1),
        ]

        for key, name, description, category, is_manager_only in default_permissions:
            cursor.execute('''
                INSERT OR IGNORE INTO permission_definitions
                (permission_key, permission_name, permission_description, category, is_manager_only)
                VALUES (?, ?, ?, ?, ?)
            ''', (key, name, description, category, is_manager_only))

    def create_multi_rental_tables(self, cursor):
        """إنشاء جداول الإيجارات المتعددة"""

        # جدول الإيجارات الرئيسي (محدث)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rental_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER NOT NULL,
                rental_date DATE NOT NULL,
                return_date DATE NOT NULL,
                total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                status TEXT DEFAULT 'Active',
                notes TEXT,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')

        # جدول عناصر الإيجار (المعدات المختارة)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rental_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rental_group_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                rental_price DECIMAL(10,2) NOT NULL,
                days_count INTEGER NOT NULL DEFAULT 1,
                subtotal DECIMAL(10,2) NOT NULL,
                status TEXT DEFAULT 'Active',
                returned_date TIMESTAMP,
                condition_on_return TEXT,
                notes TEXT,
                FOREIGN KEY (rental_group_id) REFERENCES rental_groups (id) ON DELETE CASCADE,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')

        # فهرس لتحسين الأداء
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_rental_items_group
            ON rental_items (rental_group_id)
        ''')

        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_rental_items_equipment
            ON rental_items (equipment_id)
        ''')

        # إضافة عمود group_id للجدول القديم للتوافق
        try:
            cursor.execute('ALTER TABLE rentals ADD COLUMN rental_group_id INTEGER')
        except sqlite3.OperationalError:
            # العمود موجود بالفعل
            pass

    # Equipment CRUD operations
    def add_equipment(self, name: str, model: str, serial_number: str, category: str = 'كاميرات', status: str = 'Available', price: float = 0.0, daily_rental_price: float = 0.0) -> bool:
        """Add new equipment to database"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO equipment (name, model, serial_number, category, status, price, daily_rental_price)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (name, model, serial_number, category, status, price, daily_rental_price))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            print(f"Equipment with serial number {serial_number} already exists")
            return False
        except sqlite3.Error as e:
            print(f"Error adding equipment: {e}")
            return False
        finally:
            conn.close()

    # External Equipment CRUD operations
    def add_external_equipment(self, equipment_name: str, equipment_model: str, equipment_category: str,
                             supplier_name: str, supplier_phone: str, supplier_address: str,
                             daily_rental_cost: float, customer_daily_price: float,
                             rental_start_date: str, rental_end_date: str, customer_id: int,
                             notes: str = '') -> int:
        """إضافة معدة مستقطبة من الخارج"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # حساب هامش الربح
            profit_margin = customer_daily_price - daily_rental_cost

            cursor.execute('''
                INSERT INTO external_equipment (
                    equipment_name, equipment_model, equipment_category,
                    supplier_name, supplier_phone, supplier_address,
                    daily_rental_cost, customer_daily_price, profit_margin,
                    rental_start_date, rental_end_date, customer_id, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (equipment_name, equipment_model, equipment_category,
                  supplier_name, supplier_phone, supplier_address,
                  daily_rental_cost, customer_daily_price, profit_margin,
                  rental_start_date, rental_end_date, customer_id, notes))

            external_id = cursor.lastrowid
            conn.commit()
            return external_id
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المعدة المستقطبة: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    def get_external_equipment(self) -> List[Dict]:
        """الحصول على جميع المعدات المستقطبة من الخارج"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT ee.*, c.name as customer_name, c.phone as customer_phone
                FROM external_equipment ee
                LEFT JOIN customers c ON ee.customer_id = c.id
                ORDER BY ee.created_date DESC
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المعدات المستقطبة: {e}")
            return []
        finally:
            conn.close()

    def update_external_equipment_status(self, external_id: int, status: str, notes: str = '') -> bool:
        """تحديث حالة المعدة المستقطبة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE external_equipment
                SET status = ?, notes = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (status, notes, external_id))

            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في تحديث حالة المعدة المستقطبة: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_equipment(self, equipment_id: Optional[int] = None, status: Optional[str] = None, category: Optional[str] = None) -> List[Dict]:
        """Get equipment records, optionally filtered by ID, status, or category"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if equipment_id:
                cursor.execute('SELECT * FROM equipment WHERE id = ?', (equipment_id,))
            elif status and category:
                cursor.execute('SELECT * FROM equipment WHERE status = ? AND category = ? ORDER BY name', (status, category))
            elif status:
                cursor.execute('SELECT * FROM equipment WHERE status = ? ORDER BY name', (status,))
            elif category:
                cursor.execute('SELECT * FROM equipment WHERE category = ? ORDER BY name', (category,))
            else:
                cursor.execute('SELECT * FROM equipment ORDER BY category, name')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error retrieving equipment: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_by_id(self, equipment_id: int) -> Optional[Dict]:
        """الحصول على معدة واحدة بالمعرف"""
        equipment_list = self.get_equipment(equipment_id=equipment_id)
        return equipment_list[0] if equipment_list else None

    def get_equipment_rentals(self, equipment_id: int) -> List[Dict]:
        """الحصول على تاريخ إيجارات معدة معينة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT r.*, c.name as customer_name
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                WHERE r.equipment_id = ?
                ORDER BY r.rental_date DESC
            '''

            cursor.execute(query, (equipment_id,))
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except Exception as e:
            print(f"خطأ في الحصول على إيجارات المعدة: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_active_rentals(self, equipment_id: int) -> List[Dict]:
        """الحصول على الإيجارات النشطة لمعدة معينة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT r.*, c.name as customer_name
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                WHERE r.equipment_id = ? AND r.status = 'Active'
                ORDER BY r.rental_date DESC
            '''

            cursor.execute(query, (equipment_id,))
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except Exception as e:
            print(f"خطأ في الحصول على الإيجارات النشطة للمعدة: {e}")
            return []
        finally:
            conn.close()

    def update_equipment(self, equipment_id: int, name: str, model: str, serial_number: str, category: str, status: str, price: float = None) -> bool:
        """Update equipment record"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if price is not None:
                cursor.execute('''
                    UPDATE equipment
                    SET name = ?, model = ?, serial_number = ?, category = ?, status = ?, price = ?
                    WHERE id = ?
                ''', (name, model, serial_number, category, status, price, equipment_id))
            else:
                cursor.execute('''
                    UPDATE equipment
                    SET name = ?, model = ?, serial_number = ?, category = ?, status = ?
                    WHERE id = ?
                ''', (name, model, serial_number, category, status, equipment_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.IntegrityError:
            print(f"Equipment with serial number {serial_number} already exists")
            return False
        except sqlite3.Error as e:
            print(f"Error updating equipment: {e}")
            return False
        finally:
            conn.close()
    
    def update_equipment_status(self, equipment_id: int, status: str) -> bool:
        """Update equipment status only"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', (status, equipment_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"Error updating equipment status: {e}")
            return False
        finally:
            conn.close()
    
    def delete_equipment(self, equipment_id: int) -> bool:
        """Delete equipment record"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if equipment is currently rented
            cursor.execute('''
                SELECT COUNT(*) FROM rentals 
                WHERE equipment_id = ? AND status = 'Active'
            ''', (equipment_id,))
            
            if cursor.fetchone()[0] > 0:
                print("Cannot delete equipment that is currently rented")
                return False
            
            cursor.execute('DELETE FROM equipment WHERE id = ?', (equipment_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"Error deleting equipment: {e}")
            return False
        finally:
            conn.close()
    
    def search_equipment(self, search_term: str) -> List[Dict]:
        """Search equipment by name, model, or serial number"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            search_pattern = f"%{search_term}%"
            cursor.execute('''
                SELECT * FROM equipment
                WHERE name LIKE ? OR model LIKE ? OR serial_number LIKE ?
                ORDER BY name
            ''', (search_pattern, search_pattern, search_pattern))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching equipment: {e}")
            return []
        finally:
            conn.close()

    def get_customer_rentals(self, customer_id: int) -> List[Dict]:
        """الحصول على تاريخ إيجارات عميل معين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT r.*, e.name as equipment_name
                FROM rentals r
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE r.customer_id = ?
                ORDER BY r.rental_date DESC
            '''

            cursor.execute(query, (customer_id,))
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except Exception as e:
            print(f"خطأ في الحصول على إيجارات العميل: {e}")
            return []
        finally:
            conn.close()

    # Customer CRUD operations
    def add_customer(self, name: str, phone: str, national_id: Optional[str] = None,
                    photo_path: Optional[str] = None, id_photo_path: Optional[str] = None) -> bool:
        """Add new customer to database"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO customers (name, phone, national_id, photo_path, id_photo_path)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, phone, national_id, photo_path, id_photo_path))
            conn.commit()
            return True
        except sqlite3.IntegrityError as e:
            if "national_id" in str(e):
                print(f"الرقم القومي {national_id} موجود بالفعل")
            else:
                print(f"خطأ في إضافة العميل: {e}")
            return False
        except sqlite3.Error as e:
            print(f"Error adding customer: {e}")
            return False
        finally:
            conn.close()

    def get_customers(self, customer_id: Optional[int] = None) -> List[Dict]:
        """Get customer records, optionally filtered by ID"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if customer_id:
                cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
            else:
                cursor.execute('SELECT * FROM customers ORDER BY name')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error retrieving customers: {e}")
            return []
        finally:
            conn.close()

    def update_customer(self, customer_id: int, name: str, phone: str, national_id: Optional[str] = None,
                       photo_path: Optional[str] = None, id_photo_path: Optional[str] = None) -> bool:
        """Update customer record"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE customers
                SET name = ?, phone = ?, national_id = ?, photo_path = ?, id_photo_path = ?
                WHERE id = ?
            ''', (name, phone, national_id, photo_path, id_photo_path, customer_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.IntegrityError as e:
            if "national_id" in str(e):
                print(f"الرقم القومي {national_id} موجود بالفعل")
            else:
                print(f"خطأ في تعديل العميل: {e}")
            return False
        except sqlite3.Error as e:
            print(f"Error updating customer: {e}")
            return False
        finally:
            conn.close()

    def delete_customer(self, customer_id: int) -> bool:
        """Delete customer record"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Check if customer has active rentals
            cursor.execute('''
                SELECT COUNT(*) FROM rentals
                WHERE customer_id = ? AND status = 'Active'
            ''', (customer_id,))

            if cursor.fetchone()[0] > 0:
                print("Cannot delete customer with active rentals")
                return False

            cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"Error deleting customer: {e}")
            return False
        finally:
            conn.close()

    def delete_rental(self, rental_id: int) -> bool:
        """Delete individual rental record"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Get rental details first
            cursor.execute('''
                SELECT equipment_id, status FROM rentals WHERE id = ?
            ''', (rental_id,))

            rental = cursor.fetchone()
            if not rental:
                print(f"Rental with ID {rental_id} not found")
                return False

            equipment_id, status = rental

            # Delete rental record
            cursor.execute('DELETE FROM rentals WHERE id = ?', (rental_id,))

            # If rental was active, make equipment available again
            if status == 'Active':
                cursor.execute('''
                    UPDATE equipment SET status = 'Available' WHERE id = ?
                ''', (equipment_id,))

            # Delete any related inventory items
            cursor.execute('DELETE FROM rental_inventory_items WHERE rental_id = ?', (rental_id,))

            conn.commit()
            print(f"Rental {rental_id} deleted successfully")
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"Error deleting rental: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def search_customers(self, search_term: str) -> List[Dict]:
        """Search customers by name, phone, or national ID"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            search_pattern = f"%{search_term}%"
            cursor.execute('''
                SELECT * FROM customers
                WHERE name LIKE ? OR phone LIKE ? OR national_id LIKE ?
                ORDER BY name
            ''', (search_pattern, search_pattern, search_pattern))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching customers: {e}")
            return []
        finally:
            conn.close()

    # Rental CRUD operations
    def add_rental(self, customer_id: int, equipment_id: int, rental_date: str,
                   return_date: str, price: float, notes: str = "") -> Optional[int]:
        """Add new rental to database"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Check if equipment is available
            cursor.execute('SELECT status FROM equipment WHERE id = ?', (equipment_id,))
            result = cursor.fetchone()

            if not result or result[0] != 'Available':
                print("Equipment is not available for rental")
                return None

            # الحصول على رقم الإيجار التسلسلي
            rental_number = self.get_next_sequence_number('rental_individual')
            if not rental_number:
                print("فشل في الحصول على رقم الإيجار التسلسلي")
                return None

            # Add rental
            cursor.execute('''
                INSERT INTO rentals (customer_id, equipment_id, rental_date, return_date, price, notes, rental_number)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (customer_id, equipment_id, rental_date, return_date, price, notes, rental_number))

            rental_id = cursor.lastrowid

            # Update equipment status to Rented
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Rented', equipment_id))

            conn.commit()
            return rental_id
        except sqlite3.Error as e:
            print(f"Error adding rental: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()

    def update_rental(self, rental_id: int, customer_id: int, equipment_id: int,
                     rental_date: str, return_date: str, price: float, notes: str = "") -> bool:
        """تحديث بيانات الإيجار الفردي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود الإيجار
            cursor.execute('SELECT equipment_id FROM rentals WHERE id = ?', (rental_id,))
            result = cursor.fetchone()
            if not result:
                print(f"الإيجار برقم {rental_id} غير موجود")
                return False

            old_equipment_id = result[0]

            # إذا تم تغيير المعدة، تحقق من توفر المعدة الجديدة
            if old_equipment_id != equipment_id:
                cursor.execute('SELECT status FROM equipment WHERE id = ?', (equipment_id,))
                equipment_result = cursor.fetchone()
                if not equipment_result or equipment_result[0] != 'Available':
                    print("المعدة الجديدة غير متاحة")
                    return False

                # إرجاع المعدة القديمة إلى حالة متاحة
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Available', old_equipment_id))
                # تحديث المعدة الجديدة إلى حالة مؤجرة
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Rented', equipment_id))

            # تحديث بيانات الإيجار
            cursor.execute('''
                UPDATE rentals
                SET customer_id = ?, equipment_id = ?, rental_date = ?,
                    return_date = ?, price = ?, notes = ?
                WHERE id = ?
            ''', (customer_id, equipment_id, rental_date, return_date, price, notes, rental_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تحديث الإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_rentals(self, rental_id: Optional[int] = None, status: Optional[str] = None) -> List[Dict]:
        """Get rental records with customer and equipment details"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT r.id, r.customer_id, r.equipment_id, r.rental_date, r.return_date,
                       r.price, r.status, r.actual_return_date, r.notes, r.rental_number, r.invoice_number,
                       c.name as customer_name, c.phone as customer_phone,
                       e.name as equipment_name, e.model as equipment_model,
                       e.serial_number as equipment_serial
                FROM rentals r
                JOIN customers c ON r.customer_id = c.id
                JOIN equipment e ON r.equipment_id = e.id
            '''

            if rental_id:
                cursor.execute(base_query + ' WHERE r.id = ?', (rental_id,))
            elif status:
                cursor.execute(base_query + ' WHERE r.status = ? ORDER BY r.rental_date DESC', (status,))
            else:
                cursor.execute(base_query + ' ORDER BY r.rental_date DESC')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error retrieving rentals: {e}")
            return []
        finally:
            conn.close()

    def return_equipment(self, rental_id: int, actual_return_date: str) -> bool:
        """Process equipment return"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Get rental details
            cursor.execute('SELECT equipment_id, status FROM rentals WHERE id = ?', (rental_id,))
            result = cursor.fetchone()

            if not result:
                print("Rental not found")
                return False

            equipment_id, current_status = result

            if current_status != 'Active':
                print("Rental is not active")
                return False

            # Update rental status and return date
            cursor.execute('''
                UPDATE rentals
                SET status = 'Returned', actual_return_date = ?
                WHERE id = ?
            ''', (actual_return_date, rental_id))

            # Update equipment status to Available
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Available', equipment_id))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error processing return: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def return_equipment_with_condition(self, rental_id: int, actual_return_date: str, condition: str = 'Good', notes: str = '') -> bool:
        """إرجاع معدة مع تحديد الحالة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على تفاصيل الإيجار
            cursor.execute('''
                SELECT r.equipment_id, r.status, r.customer_id, r.rental_date, r.return_date, r.price,
                       e.price as equipment_price
                FROM rentals r
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE r.id = ?
            ''', (rental_id,))
            result = cursor.fetchone()

            if not result:
                print("الإيجار غير موجود")
                return False

            equipment_id, current_status, customer_id, rental_date, return_date, rental_price, equipment_price = result

            if current_status != 'Active':
                print("الإيجار غير نشط")
                return False

            # تحديث حالة الإيجار وتاريخ الإرجاع
            cursor.execute('''
                UPDATE rentals
                SET status = 'Returned', actual_return_date = ?
                WHERE id = ?
            ''', (actual_return_date, rental_id))

            # تحديث حالة المعدة حسب الحالة المحددة
            if condition == 'Good':
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Available', equipment_id))
            elif condition == 'Damaged':
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Damaged', equipment_id))
            elif condition == 'Lost':
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Lost', equipment_id))

                # إضافة المعدة إلى جدول المعدات المفقودة
                lost_date = datetime.now().strftime('%Y-%m-%d')

                # حساب مبلغ التعويض
                compensation_amount = equipment_price if equipment_price and equipment_price > 0 else rental_price * 2

                cursor.execute('''
                    INSERT INTO lost_equipment (
                        equipment_id, rental_id, customer_id,
                        lost_date, rental_date, expected_return_date, rental_price,
                        compensation_amount, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (equipment_id, rental_id, customer_id,
                      lost_date, rental_date, return_date, rental_price,
                      compensation_amount, notes))
            else:
                # حالة افتراضية للصيانة
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Maintenance', equipment_id))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إرجاع المعدة: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_overdue_rentals(self) -> List[Dict]:
        """Get rentals that are overdue (both individual and multi-rentals)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Get overdue individual rentals
            cursor.execute('''
                SELECT r.*, c.name as customer_name, c.phone as customer_phone,
                       e.name as equipment_name, e.model as equipment_model,
                       'individual' as rental_type, r.id as rental_id
                FROM rentals r
                JOIN customers c ON r.customer_id = c.id
                JOIN equipment e ON r.equipment_id = e.id
                WHERE r.status = 'Active' AND r.return_date < date('now')
            ''')

            individual_rentals = cursor.fetchall()
            columns = [description[0] for description in cursor.description]
            overdue_rentals = [dict(zip(columns, row)) for row in individual_rentals]

            # Get overdue multi-rentals
            cursor.execute('''
                SELECT rg.*, c.name as customer_name, c.phone as customer_phone,
                       COUNT(ri.id) as items_count,
                       GROUP_CONCAT(e.name, ', ') as equipment_names,
                       'multi' as rental_type, rg.id as rental_id
                FROM rental_groups rg
                JOIN customers c ON rg.customer_id = c.id
                LEFT JOIN rental_items ri ON rg.id = ri.rental_group_id
                LEFT JOIN equipment e ON ri.equipment_id = e.id
                WHERE rg.status = 'Active' AND rg.return_date < date('now')
                GROUP BY rg.id
            ''')

            multi_rentals = cursor.fetchall()
            multi_columns = [description[0] for description in cursor.description]

            # Format multi-rentals to match individual rental structure
            for row in multi_rentals:
                multi_rental = dict(zip(multi_columns, row))
                # Format equipment name for display
                equipment_count = multi_rental.get('items_count', 0)
                equipment_names = multi_rental.get('equipment_names', '')
                if equipment_count > 0:
                    multi_rental['equipment_name'] = f"{equipment_count} معدات"
                    if equipment_names:
                        names_list = equipment_names.split(', ')[:2]
                        multi_rental['equipment_name'] += f": {', '.join(names_list)}"
                        if equipment_count > 2:
                            multi_rental['equipment_name'] += f" و {equipment_count - 2} أخرى"
                else:
                    multi_rental['equipment_name'] = "لا توجد معدات"

                multi_rental['equipment_model'] = f"إيجار متعدد (#{multi_rental['id']})"
                overdue_rentals.append(multi_rental)

            # Sort by return date
            overdue_rentals.sort(key=lambda x: x.get('return_date', ''))
            return overdue_rentals

        except sqlite3.Error as e:
            print(f"Error retrieving overdue rentals: {e}")
            return []
        finally:
            conn.close()

    def get_overdue_rentals_unified(self) -> List[Dict]:
        """الحصول على الإيجارات المتأخرة (فردية ومتعددة) في تنسيق موحد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الإيجارات الفردية المتأخرة
            individual_query = '''
                SELECT
                    r.id,
                    r.customer_id,
                    r.equipment_id,
                    r.rental_date,
                    r.return_date,
                    r.price,
                    r.status,
                    r.notes,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    e.name as equipment_name,
                    e.model as equipment_model,
                    e.category as equipment_category,
                    'individual' as rental_type,
                    NULL as rental_group_id,
                    1 as items_count,
                    r.price as total_price,
                    julianday('now') - julianday(r.return_date) as days_overdue
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE r.status = 'Active' AND r.return_date < date('now')
            '''

            # الإيجارات المتعددة المتأخرة
            multi_query = '''
                SELECT
                    rg.id,
                    rg.customer_id,
                    NULL as equipment_id,
                    rg.rental_date,
                    rg.return_date,
                    rg.total_price as price,
                    rg.status,
                    rg.notes,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    GROUP_CONCAT(e.name, ', ') as equipment_name,
                    'متعدد' as equipment_model,
                    'متعدد' as equipment_category,
                    'multi' as rental_type,
                    rg.id as rental_group_id,
                    COUNT(ri.id) as items_count,
                    rg.total_price,
                    julianday('now') - julianday(rg.return_date) as days_overdue
                FROM rental_groups rg
                LEFT JOIN customers c ON rg.customer_id = c.id
                LEFT JOIN rental_items ri ON rg.id = ri.rental_group_id
                LEFT JOIN equipment e ON ri.equipment_id = e.id
                WHERE rg.status = 'Active' AND rg.return_date < date('now')
                GROUP BY rg.id, rg.customer_id, rg.rental_date, rg.return_date,
                         rg.total_price, rg.status, rg.notes,
                         c.name, c.phone
            '''

            # دمج الاستعلامات
            unified_query = f'''
                {individual_query}
                UNION ALL
                {multi_query}
                ORDER BY return_date ASC, days_overdue DESC
            '''

            cursor.execute(unified_query)
            columns = [description[0] for description in cursor.description]
            overdue_rentals = [dict(zip(columns, row)) for row in cursor.fetchall()]

            return overdue_rentals

        except sqlite3.Error as e:
            print(f"Error retrieving unified overdue rentals: {e}")
            return []
        finally:
            conn.close()

    def get_rental_notifications(self):
        """
        جلب إشعارات الإيجارات (المتأخرة والمنتهية قريباً)
        Get rental notifications (overdue and expiring soon)
        """
        try:
            notifications = []

            # إشعارات الإيجارات المتأخرة
            overdue_rentals = self.get_overdue_rentals_unified()
            for rental in overdue_rentals:
                notifications.append({
                    'id': f"overdue_{rental['rental_type']}_{rental['id']}",
                    'type': 'overdue',
                    'type_label': 'متأخر',
                    'title': 'إيجار متأخر',
                    'message': f"الإيجار للعميل {rental['customer_name']} متأخر بـ {int(rental['days_overdue'])} يوم",
                    'date': rental['return_date'],
                    'rental_id': rental['id'],
                    'rental_type': rental['rental_type'],
                    'customer_name': rental['customer_name']
                })

            # إشعارات الإيجارات المنتهية خلال 3 أيام
            conn = self.get_connection()
            cursor = conn.cursor()

            expiring_query = """
                SELECT
                    r.id, r.customer_id, c.name as customer_name, r.return_date,
                    'individual' as rental_type
                FROM rentals r
                JOIN customers c ON r.customer_id = c.id
                WHERE r.status = 'Active'
                AND r.return_date BETWEEN date('now') AND date('now', '+3 days')

                UNION ALL

                SELECT
                    rg.id, rg.customer_id, c.name as customer_name, rg.return_date,
                    'multi' as rental_type
                FROM rental_groups rg
                JOIN customers c ON rg.customer_id = c.id
                WHERE rg.status = 'Active'
                AND rg.return_date BETWEEN date('now') AND date('now', '+3 days')

                ORDER BY return_date ASC
            """

            cursor.execute(expiring_query)
            expiring_results = cursor.fetchall()
            conn.close()

            for row in expiring_results:
                rental_data = {
                    'id': row[0],
                    'customer_id': row[1],
                    'customer_name': row[2],
                    'return_date': row[3],
                    'rental_type': row[4]
                }

                from datetime import datetime, date
                return_date = datetime.strptime(rental_data['return_date'], '%Y-%m-%d').date()
                days_until_expiry = (return_date - date.today()).days

                notifications.append({
                    'id': f"expiring_{rental_data['rental_type']}_{rental_data['id']}",
                    'type': 'expiring',
                    'type_label': 'ينتهي قريباً',
                    'title': 'إيجار ينتهي قريباً',
                    'message': f"إيجار العميل {rental_data['customer_name']} ينتهي خلال {days_until_expiry} يوم",
                    'date': rental_data['return_date'],
                    'rental_id': rental_data['id'],
                    'rental_type': rental_data['rental_type'],
                    'customer_name': rental_data['customer_name']
                })

            # ترتيب الإشعارات حسب الأولوية (المتأخرة أولاً، ثم المنتهية قريباً)
            notifications.sort(key=lambda x: (x['type'] != 'overdue', x['date']))

            return notifications

        except Exception as e:
            print(f"خطأ في جلب إشعارات الإيجارات: {e}")
            return []

    def get_dashboard_stats(self):
        """
        جلب إحصائيات لوحة التحكم
        Get dashboard statistics
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            stats = {}

            # إجمالي المعدات
            cursor.execute("SELECT COUNT(*) FROM equipment")
            stats['total_equipment'] = cursor.fetchone()[0]

            # المعدات المتاحة
            cursor.execute("SELECT COUNT(*) FROM equipment WHERE status = 'Available'")
            stats['available_equipment'] = cursor.fetchone()[0]

            # المعدات المؤجرة
            cursor.execute("SELECT COUNT(*) FROM equipment WHERE status = 'Rented'")
            stats['rented_equipment'] = cursor.fetchone()[0]

            # إجمالي العملاء
            cursor.execute("SELECT COUNT(*) FROM customers")
            stats['total_customers'] = cursor.fetchone()[0]

            # الإيجارات النشطة (فردية + متعددة)
            cursor.execute("SELECT COUNT(*) FROM rentals WHERE status = 'Active'")
            individual_active = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM rental_groups WHERE status = 'Active'")
            multi_active = cursor.fetchone()[0]

            stats['active_rentals'] = individual_active + multi_active

            # الإيجارات المتأخرة
            overdue_rentals = self.get_overdue_rentals_unified()
            stats['overdue_rentals'] = len(overdue_rentals)

            # إجمالي الإيرادات هذا الشهر
            cursor.execute("""
                SELECT COALESCE(SUM(price), 0) FROM rentals
                WHERE strftime('%Y-%m', rental_date) = strftime('%Y-%m', 'now')
                AND status IN ('Active', 'Returned')
            """)
            individual_revenue = cursor.fetchone()[0]

            cursor.execute("""
                SELECT COALESCE(SUM(total_price), 0) FROM rental_groups
                WHERE strftime('%Y-%m', rental_date) = strftime('%Y-%m', 'now')
                AND status IN ('Active', 'Returned')
            """)
            multi_revenue = cursor.fetchone()[0]

            stats['monthly_revenue'] = individual_revenue + multi_revenue

            # إجمالي الإيرادات
            cursor.execute("""
                SELECT COALESCE(SUM(price), 0) FROM rentals
                WHERE status IN ('Active', 'Returned')
            """)
            total_individual_revenue = cursor.fetchone()[0]

            cursor.execute("""
                SELECT COALESCE(SUM(total_price), 0) FROM rental_groups
                WHERE status IN ('Active', 'Returned')
            """)
            total_multi_revenue = cursor.fetchone()[0]

            stats['total_revenue'] = total_individual_revenue + total_multi_revenue

            conn.close()
            return stats

        except Exception as e:
            print(f"خطأ في جلب إحصائيات لوحة التحكم: {e}")
            return {
                'total_equipment': 0,
                'available_equipment': 0,
                'rented_equipment': 0,
                'total_customers': 0,
                'active_rentals': 0,
                'overdue_rentals': 0,
                'monthly_revenue': 0,
                'total_revenue': 0
            }

    def get_rental_history(self, customer_id: Optional[int] = None,
                          equipment_id: Optional[int] = None) -> List[Dict]:
        """Get rental history, optionally filtered by customer or equipment"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT r.*, c.name as customer_name, c.phone as customer_phone,
                       e.name as equipment_name, e.model as equipment_model
                FROM rentals r
                JOIN customers c ON r.customer_id = c.id
                JOIN equipment e ON r.equipment_id = e.id
            '''

            if customer_id:
                cursor.execute(base_query + ' WHERE r.customer_id = ? ORDER BY r.rental_date DESC',
                             (customer_id,))
            elif equipment_id:
                cursor.execute(base_query + ' WHERE r.equipment_id = ? ORDER BY r.rental_date DESC',
                             (equipment_id,))
            else:
                cursor.execute(base_query + ' ORDER BY r.rental_date DESC')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error retrieving rental history: {e}")
            return []
        finally:
            conn.close()

    def create_sample_data(self):
        """Create sample data for testing"""
        # Add sample equipment with categories
        sample_equipment = [
            ("Canon EOS R5", "Canon", "CR5001", "كاميرات", "Available"),
            ("Sony A7R IV", "Sony", "SA7R001", "كاميرات", "Available"),
            ("Nikon Z9", "Nikon", "NZ9001", "كاميرات", "Available"),
            ("Canon 24-70mm f/2.8", "Canon", "CL2470001", "عدسات", "Available"),
            ("Sony 85mm f/1.4", "Sony", "SL85001", "عدسات", "Available"),
            ("Tamron 70-200mm f/2.8", "Tamron", "TL70200001", "عدسات", "Available"),
            ("Godox AD600Pro", "Godox", "GD600001", "معدات إضاءة", "Available"),
            ("Profoto B10", "Profoto", "PF10001", "معدات إضاءة", "Available"),
            ("Softbox 90x90cm", "Generic", "SB90001", "معدات إضاءة", "Available")
        ]

        for name, model, serial, category, status in sample_equipment:
            self.add_equipment(name, model, serial, category, status)

        # Add sample offices
        sample_offices = [
            ("استوديو الضوء الذهبي", "شارع التحرير، القاهرة", "0223456789", "<EMAIL>", "محمد أحمد", "supplier"),
            ("مكتب الرؤية الإبداعية", "شارع الهرم، الجيزة", "0233456789", "<EMAIL>", "سارة محمد", "partner"),
            ("فرع الإسكندرية", "كورنيش الإسكندرية", "0334567890", "<EMAIL>", "أحمد علي", "branch"),
            ("استوديو النور المتقدم", "مدينة نصر، القاهرة", "0225678901", "<EMAIL>", "فاطمة حسن", "supplier"),
            ("مركز التصوير المحترف", "المعادي، القاهرة", "0227890123", "<EMAIL>", "خالد محمود", "partner")
        ]

        conn = self.get_connection()
        cursor = conn.cursor()

        for name, address, phone, email, contact, office_type in sample_offices:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO offices (name, address, phone, email, contact_person, office_type)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (name, address, phone, email, contact, office_type))
            except Exception as e:
                print(f"Error adding office {name}: {e}")

        conn.commit()
        conn.close()

        # Add sample customers
        sample_customers = [
            ("أحمد محمد", "01234567890"),
            ("فاطمة علي", "01987654321"),
            ("محمد حسن", "01122334455")
        ]

        for name, phone in sample_customers:
            self.add_customer(name, phone)

        print("Sample data created successfully")

    # ==================== وظائف إعدادات الشركة ====================

    def get_company_setting(self, setting_key: str) -> Optional[str]:
        """الحصول على إعداد شركة محدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT setting_value FROM company_settings WHERE setting_key = ?', (setting_key,))
            result = cursor.fetchone()
            return result[0] if result else None
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع إعداد الشركة: {e}")
            return None
        finally:
            conn.close()

    def get_all_company_settings(self) -> Dict[str, str]:
        """الحصول على جميع إعدادات الشركة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT setting_key, setting_value FROM company_settings')
            results = cursor.fetchall()
            return {row[0]: row[1] for row in results}
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع إعدادات الشركة: {e}")
            return {}
        finally:
            conn.close()

    def get_company_settings_with_details(self) -> List[Dict]:
        """الحصول على إعدادات الشركة مع التفاصيل"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT setting_key, setting_value, setting_type, description, updated_date
                FROM company_settings
                ORDER BY setting_key
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع تفاصيل إعدادات الشركة: {e}")
            return []
        finally:
            conn.close()

    def update_company_setting(self, setting_key: str, setting_value: str) -> bool:
        """تحديث إعداد شركة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE company_settings
                SET setting_value = ?, updated_date = CURRENT_TIMESTAMP
                WHERE setting_key = ?
            ''', (setting_value, setting_key))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في تحديث إعداد الشركة: {e}")
            return False
        finally:
            conn.close()

    def update_multiple_company_settings(self, settings: Dict[str, str]) -> bool:
        """تحديث عدة إعدادات للشركة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            for setting_key, setting_value in settings.items():
                cursor.execute('''
                    UPDATE company_settings
                    SET setting_value = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE setting_key = ?
                ''', (setting_value, setting_key))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث إعدادات الشركة: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def add_company_setting(self, setting_key: str, setting_value: str,
                           setting_type: str = 'text', description: str = '') -> bool:
        """إضافة إعداد شركة جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO company_settings (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            ''', (setting_key, setting_value, setting_type, description))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            print(f"إعداد الشركة {setting_key} موجود بالفعل")
            return False
        except sqlite3.Error as e:
            print(f"خطأ في إضافة إعداد الشركة: {e}")
            return False
        finally:
            conn.close()

    # ==================== وظائف إدارة المخزن ====================

    # إدارة الموردين
    def add_supplier(self, name: str, contact_person: str = None, phone: str = None,
                    email: str = None, address: str = None, notes: str = None) -> Optional[int]:
        """إضافة مورد جديد - يرجع ID المورد الجديد أو None في حالة الفشل"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO suppliers (name, contact_person, phone, email, address, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, contact_person, phone, email, address, notes))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المورد: {e}")
            return None
        finally:
            conn.close()

    def get_suppliers(self, supplier_id: Optional[int] = None) -> List[Dict]:
        """الحصول على قائمة الموردين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if supplier_id:
                cursor.execute('SELECT * FROM suppliers WHERE id = ?', (supplier_id,))
            else:
                cursor.execute('SELECT * FROM suppliers ORDER BY name')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الموردين: {e}")
            return []
        finally:
            conn.close()

    def update_supplier(self, supplier_id: int, name: str, contact_person: str = None,
                       phone: str = None, email: str = None, address: str = None,
                       notes: str = None) -> bool:
        """تحديث بيانات المورد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE suppliers
                SET name = ?, contact_person = ?, phone = ?, email = ?, address = ?, notes = ?
                WHERE id = ?
            ''', (name, contact_person, phone, email, address, notes, supplier_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في تحديث المورد: {e}")
            return False
        finally:
            conn.close()

    def delete_supplier(self, supplier_id: int) -> bool:
        """حذف مورد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود قطع غيار مرتبطة بالمورد
            cursor.execute('SELECT COUNT(*) FROM inventory_items WHERE supplier_id = ?', (supplier_id,))
            if cursor.fetchone()[0] > 0:
                print("لا يمكن حذف المورد لوجود قطع غيار مرتبطة به")
                return False

            cursor.execute('DELETE FROM suppliers WHERE id = ?', (supplier_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في حذف المورد: {e}")
            return False
        finally:
            conn.close()

    # إدارة فئات المخزون
    def get_inventory_categories(self) -> List[Dict]:
        """الحصول على فئات المخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM inventory_categories ORDER BY name')
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع فئات المخزون: {e}")
            return []
        finally:
            conn.close()

    def add_inventory_category(self, name: str, description: str = None) -> bool:
        """إضافة فئة مخزون جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO inventory_categories (name, description)
                VALUES (?, ?)
            ''', (name, description))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            print(f"فئة المخزون '{name}' موجودة مسبقاً")
            return False
        except sqlite3.Error as e:
            print(f"خطأ في إضافة فئة المخزون: {e}")
            return False
        finally:
            conn.close()

    # إدارة قطع الغيار
    def add_inventory_item(self, name: str, category_id: int, brand: str = None,
                          model: str = None, part_number: str = None, description: str = None,
                          unit_price: float = 0.0, rental_price: float = 0.0,
                          min_stock_level: int = 5, current_stock: int = 0,
                          supplier_id: int = None, expiry_date: str = None,
                          battery_level: int = None, battery_condition: str = None,
                          weight_capacity: float = None, memory_size: int = None,
                          memory_speed: str = None, compatible_equipment: str = None,
                          location: str = None, notes: str = None) -> bool:
        """إضافة قطعة غيار جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO inventory_items (
                    name, category_id, brand, model, part_number, description,
                    unit_price, rental_price, min_stock_level, current_stock,
                    supplier_id, expiry_date, battery_level, battery_condition,
                    weight_capacity, memory_size, memory_speed, compatible_equipment, location, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, category_id, brand, model, part_number, description,
                  unit_price, rental_price, min_stock_level, current_stock,
                  supplier_id, expiry_date, battery_level, battery_condition,
                  weight_capacity, memory_size, memory_speed, compatible_equipment, location, notes))
            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إضافة قطعة الغيار: {e}")
            return False
        finally:
            conn.close()

    def get_inventory_item(self, item_id: int) -> Optional[Dict]:
        """الحصول على عنصر مخزون واحد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT i.*, c.name as category_name, s.name as supplier_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.id
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.id = ?
            '''

            cursor.execute(query, (item_id,))
            columns = [description[0] for description in cursor.description]
            row = cursor.fetchone()

            if row:
                return dict(zip(columns, row))
            return None

        except Exception as e:
            print(f"خطأ في الحصول على عنصر المخزون: {e}")
            return None
        finally:
            conn.close()

    def get_inventory_items(self, item_id: Optional[int] = None, category_id: Optional[int] = None,
                           low_stock_only: bool = False) -> List[Dict]:
        """الحصول على قطع الغيار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT i.*, c.name as category_name, s.name as supplier_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.id
                LEFT JOIN suppliers s ON i.supplier_id = s.id
            '''

            conditions = []
            params = []

            if item_id:
                conditions.append('i.id = ?')
                params.append(item_id)

            if category_id:
                conditions.append('i.category_id = ?')
                params.append(category_id)

            if low_stock_only:
                conditions.append('i.current_stock <= i.min_stock_level')

            if conditions:
                base_query += ' WHERE ' + ' AND '.join(conditions)

            base_query += ' ORDER BY i.name'

            cursor.execute(base_query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع قطع الغيار: {e}")
            return []
        finally:
            conn.close()

    def update_inventory_item(self, item_id: int, name: str, category_id: int,
                             brand: str = None, model: str = None, part_number: str = None,
                             description: str = None, unit_price: float = 0.0,
                             rental_price: float = 0.0, min_stock_level: int = 5,
                             current_stock: int = 0, supplier_id: int = None,
                             expiry_date: str = None, battery_level: int = None,
                             battery_condition: str = None, weight_capacity: float = None,
                             memory_size: int = None, memory_speed: str = None,
                             compatible_equipment: str = None, location: str = None, notes: str = None) -> bool:
        """تحديث قطعة غيار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE inventory_items SET
                    name = ?, category_id = ?, brand = ?, model = ?, part_number = ?,
                    description = ?, unit_price = ?, rental_price = ?, min_stock_level = ?,
                    current_stock = ?, supplier_id = ?, expiry_date = ?, battery_level = ?,
                    battery_condition = ?, weight_capacity = ?, memory_size = ?, memory_speed = ?,
                    compatible_equipment = ?, location = ?, notes = ?
                WHERE id = ?
            ''', (name, category_id, brand, model, part_number, description,
                  unit_price, rental_price, min_stock_level, current_stock,
                  supplier_id, expiry_date, battery_level, battery_condition,
                  weight_capacity, memory_size, memory_speed, compatible_equipment,
                  location, notes, item_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في تحديث قطعة الغيار: {e}")
            return False
        finally:
            conn.close()

    def delete_inventory_item(self, item_id: int) -> bool:
        """حذف قطعة غيار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود حركات مخزون
            cursor.execute('SELECT COUNT(*) FROM inventory_movements WHERE item_id = ?', (item_id,))
            if cursor.fetchone()[0] > 0:
                print("لا يمكن حذف قطعة الغيار لوجود حركات مخزون مرتبطة بها")
                return False

            cursor.execute('DELETE FROM inventory_items WHERE id = ?', (item_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في حذف قطعة الغيار: {e}")
            return False
        finally:
            conn.close()

    def update_inventory_stock(self, item_id: int, new_stock: int) -> bool:
        """تحديث مخزون قطعة غيار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('UPDATE inventory_items SET current_stock = ? WHERE id = ?',
                          (new_stock, item_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"خطأ في تحديث المخزون: {e}")
            return False
        finally:
            conn.close()

    # إدارة حركة المخزون
    def add_inventory_movement(self, item_id: int, movement_type: str, quantity: int,
                              unit_cost: float = None, supplier_id: int = None,
                              reference_number: str = None, notes: str = None,
                              movement_date: str = None) -> bool:
        """إضافة حركة مخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            total_cost = (unit_cost * quantity) if unit_cost else None

            cursor.execute('''
                INSERT INTO inventory_movements (
                    item_id, movement_type, quantity, unit_cost, total_cost,
                    supplier_id, reference_number, notes, movement_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (item_id, movement_type, quantity, unit_cost, total_cost,
                  supplier_id, reference_number, notes, movement_date))

            # تحديث المخزون الحالي
            if movement_type in ['Purchase', 'Return', 'Adjustment']:
                cursor.execute('''
                    UPDATE inventory_items
                    SET current_stock = current_stock + ?
                    WHERE id = ?
                ''', (quantity, item_id))
            elif movement_type in ['Sale', 'Rental', 'Damage', 'Loss']:
                cursor.execute('''
                    UPDATE inventory_items
                    SET current_stock = current_stock - ?
                    WHERE id = ?
                ''', (quantity, item_id))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إضافة حركة المخزون: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_inventory_movements(self, item_id: Optional[int] = None,
                               movement_type: Optional[str] = None,
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None) -> List[Dict]:
        """الحصول على حركات المخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT m.*, i.name as item_name, s.name as supplier_name
                FROM inventory_movements m
                LEFT JOIN inventory_items i ON m.item_id = i.id
                LEFT JOIN suppliers s ON m.supplier_id = s.id
            '''

            conditions = []
            params = []

            if item_id:
                conditions.append('m.item_id = ?')
                params.append(item_id)

            if movement_type:
                conditions.append('m.movement_type = ?')
                params.append(movement_type)

            if start_date:
                conditions.append('m.movement_date >= ?')
                params.append(start_date)

            if end_date:
                conditions.append('m.movement_date <= ?')
                params.append(end_date)

            if conditions:
                base_query += ' WHERE ' + ' AND '.join(conditions)

            base_query += ' ORDER BY m.movement_date DESC, m.created_date DESC'

            cursor.execute(base_query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع حركات المخزون: {e}")
            return []
        finally:
            conn.close()

    def get_low_stock_items(self) -> List[Dict]:
        """الحصول على قطع الغيار ذات المخزون المنخفض"""
        return self.get_inventory_items(low_stock_only=True)

    def search_inventory_items(self, search_term: str) -> List[Dict]:
        """البحث في قطع الغيار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            search_pattern = f"%{search_term}%"
            cursor.execute('''
                SELECT i.*, c.name as category_name, s.name as supplier_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.id
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.name LIKE ? OR i.brand LIKE ? OR i.model LIKE ?
                   OR i.part_number LIKE ? OR i.description LIKE ?
                ORDER BY i.name
            ''', (search_pattern, search_pattern, search_pattern,
                  search_pattern, search_pattern))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في البحث في قطع الغيار: {e}")
            return []
        finally:
            conn.close()

    # ربط قطع الغيار بالإيجارات
    def add_rental_inventory_items(self, rental_id: int, items: List[Dict]) -> bool:
        """إضافة قطع غيار للإيجار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            for item in items:
                item_id = item['item_id']
                quantity = item['quantity']
                rental_price = item.get('rental_price', 0.0)

                # التحقق من توفر المخزون
                cursor.execute('SELECT current_stock FROM inventory_items WHERE id = ?', (item_id,))
                result = cursor.fetchone()
                if not result or result[0] < quantity:
                    print(f"مخزون غير كافي لقطعة الغيار رقم {item_id}")
                    return False

                # إضافة قطعة الغيار للإيجار
                cursor.execute('''
                    INSERT INTO rental_inventory_items (rental_id, item_id, quantity, rental_price)
                    VALUES (?, ?, ?, ?)
                ''', (rental_id, item_id, quantity, rental_price))

                # تسجيل حركة مخزون
                cursor.execute('''
                    INSERT INTO inventory_movements (
                        item_id, movement_type, quantity, reference_number, notes
                    ) VALUES (?, 'Rental', ?, ?, ?)
                ''', (item_id, quantity, f"إيجار رقم {rental_id}",
                      f"تأجير {quantity} قطعة مع الإيجار"))

                # تحديث المخزون
                cursor.execute('''
                    UPDATE inventory_items
                    SET current_stock = current_stock - ?
                    WHERE id = ?
                ''', (quantity, item_id))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إضافة قطع الغيار للإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_rental_inventory_items(self, rental_id: int) -> List[Dict]:
        """الحصول على قطع الغيار المرتبطة بإيجار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT ri.*, i.name as item_name, i.brand, i.model
                FROM rental_inventory_items ri
                LEFT JOIN inventory_items i ON ri.item_id = i.id
                WHERE ri.rental_id = ?
                ORDER BY i.name
            ''', (rental_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع قطع غيار الإيجار: {e}")
            return []
        finally:
            conn.close()

    def return_rental_inventory_items(self, rental_id: int, returned_items: List[Dict]) -> bool:
        """إرجاع قطع غيار الإيجار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            for item in returned_items:
                rental_item_id = item['rental_item_id']
                returned_quantity = item['returned_quantity']
                status = item.get('status', 'Returned')

                # تحديث حالة قطعة الغيار في الإيجار
                cursor.execute('''
                    UPDATE rental_inventory_items
                    SET returned_quantity = ?, status = ?
                    WHERE id = ?
                ''', (returned_quantity, status, rental_item_id))

                # الحصول على معلومات القطعة
                cursor.execute('''
                    SELECT item_id, quantity FROM rental_inventory_items WHERE id = ?
                ''', (rental_item_id,))
                result = cursor.fetchone()

                if result:
                    item_id, original_quantity = result

                    # تسجيل حركة إرجاع في المخزون
                    if status == 'Returned':
                        cursor.execute('''
                            INSERT INTO inventory_movements (
                                item_id, movement_type, quantity, reference_number, notes
                            ) VALUES (?, 'Return', ?, ?, ?)
                        ''', (item_id, returned_quantity, f"إرجاع من إيجار {rental_id}",
                              f"إرجاع {returned_quantity} قطعة من الإيجار"))

                        # تحديث المخزون
                        cursor.execute('''
                            UPDATE inventory_items
                            SET current_stock = current_stock + ?
                            WHERE id = ?
                        ''', (returned_quantity, item_id))

                    elif status in ['Damaged', 'Lost']:
                        # تسجيل حركة تلف أو فقدان
                        movement_type = 'Damage' if status == 'Damaged' else 'Loss'
                        cursor.execute('''
                            INSERT INTO inventory_movements (
                                item_id, movement_type, quantity, reference_number, notes
                            ) VALUES (?, ?, ?, ?, ?)
                        ''', (item_id, movement_type, original_quantity - returned_quantity,
                              f"إيجار {rental_id}", f"تلف/فقدان من الإيجار"))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إرجاع قطع غيار الإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_inventory_statistics(self) -> Dict:
        """الحصول على إحصائيات المخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            stats = {}

            # إجمالي القطع
            cursor.execute('SELECT COUNT(*) FROM inventory_items WHERE status = "Active"')
            stats['total_items'] = cursor.fetchone()[0]

            # قطع المخزون المنخفض
            cursor.execute('''
                SELECT COUNT(*) FROM inventory_items
                WHERE current_stock <= min_stock_level AND current_stock > 0 AND status = "Active"
            ''')
            stats['low_stock_items'] = cursor.fetchone()[0]

            # قطع نفد مخزونها
            cursor.execute('''
                SELECT COUNT(*) FROM inventory_items
                WHERE current_stock = 0 AND status = "Active"
            ''')
            stats['out_of_stock_items'] = cursor.fetchone()[0]

            # قيمة المخزون الإجمالية
            cursor.execute('''
                SELECT SUM(current_stock * unit_price) FROM inventory_items
                WHERE status = "Active"
            ''')
            result = cursor.fetchone()[0]
            stats['total_inventory_value'] = result if result else 0.0

            # عدد الموردين
            cursor.execute('SELECT COUNT(*) FROM suppliers')
            stats['total_suppliers'] = cursor.fetchone()[0]

            # حركات المخزون هذا الشهر
            cursor.execute('''
                SELECT COUNT(*) FROM inventory_movements
                WHERE movement_date >= date('now', 'start of month')
            ''')
            stats['monthly_movements'] = cursor.fetchone()[0]

            return stats
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع إحصائيات المخزون: {e}")
            return {}
        finally:
            conn.close()

    def get_expiring_items(self, days_ahead: int = 30) -> List[Dict]:
        """الحصول على القطع التي ستنتهي صلاحيتها قريباً"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT i.*, c.name as category_name
                FROM inventory_items i
                LEFT JOIN inventory_categories c ON i.category_id = c.id
                WHERE i.expiry_date IS NOT NULL
                  AND i.expiry_date <= date('now', '+{} days')
                  AND i.status = 'Active'
                ORDER BY i.expiry_date
            '''.format(days_ahead))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع القطع منتهية الصلاحية: {e}")
            return []
        finally:
            conn.close()

    def create_inventory_sample_data(self):
        """إنشاء بيانات تجريبية للمخزون"""
        try:
            # إضافة موردين تجريبيين
            suppliers_data = [
                ("شركة الكاميرات المتقدمة", "أحمد محمد", "0501234567", "<EMAIL>", "الرياض", "مورد رئيسي للكاميرات"),
                ("مؤسسة الإكسسوارات الذكية", "فاطمة علي", "0509876543", "<EMAIL>", "جدة", "متخصص في الإكسسوارات"),
                ("متجر قطع الغيار الأصلية", "محمد سالم", "0551122334", "<EMAIL>", "الدمام", "قطع غيار أصلية")
            ]

            for supplier_data in suppliers_data:
                self.add_supplier(*supplier_data)

            # الحصول على معرفات الفئات والموردين
            categories = self.get_inventory_categories()
            suppliers = self.get_suppliers()

            category_map = {cat['name']: cat['id'] for cat in categories}
            supplier_map = {sup['name']: sup['id'] for sup in suppliers}

            # إضافة قطع غيار تجريبية
            inventory_items = [
                # بطاريات
                ("بطارية Canon LP-E6N", category_map.get('بطاريات'), "Canon", "LP-E6N", "LP-E6N-001",
                 "بطارية ليثيوم أيون للكاميرات", 150.0, 25.0, 5, 12, supplier_map.get("شركة الكاميرات المتقدمة"),
                 None, 85, "ممتاز", None, None, None, "Canon EOS R5, R6, 5D Mark IV"),

                ("بطارية Sony NP-FZ100", category_map.get('بطاريات'), "Sony", "NP-FZ100", "NP-FZ100-001",
                 "بطارية عالية السعة", 180.0, 30.0, 3, 8, supplier_map.get("شركة الكاميرات المتقدمة"),
                 None, 90, "ممتاز", None, None, None, "Sony A7R IV, A7 III, A9"),

                # شواحن
                ("شاحن Canon LC-E6", category_map.get('شواحن'), "Canon", "LC-E6", "LC-E6-001",
                 "شاحن سريع للبطاريات", 80.0, 15.0, 2, 5, supplier_map.get("مؤسسة الإكسسوارات الذكية"),
                 None, None, None, None, None, None, "بطاريات Canon LP-E6"),

                # ترايبودات
                ("ترايبود Manfrotto MT055", category_map.get('ترايبودات'), "Manfrotto", "MT055CXPRO3", "MT055-001",
                 "ترايبود كربون فايبر محترف", 1200.0, 80.0, 1, 3, supplier_map.get("مؤسسة الإكسسوارات الذكية"),
                 None, None, None, 8.0, None, None, "جميع الكاميرات"),

                # كروت ذاكرة
                ("كرت ذاكرة SanDisk 64GB", category_map.get('كروت ذاكرة'), "SanDisk", "Extreme Pro", "SD64GB-001",
                 "كرت ذاكرة عالي السرعة", 120.0, 20.0, 5, 15, supplier_map.get("متجر قطع الغيار الأصلية"),
                 None, None, None, None, 64, "170MB/s", "جميع الكاميرات"),

                ("كرت ذاكرة Lexar 128GB", category_map.get('كروت ذاكرة'), "Lexar", "Professional", "LX128GB-001",
                 "كرت ذاكرة احترافي", 200.0, 35.0, 3, 10, supplier_map.get("متجر قطع الغيار الأصلية"),
                 None, None, None, None, 128, "150MB/s", "جميع الكاميرات"),

                # فلاتر
                ("فلتر B+W UV 77mm", category_map.get('فلاتر العدسات'), "B+W", "UV-Haze", "BW77UV-001",
                 "فلتر حماية من الأشعة فوق البنفسجية", 90.0, 15.0, 2, 8, supplier_map.get("مؤسسة الإكسسوارات الذكية"),
                 None, None, None, None, None, None, "عدسات 77mm"),

                # حقائب
                ("حقيبة Lowepro ProTactic", category_map.get('حقائب الحمل'), "Lowepro", "ProTactic 450 AW", "LP450-001",
                 "حقيبة احترافية مقاومة للماء", 350.0, 50.0, 1, 4, supplier_map.get("مؤسسة الإكسسوارات الذكية"),
                 None, None, None, None, None, None, "كاميرات وعدسات متعددة"),

                # كابلات
                ("كابل USB-C إلى USB-A", category_map.get('كابلات وأسلاك'), "Generic", "USB-C-A-3M", "USBC3M-001",
                 "كابل نقل بيانات 3 متر", 25.0, 5.0, 5, 20, supplier_map.get("متجر قطع الغيار الأصلية"),
                 None, None, None, None, None, None, "كاميرات حديثة")
            ]

            for item_data in inventory_items:
                self.add_inventory_item(*item_data)

            print("تم إنشاء بيانات المخزون التجريبية بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء بيانات المخزون التجريبية: {e}")

    def get_rental_inventory_items_by_item(self, item_id: int) -> List[Dict]:
        """الحصول على الإيجارات المرتبطة بعنصر مخزون معين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT ri.*, r.status as rental_status, r.rental_date, r.return_date
                FROM rental_inventory_items ri
                LEFT JOIN rentals r ON ri.rental_id = r.id
                WHERE ri.item_id = ?
                ORDER BY ri.created_date DESC
            ''', (item_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع إيجارات العنصر: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_active_rentals(self, equipment_id: int) -> List[Dict]:
        """الحصول على الإيجارات النشطة للمعدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT r.*, c.name as customer_name
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                WHERE r.equipment_id = ? AND r.status = 'Active'
                ORDER BY r.rental_date DESC
            ''', (equipment_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الإيجارات النشطة للمعدة: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_rentals(self, equipment_id: int) -> List[Dict]:
        """الحصول على جميع إيجارات المعدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT r.*, c.name as customer_name
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                WHERE r.equipment_id = ?
                ORDER BY r.rental_date DESC
            ''', (equipment_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع إيجارات المعدة: {e}")
            return []
        finally:
            conn.close()

    def get_customer_active_rentals(self, customer_id: int) -> List[Dict]:
        """الحصول على الإيجارات النشطة للعميل"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT r.*, e.name as equipment_name, e.model as equipment_model
                FROM rentals r
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE r.customer_id = ? AND r.status = 'Active'
                ORDER BY r.rental_date DESC
            ''', (customer_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الإيجارات النشطة للعميل: {e}")
            return []
        finally:
            conn.close()

    def get_customer_rentals(self, customer_id: int) -> List[Dict]:
        """الحصول على جميع إيجارات العميل"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT r.*, e.name as equipment_name, e.model as equipment_model
                FROM rentals r
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE r.customer_id = ?
                ORDER BY r.rental_date DESC
            ''', (customer_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع إيجارات العميل: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_by_category_stats(self) -> List[Dict]:
        """Get equipment statistics grouped by category"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    category,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'Available' THEN 1 ELSE 0 END) as available_count,
                    SUM(CASE WHEN status = 'Rented' THEN 1 ELSE 0 END) as rented_count,
                    SUM(CASE WHEN status = 'Maintenance' THEN 1 ELSE 0 END) as maintenance_count
                FROM equipment
                GROUP BY category
                ORDER BY category
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error getting equipment category statistics: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_categories(self) -> List[str]:
        """Get list of all equipment categories"""
        return ['كاميرات', 'عدسات', 'معدات إضاءة']

    def get_return_notifications(self) -> Dict:
        """Get equipment return notifications with urgency levels (both individual and multi-rentals)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Get individual rentals notifications
            cursor.execute('''
                SELECT r.*, c.name as customer_name, c.phone as customer_phone,
                       e.name as equipment_name, e.model as equipment_model,
                       e.serial_number as equipment_serial,
                       (julianday('now') - julianday(r.return_date)) as days_overdue,
                       'individual' as rental_type, r.id as rental_id
                FROM rentals r
                JOIN customers c ON r.customer_id = c.id
                JOIN equipment e ON r.equipment_id = e.id
                WHERE r.status = 'Active' AND r.return_date <= date('now', '+1 day')
            ''')

            individual_notifications = cursor.fetchall()
            individual_columns = [description[0] for description in cursor.description]
            notifications_data = [dict(zip(individual_columns, row)) for row in individual_notifications]

            # Get multi-rentals notifications
            cursor.execute('''
                SELECT rg.*, c.name as customer_name, c.phone as customer_phone,
                       COUNT(ri.id) as items_count,
                       GROUP_CONCAT(e.name, ', ') as equipment_names,
                       (julianday('now') - julianday(rg.return_date)) as days_overdue,
                       'multi' as rental_type, rg.id as rental_id
                FROM rental_groups rg
                JOIN customers c ON rg.customer_id = c.id
                LEFT JOIN rental_items ri ON rg.id = ri.rental_group_id
                LEFT JOIN equipment e ON ri.equipment_id = e.id
                WHERE rg.status = 'Active' AND rg.return_date <= date('now', '+1 day')
                GROUP BY rg.id
            ''')

            multi_notifications = cursor.fetchall()
            multi_columns = [description[0] for description in cursor.description]

            # Format multi-rental notifications
            for row in multi_notifications:
                multi_notification = dict(zip(multi_columns, row))
                # Format equipment information for display
                equipment_count = multi_notification.get('items_count', 0)
                equipment_names = multi_notification.get('equipment_names', '')

                if equipment_count > 0:
                    multi_notification['equipment_name'] = f"{equipment_count} معدات"
                    if equipment_names:
                        names_list = equipment_names.split(', ')[:2]
                        multi_notification['equipment_name'] += f": {', '.join(names_list)}"
                        if equipment_count > 2:
                            multi_notification['equipment_name'] += f" و {equipment_count - 2} أخرى"
                else:
                    multi_notification['equipment_name'] = "لا توجد معدات"

                multi_notification['equipment_model'] = f"إيجار متعدد (#{multi_notification['id']})"
                multi_notification['equipment_serial'] = f"MR{multi_notification['id']}"
                notifications_data.append(multi_notification)

            # Categorize notifications by urgency
            due_today = []
            overdue_1_day = []
            overdue_multiple = []
            due_tomorrow = []

            for notification in notifications_data:
                days_overdue = notification['days_overdue']

                if days_overdue >= 1:  # Overdue
                    if days_overdue <= 2:
                        overdue_1_day.append(notification)
                    else:
                        overdue_multiple.append(notification)
                elif days_overdue >= 0:  # Due today
                    due_today.append(notification)
                else:  # Due tomorrow
                    due_tomorrow.append(notification)

            return {
                'due_today': due_today,
                'due_tomorrow': due_tomorrow,
                'overdue_1_day': overdue_1_day,
                'overdue_multiple': overdue_multiple,
                'total_count': len(notifications_data)
            }

        except sqlite3.Error as e:
            print(f"Error getting return notifications: {e}")
            return {
                'due_today': [],
                'due_tomorrow': [],
                'overdue_1_day': [],
                'overdue_multiple': [],
                'total_count': 0
            }
        finally:
            conn.close()

    def auto_update_overdue_status(self) -> int:
        """Automatically update overdue rental status and return count of updated records (both individual and multi-rentals)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Update individual rentals that are overdue
            cursor.execute('''
                UPDATE rentals
                SET status = 'Overdue'
                WHERE status = 'Active' AND return_date < date('now')
            ''')

            individual_updated = cursor.rowcount

            # Update multi-rentals that are overdue
            cursor.execute('''
                UPDATE rental_groups
                SET status = 'Overdue'
                WHERE status = 'Active' AND return_date < date('now')
            ''')

            multi_updated = cursor.rowcount
            total_updated = individual_updated + multi_updated

            conn.commit()
            return total_updated

        except sqlite3.Error as e:
            print(f"Error updating overdue status: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    def update_overdue_rentals(self):
        """Update status of overdue rentals"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE rentals
                SET status = 'Overdue'
                WHERE status = 'Active' AND return_date < date('now')
            ''')
            conn.commit()
            return cursor.rowcount
        except sqlite3.Error as e:
            print(f"Error updating overdue rentals: {e}")
            return 0
        finally:
            conn.close()

    def get_revenue_report(self, start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> Dict:
        """Get revenue report for a date range"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT
                    COUNT(*) as total_rentals,
                    SUM(price) as total_revenue,
                    AVG(price) as average_rental_price,
                    MIN(price) as min_price,
                    MAX(price) as max_price
                FROM rentals
                WHERE status IN ('Returned', 'Active', 'Overdue')
            '''

            if start_date and end_date:
                cursor.execute(base_query + ' AND rental_date BETWEEN ? AND ?',
                             (start_date, end_date))
            elif start_date:
                cursor.execute(base_query + ' AND rental_date >= ?', (start_date,))
            elif end_date:
                cursor.execute(base_query + ' AND rental_date <= ?', (end_date,))
            else:
                cursor.execute(base_query)

            result = cursor.fetchone()
            return {
                'total_rentals': result[0] or 0,
                'total_revenue': result[1] or 0.0,
                'average_rental_price': result[2] or 0.0,
                'min_price': result[3] or 0.0,
                'max_price': result[4] or 0.0
            }
        except sqlite3.Error as e:
            print(f"Error generating revenue report: {e}")
            return {
                'total_rentals': 0,
                'total_revenue': 0.0,
                'average_rental_price': 0.0,
                'min_price': 0.0,
                'max_price': 0.0
            }
        finally:
            conn.close()

    def get_equipment_utilization(self) -> List[Dict]:
        """Get equipment utilization statistics"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    e.id,
                    e.name,
                    e.model,
                    e.serial_number,
                    e.status,
                    COUNT(r.id) as total_rentals,
                    COALESCE(SUM(r.price), 0) as total_revenue,
                    MAX(r.rental_date) as last_rental_date
                FROM equipment e
                LEFT JOIN rentals r ON e.id = r.equipment_id
                GROUP BY e.id, e.name, e.model, e.serial_number, e.status
                ORDER BY total_rentals DESC, total_revenue DESC
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error getting equipment utilization: {e}")
            return []
        finally:
            conn.close()

    def get_customer_statistics(self) -> List[Dict]:
        """Get customer rental statistics"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    c.id,
                    c.name,
                    c.phone,
                    COUNT(r.id) as total_rentals,
                    COALESCE(SUM(r.price), 0) as total_spent,
                    MAX(r.rental_date) as last_rental_date,
                    COUNT(CASE WHEN r.status = 'Active' THEN 1 END) as active_rentals
                FROM customers c
                LEFT JOIN rentals r ON c.id = r.customer_id
                GROUP BY c.id, c.name, c.phone
                ORDER BY total_rentals DESC, total_spent DESC
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error getting customer statistics: {e}")
            return []
        finally:
            conn.close()

    def get_customer_rentals(self, customer_id: int) -> List[Dict]:
        """Get all rentals for a specific customer"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT r.id, r.customer_id, r.equipment_id, r.rental_date, r.return_date,
                       r.price, r.status, r.actual_return_date, r.notes, r.created_date,
                       c.name as customer_name, c.phone as customer_phone,
                       e.name as equipment_name, e.model as equipment_model,
                       e.serial_number as equipment_serial, e.category as equipment_category,
                       CASE
                           WHEN r.status = 'Active' AND date(r.return_date) < date('now') THEN 'Overdue'
                           ELSE r.status
                       END as actual_status,
                       CASE
                           WHEN r.status = 'Active' AND date(r.return_date) < date('now')
                           THEN julianday('now') - julianday(r.return_date)
                           ELSE 0
                       END as days_overdue
                FROM rentals r
                JOIN customers c ON r.customer_id = c.id
                JOIN equipment e ON r.equipment_id = e.id
                WHERE r.customer_id = ?
                ORDER BY r.rental_date DESC
            ''', (customer_id,))

            columns = [description[0] for description in cursor.description]
            rentals = [dict(zip(columns, row)) for row in cursor.fetchall()]

            # تحديث الحالة للإيجارات المتأخرة
            for rental in rentals:
                if rental['actual_status'] == 'Overdue':
                    rental['status'] = 'Overdue'

            return rentals
        except sqlite3.Error as e:
            print(f"Error getting customer rentals: {e}")
            return []
        finally:
            conn.close()

    # ==================== وظائف الإيجارات المتعددة ====================

    def create_multi_rental(self, customer_id: int, rental_date: str, return_date: str,
                           equipment_list: List[Dict], discount_amount: float = 0.0,
                           paid_amount: float = 0.0, notes: str = "", created_by: int = None) -> Optional[int]:
        """إنشاء إيجار متعدد المعدات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من توفر جميع المعدات
            for equipment in equipment_list:
                equipment_id = equipment['equipment_id']
                cursor.execute('SELECT status FROM equipment WHERE id = ?', (equipment_id,))
                result = cursor.fetchone()

                if not result or result[0] != 'Available':
                    print(f"المعدة رقم {equipment_id} غير متاحة للإيجار")
                    return None

            # حساب التكلفة الإجمالية
            total_price = 0.0
            for equipment in equipment_list:
                rental_price = float(equipment.get('rental_price', 0))
                days_count = int(equipment.get('days_count', 1))
                total_price += rental_price * days_count

            # تطبيق الخصم
            total_after_discount = total_price - discount_amount
            remaining_amount = total_after_discount - paid_amount

            # تحديد حالة الدفع
            if paid_amount >= total_after_discount:
                payment_status = "Paid"
                remaining_amount = 0.0
            elif paid_amount > 0:
                payment_status = "Partial"
            else:
                payment_status = "Pending"

            # الحصول على رقم الإيجار المتعدد التسلسلي
            rental_number = self.get_next_sequence_number('rental_multi')
            if not rental_number:
                print("فشل في الحصول على رقم الإيجار المتعدد التسلسلي")
                return None

            # إنشاء مجموعة الإيجار
            cursor.execute('''
                INSERT INTO rental_groups (
                    customer_id, rental_date, return_date, total_price,
                    discount_amount, paid_amount, remaining_amount, payment_status,
                    notes, created_by, rental_number
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (customer_id, rental_date, return_date, total_price,
                  discount_amount, paid_amount, remaining_amount, payment_status,
                  notes, created_by, rental_number))

            rental_group_id = cursor.lastrowid

            # إضافة عناصر الإيجار
            for equipment in equipment_list:
                equipment_id = equipment['equipment_id']
                rental_price = float(equipment.get('rental_price', 0))
                days_count = int(equipment.get('days_count', 1))
                subtotal = rental_price * days_count

                cursor.execute('''
                    INSERT INTO rental_items (rental_group_id, equipment_id, rental_price, days_count, subtotal)
                    VALUES (?, ?, ?, ?, ?)
                ''', (rental_group_id, equipment_id, rental_price, days_count, subtotal))

                # تحديث حالة المعدة إلى مؤجرة
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Rented', equipment_id))

            conn.commit()
            return rental_group_id

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الإيجار المتعدد: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()

    def get_multi_rentals(self, rental_group_id: Optional[int] = None, status: Optional[str] = None) -> List[Dict]:
        """الحصول على الإيجارات المتعددة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT rg.*, c.name as customer_name, c.phone as customer_phone,
                       COUNT(ri.id) as items_count
                FROM rental_groups rg
                JOIN customers c ON rg.customer_id = c.id
                LEFT JOIN rental_items ri ON rg.id = ri.rental_group_id
            '''

            conditions = []
            params = []

            if rental_group_id:
                conditions.append('rg.id = ?')
                params.append(rental_group_id)

            if status:
                conditions.append('rg.status = ?')
                params.append(status)

            if conditions:
                base_query += ' WHERE ' + ' AND '.join(conditions)

            base_query += ' GROUP BY rg.id ORDER BY rg.rental_date DESC'

            cursor.execute(base_query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الإيجارات المتعددة: {e}")
            return []
        finally:
            conn.close()

    def get_rental_items(self, rental_group_id: int) -> List[Dict]:
        """الحصول على عناصر إيجار محدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT ri.*, e.name as equipment_name, e.model as equipment_model,
                       e.serial_number as equipment_serial, e.category as equipment_category
                FROM rental_items ri
                JOIN equipment e ON ri.equipment_id = e.id
                WHERE ri.rental_group_id = ?
                ORDER BY e.name
            ''', (rental_group_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في استرجاع عناصر الإيجار: {e}")
            return []
        finally:
            conn.close()

    def return_multi_rental(self, rental_group_id: int, returned_items: List[Dict]) -> bool:
        """إرجاع إيجار متعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # تحقق من وجود مجموعة الإيجار قبل المتابعة
        cursor.execute('SELECT id FROM rental_groups WHERE id = ?', (rental_group_id,))
        group_exists = cursor.fetchone()
        if not group_exists:
            print(f"تحذير: لم يتم العثور على مجموعة الإيجار برقم {rental_group_id}")
            conn.close()
            return False

        try:
            for item in returned_items:
                item_id = item['item_id']
                condition = item.get('condition', 'Good')
                notes = item.get('notes', '')

                # تحديث حالة العنصر
                cursor.execute('''
                    UPDATE rental_items
                    SET status = 'Returned', returned_date = CURRENT_TIMESTAMP,
                        condition_on_return = ?, notes = ?
                    WHERE id = ?
                ''', (condition, notes, item_id))

                # الحصول على معرف المعدة مع التحقق من وجودها
                cursor.execute('SELECT equipment_id FROM rental_items WHERE id = ?', (item_id,))
                equipment_row = cursor.fetchone()
                if not equipment_row:
                    print(f"تحذير: لم يتم العثور على عنصر الإيجار برقم {item_id}")
                    continue
                equipment_id = equipment_row[0]

                # تحديث حالة المعدة حسب الحالة المحددة
                if condition == 'Good':
                    cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Available', equipment_id))
                elif condition == 'Damaged':
                    cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Damaged', equipment_id))
                elif condition == 'Lost':
                    cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Lost', equipment_id))

                    # إضافة المعدة إلى جدول المعدات المفقودة
                    cursor.execute('''
                        SELECT rg.customer_id, rg.rental_date, rg.return_date, ri.rental_price
                        FROM rental_groups rg
                        JOIN rental_items ri ON rg.id = ri.rental_group_id
                        WHERE rg.id = ? AND ri.id = ?
                    ''', (rental_group_id, item_id))

                    rental_info = cursor.fetchone()
                    if rental_info:
                        customer_id, rental_date, return_date, rental_price = rental_info
                        lost_date = datetime.now().strftime('%Y-%m-%d')

                        # حساب مبلغ التعويض (سعر المعدة أو قيمة افتراضية)
                        cursor.execute('SELECT price FROM equipment WHERE id = ?', (equipment_id,))
                        equipment_price = cursor.fetchone()
                        compensation_amount = equipment_price[0] if equipment_price and equipment_price[0] > 0 else rental_price * 2

                        cursor.execute('''
                            INSERT INTO lost_equipment (
                                equipment_id, rental_group_id, customer_id,
                                lost_date, rental_date, expected_return_date, rental_price,
                                compensation_amount, notes
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (equipment_id, rental_group_id, customer_id,
                              lost_date, rental_date, return_date, rental_price,
                              compensation_amount, notes))
                else:
                    # حالة افتراضية للصيانة
                    cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Maintenance', equipment_id))

            # التحقق من إرجاع جميع العناصر
            cursor.execute('''
                SELECT COUNT(*) FROM rental_items
                WHERE rental_group_id = ? AND status != 'Returned'
            ''', (rental_group_id,))

            remaining_items = cursor.fetchone()[0]

            if remaining_items == 0:
                # تحديث حالة مجموعة الإيجار
                cursor.execute('''
                    UPDATE rental_groups
                    SET status = 'Returned', updated_date = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (rental_group_id,))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في إرجاع الإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_available_equipment_for_rental(self, rental_date: str, return_date: str) -> List[Dict]:
        """الحصول على المعدات المتاحة للإيجار في فترة محددة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT * FROM equipment
                WHERE status = 'Available'
                AND id NOT IN (
                    SELECT DISTINCT ri.equipment_id
                    FROM rental_items ri
                    JOIN rental_groups rg ON ri.rental_group_id = rg.id
                    WHERE rg.status = 'Active'
                    AND (
                        (rg.rental_date <= ? AND rg.return_date >= ?) OR
                        (rg.rental_date <= ? AND rg.return_date >= ?) OR
                        (rg.rental_date >= ? AND rg.return_date <= ?)
                    )
                )
                ORDER BY category, name
            ''', (rental_date, rental_date, return_date, return_date, rental_date, return_date))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المعدات المتاحة: {e}")
            return []
        finally:
            conn.close()

    def update_rental_payment(self, rental_group_id: int, paid_amount: float, discount_amount: float = None) -> bool:
        """تحديث دفعة الإيجار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على بيانات الإيجار الحالية
            cursor.execute('''
                SELECT total_price, discount_amount, paid_amount
                FROM rental_groups
                WHERE id = ?
            ''', (rental_group_id,))

            result = cursor.fetchone()
            if not result:
                return False

            current_total, current_discount, current_paid = result

            # تحديث الخصم إذا تم تمريره
            if discount_amount is not None:
                current_discount = discount_amount

            # حساب المبلغ الجديد
            total_after_discount = current_total - current_discount
            new_paid = current_paid + paid_amount
            remaining_amount = total_after_discount - new_paid

            # تحديد حالة الدفع
            if new_paid >= total_after_discount:
                payment_status = "Paid"
                remaining_amount = 0.0
            elif new_paid > 0:
                payment_status = "Partial"
            else:
                payment_status = "Pending"

            # تحديث قاعدة البيانات
            cursor.execute('''
                UPDATE rental_groups
                SET discount_amount = ?, paid_amount = ?, remaining_amount = ?,
                    payment_status = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (current_discount, new_paid, remaining_amount, payment_status, rental_group_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تحديث الدفع: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_multi_rental_items(self, rental_group_id: int) -> List[Dict]:
        """الحصول على عناصر الإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT ri.*, e.name as equipment_name, e.model as equipment_model,
                       e.category as equipment_category
                FROM rental_items ri
                LEFT JOIN equipment e ON ri.equipment_id = e.id
                WHERE ri.rental_group_id = ?
                ORDER BY ri.id
            ''', (rental_group_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على عناصر الإيجار المتعدد: {e}")
            return []
        finally:
            conn.close()

    def get_all_rentals_unified(self, status: Optional[str] = None, customer_id: Optional[int] = None) -> List[Dict]:
        """الحصول على جميع الإيجارات في قائمة موحدة (نظام الخطوات المتعدد فقط)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الإيجارات (نظام الخطوات المتعدد فقط)
            query = '''
                SELECT
                    rg.id,
                    rg.customer_id,
                    NULL as equipment_id,
                    rg.rental_date,
                    rg.return_date,
                    rg.actual_return_date,
                    rg.total_price as price,
                    rg.status,
                    rg.notes,
                    rg.created_date as created_at,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    CASE
                        WHEN COUNT(ri.id) = 1 THEN
                            (SELECT e.name FROM equipment e WHERE e.id = ri.equipment_id LIMIT 1)
                        ELSE
                            CAST(COUNT(ri.id) AS TEXT) || ' معدات: ' ||
                            GROUP_CONCAT(e.name, ', ')
                    END as equipment_name,
                    NULL as equipment_model,
                    'متعدد' as equipment_category,
                    rg.id as rental_group_id,
                    COUNT(ri.id) as items_count,
                    rg.total_price,
                    rg.rental_number
                FROM rental_groups rg
                LEFT JOIN customers c ON rg.customer_id = c.id
                LEFT JOIN rental_items ri ON rg.id = ri.rental_group_id
                LEFT JOIN equipment e ON ri.equipment_id = e.id
                GROUP BY rg.id, rg.customer_id, rg.rental_date, rg.return_date,
                         rg.actual_return_date, rg.total_price, rg.status, rg.notes, rg.created_date,
                         c.name, c.phone, rg.rental_number
            '''

            # إضافة شروط التصفية
            params = []

            if status:
                if status == 'Active':
                    # للإيجارات النشطة، نستبعد المفقودة والمُرجعة
                    query += ' HAVING rg.status IN (?, ?)'
                    params.extend(['Active', 'Overdue'])
                else:
                    query += ' HAVING rg.status = ?'
                    params.append(status)

            if customer_id:
                if status:
                    query += ' AND rg.customer_id = ?'
                else:
                    query += ' HAVING rg.customer_id = ?'
                params.append(customer_id)

            # ترتيب النتائج
            query += ' ORDER BY rg.rental_date DESC, rg.created_date DESC'

            cursor.execute(query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على الإيجارات الموحدة: {e}")
            return []
        finally:
            conn.close()

    def update_multi_rental(self, rental_group_id: int, customer_id: int,
                           rental_date: str, return_date: str, notes: str = "") -> bool:
        """تحديث بيانات الإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # حساب إجمالي السعر الجديد
            cursor.execute('''
                SELECT SUM(subtotal) as total_price
                FROM rental_items
                WHERE rental_group_id = ?
            ''', (rental_group_id,))

            result = cursor.fetchone()
            total_price = result[0] if result and result[0] else 0

            # تحديث الإيجار المتعدد
            cursor.execute('''
                UPDATE rental_groups
                SET customer_id = ?, rental_date = ?, return_date = ?,
                    notes = ?, total_price = ?
                WHERE id = ?
            ''', (customer_id, rental_date, return_date, notes, total_price, rental_group_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تحديث الإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def add_multi_rental_item(self, rental_group_id: int, equipment_id: int,
                             daily_price: float, days: int, subtotal: float) -> bool:
        """إضافة معدة للإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من توفر المعدة
            cursor.execute('SELECT status FROM equipment WHERE id = ?', (equipment_id,))
            equipment = cursor.fetchone()

            if not equipment or equipment[0] != 'Available':
                return False

            # إضافة العنصر
            cursor.execute('''
                INSERT INTO rental_items (rental_group_id, equipment_id, daily_price, days, subtotal)
                VALUES (?, ?, ?, ?, ?)
            ''', (rental_group_id, equipment_id, daily_price, days, subtotal))

            # تحديث حالة المعدة
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Rented', equipment_id))

            # تحديث إجمالي السعر
            cursor.execute('''
                UPDATE rental_groups
                SET total_price = (
                    SELECT SUM(subtotal) FROM rental_items WHERE rental_group_id = ?
                )
                WHERE id = ?
            ''', (rental_group_id, rental_group_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في إضافة معدة للإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def update_multi_rental_item(self, item_id: int, daily_price: float = None,
                                days: int = None, subtotal: float = None) -> bool:
        """تحديث معدة في الإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على البيانات الحالية
            cursor.execute('SELECT * FROM rental_items WHERE id = ?', (item_id,))
            current_item = cursor.fetchone()

            if not current_item:
                return False

            # تحويل النتيجة إلى dictionary
            columns = [description[0] for description in cursor.description]
            current_item_dict = dict(zip(columns, current_item))

            # تحديث البيانات
            new_daily_price = daily_price if daily_price is not None else current_item_dict['daily_price']
            new_days = days if days is not None else current_item_dict['days']
            new_subtotal = subtotal if subtotal is not None else (new_daily_price * new_days)

            cursor.execute('''
                UPDATE rental_items
                SET daily_price = ?, days = ?, subtotal = ?
                WHERE id = ?
            ''', (new_daily_price, new_days, new_subtotal, item_id))

            # تحديث إجمالي السعر
            rental_group_id = current_item_dict['rental_group_id']
            cursor.execute('''
                UPDATE rental_groups
                SET total_price = (
                    SELECT SUM(subtotal) FROM rental_items WHERE rental_group_id = ?
                )
                WHERE id = ?
            ''', (rental_group_id, rental_group_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تحديث معدة الإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def remove_multi_rental_item(self, item_id: int) -> bool:
        """حذف معدة من الإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على معلومات العنصر
            cursor.execute('SELECT * FROM rental_items WHERE id = ?', (item_id,))
            item = cursor.fetchone()

            if not item:
                return False

            # تحويل النتيجة إلى dictionary
            columns = [description[0] for description in cursor.description]
            item_dict = dict(zip(columns, item))

            # إرجاع المعدة لحالة متاحة
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?',
                         ('Available', item_dict['equipment_id']))

            # حذف العنصر
            cursor.execute('DELETE FROM rental_items WHERE id = ?', (item_id,))

            # تحديث إجمالي السعر
            rental_group_id = item_dict['rental_group_id']
            cursor.execute('''
                UPDATE rental_groups
                SET total_price = (
                    SELECT COALESCE(SUM(subtotal), 0) FROM rental_items WHERE rental_group_id = ?
                )
                WHERE id = ?
            ''', (rental_group_id, rental_group_id))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في حذف معدة من الإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    # ==================== وظائف الفواتير ====================

    def get_all_invoices(self, search: Optional[str] = None, invoice_type: Optional[str] = None,
                        date_from: Optional[str] = None, date_to: Optional[str] = None) -> List[Dict]:
        """الحصول على جميع الفواتير (فردية ومتعددة) مع إمكانية البحث والتصفية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الفواتير الفردية
            individual_query = '''
                SELECT
                    r.id,
                    r.customer_id,
                    r.equipment_id,
                    r.rental_date,
                    r.return_date,
                    r.price as total_amount,
                    r.status,
                    r.notes,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    e.name as equipment_name,
                    e.model as equipment_model,
                    e.serial_number as equipment_serial,
                    'individual' as rental_type,
                    1 as items_count
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE 1=1
            '''

            # الفواتير المتعددة
            multi_query = '''
                SELECT
                    rg.id,
                    rg.customer_id,
                    NULL as equipment_id,
                    rg.rental_date,
                    rg.return_date,
                    rg.total_price as total_amount,
                    rg.status,
                    rg.notes,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    'إيجار متعدد' as equipment_name,
                    NULL as equipment_model,
                    NULL as equipment_serial,
                    'multi' as rental_type,
                    COUNT(ri.id) as items_count
                FROM rental_groups rg
                LEFT JOIN customers c ON rg.customer_id = c.id
                LEFT JOIN rental_items ri ON rg.id = ri.rental_group_id
                WHERE 1=1
                GROUP BY rg.id, rg.customer_id, rg.rental_date, rg.return_date,
                         rg.total_price, rg.status, rg.notes, c.name, c.phone
            '''

            # إضافة شروط البحث والتصفية
            individual_params = []
            multi_params = []

            if search:
                individual_query += " AND (c.name LIKE ? OR CAST(r.id AS TEXT) LIKE ?)"
                multi_query += " AND (c.name LIKE ? OR CAST(rg.id AS TEXT) LIKE ?)"
                search_param = f"%{search}%"
                individual_params.extend([search_param, search_param])
                multi_params.extend([search_param, search_param])

            if date_from:
                individual_query += " AND r.rental_date >= ?"
                multi_query += " AND rg.rental_date >= ?"
                individual_params.append(date_from)
                multi_params.append(date_from)

            if date_to:
                individual_query += " AND r.rental_date <= ?"
                multi_query += " AND rg.rental_date <= ?"
                individual_params.append(date_to)
                multi_params.append(date_to)

            # تنفيذ الاستعلام حسب نوع الفاتورة
            invoices = []

            if invoice_type == 'individual' or invoice_type is None:
                cursor.execute(individual_query + " ORDER BY r.rental_date DESC", individual_params)
                columns = [description[0] for description in cursor.description]
                invoices.extend([dict(zip(columns, row)) for row in cursor.fetchall()])

            if invoice_type == 'multi' or invoice_type is None:
                cursor.execute(multi_query + " ORDER BY rg.rental_date DESC", multi_params)
                columns = [description[0] for description in cursor.description]
                invoices.extend([dict(zip(columns, row)) for row in cursor.fetchall()])

            # ترتيب النتائج حسب التاريخ
            invoices.sort(key=lambda x: x['rental_date'], reverse=True)
            return invoices

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على الفواتير: {e}")
            return []
        finally:
            conn.close()

    def get_invoice_statistics(self) -> Dict:
        """الحصول على إحصائيات الفواتير"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # إحصائيات الفواتير الفردية
            cursor.execute('''
                SELECT COUNT(*) as count, COALESCE(SUM(price), 0) as total
                FROM rentals
                WHERE rental_date >= date('now', 'start of month')
            ''')
            individual_monthly = cursor.fetchone()

            cursor.execute('''
                SELECT COUNT(*) as count, COALESCE(SUM(price), 0) as total
                FROM rentals
            ''')
            individual_total = cursor.fetchone()

            # إحصائيات الفواتير المتعددة
            cursor.execute('''
                SELECT COUNT(*) as count, COALESCE(SUM(total_price), 0) as total
                FROM rental_groups
                WHERE rental_date >= date('now', 'start of month')
            ''')
            multi_monthly = cursor.fetchone()

            cursor.execute('''
                SELECT COUNT(*) as count, COALESCE(SUM(total_price), 0) as total
                FROM rental_groups
            ''')
            multi_total = cursor.fetchone()

            return {
                'total_invoices': individual_total[0] + multi_total[0],
                'monthly_invoices': individual_monthly[0] + multi_monthly[0],
                'total_revenue': individual_total[1] + multi_total[1],
                'monthly_revenue': individual_monthly[1] + multi_monthly[1],
                'individual_invoices': individual_total[0],
                'multi_invoices': multi_total[0]
            }

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على إحصائيات الفواتير: {e}")
            return {
                'total_invoices': 0,
                'monthly_invoices': 0,
                'total_revenue': 0,
                'monthly_revenue': 0,
                'individual_invoices': 0,
                'multi_invoices': 0
            }
        finally:
            conn.close()

    # ==================== وظائف الصلاحيات المتقدمة ====================

    def get_permission_definitions(self) -> List[Dict]:
        """الحصول على تعريفات جميع الصلاحيات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT * FROM permission_definitions
                ORDER BY category, permission_name
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع تعريفات الصلاحيات: {e}")
            return []
        finally:
            conn.close()

    def get_user_permissions(self, user_id: int) -> Dict[str, bool]:
        """الحصول على صلاحيات مستخدم محدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT permission_key, permission_value
                FROM user_permissions
                WHERE user_id = ?
            ''', (user_id,))

            permissions = {}
            for row in cursor.fetchall():
                permissions[row[0]] = bool(row[1])

            return permissions
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع صلاحيات المستخدم: {e}")
            return {}
        finally:
            conn.close()

    def get_user_permissions_detailed(self, user_id: int) -> List[Dict]:
        """الحصول على صلاحيات مستخدم مع التفاصيل"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    pd.permission_key,
                    pd.permission_name,
                    pd.permission_description,
                    pd.category,
                    pd.is_manager_only,
                    COALESCE(up.permission_value, 0) as has_permission,
                    up.granted_date,
                    u.username as granted_by_username
                FROM permission_definitions pd
                LEFT JOIN user_permissions up ON pd.permission_key = up.permission_key AND up.user_id = ?
                LEFT JOIN users u ON up.granted_by = u.id
                ORDER BY pd.category, pd.permission_name
            ''', (user_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع تفاصيل صلاحيات المستخدم: {e}")
            return []
        finally:
            conn.close()

    def set_user_permission(self, user_id: int, permission_key: str,
                           has_permission: bool, granted_by: int) -> bool:
        """تعيين صلاحية محددة لمستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT OR REPLACE INTO user_permissions
                (user_id, permission_key, permission_value, granted_by, updated_date)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (user_id, permission_key, has_permission, granted_by))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تعيين صلاحية المستخدم: {e}")
            return False
        finally:
            conn.close()

    def set_multiple_user_permissions(self, user_id: int, permissions: Dict[str, bool],
                                    granted_by: int) -> bool:
        """تعيين عدة صلاحيات لمستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            for permission_key, has_permission in permissions.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO user_permissions
                    (user_id, permission_key, permission_value, granted_by, updated_date)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (user_id, permission_key, has_permission, granted_by))

            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تعيين صلاحيات المستخدم: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def check_user_permission(self, user_id: int, permission_key: str) -> bool:
        """التحقق من وجود صلاحية محددة للمستخدم"""
        user_permissions = self.get_user_permissions(user_id)
        return user_permissions.get(permission_key, False)

    def create_sequence_tables(self, cursor):
        """إنشاء جداول الترقيم التسلسلي"""
        try:
            # جدول الأرقام التسلسلية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sequence_numbers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sequence_type TEXT NOT NULL UNIQUE,
                    current_number INTEGER NOT NULL DEFAULT 1000,
                    prefix TEXT DEFAULT '',
                    suffix TEXT DEFAULT '',
                    description TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CHECK (sequence_type IN ('rental_individual', 'rental_multi', 'invoice'))
                )
            ''')

            # إدراج القيم الافتراضية للأرقام التسلسلية
            cursor.execute('''
                INSERT OR IGNORE INTO sequence_numbers
                (sequence_type, current_number, prefix, description)
                VALUES
                ('rental_individual', 1000, 'R-', 'أرقام الإيجارات الفردية'),
                ('rental_multi', 2000, 'MR-', 'أرقام الإيجارات المتعددة'),
                ('invoice', 3000, 'INV-', 'أرقام الفواتير')
            ''')

            # إضافة حقول الأرقام التسلسلية للجداول الموجودة
            try:
                cursor.execute('ALTER TABLE rentals ADD COLUMN rental_number TEXT UNIQUE')
                print("تم إضافة حقل rental_number لجدول الإيجارات الفردية")
            except sqlite3.OperationalError:
                pass  # الحقل موجود بالفعل

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN rental_number TEXT UNIQUE')
                print("تم إضافة حقل rental_number لجدول الإيجارات المتعددة")
            except sqlite3.OperationalError:
                pass  # الحقل موجود بالفعل

            try:
                cursor.execute('ALTER TABLE rentals ADD COLUMN invoice_number TEXT')
                print("تم إضافة حقل invoice_number لجدول الإيجارات الفردية")
            except sqlite3.OperationalError:
                pass  # الحقل موجود بالفعل

            try:
                cursor.execute('ALTER TABLE rental_groups ADD COLUMN invoice_number TEXT')
                print("تم إضافة حقل invoice_number لجدول الإيجارات المتعددة")
            except sqlite3.OperationalError:
                pass  # الحقل موجود بالفعل

            print("تم إنشاء جداول الترقيم التسلسلي بنجاح")

        except Exception as e:
            print(f"خطأ في إنشاء جداول الترقيم التسلسلي: {e}")
            raise

    def get_next_sequence_number(self, sequence_type: str) -> str:
        """الحصول على الرقم التسلسلي التالي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على الرقم الحالي والبادئة
            cursor.execute('''
                SELECT current_number, prefix, suffix
                FROM sequence_numbers
                WHERE sequence_type = ?
            ''', (sequence_type,))

            result = cursor.fetchone()
            if not result:
                raise ValueError(f"نوع الترقيم غير موجود: {sequence_type}")

            current_number, prefix, suffix = result
            next_number = current_number + 1

            # تحديث الرقم في قاعدة البيانات
            cursor.execute('''
                UPDATE sequence_numbers
                SET current_number = ?, updated_date = CURRENT_TIMESTAMP
                WHERE sequence_type = ?
            ''', (next_number, sequence_type))

            conn.commit()

            # تكوين الرقم النهائي
            sequence_number = f"{prefix}{next_number:04d}{suffix}"
            return sequence_number

        except Exception as e:
            print(f"خطأ في الحصول على الرقم التسلسلي: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()

    def add_equipment_return_log(self, return_data: Dict) -> bool:
        """إضافة سجل إرجاع المعدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO equipment_return_log (
                    rental_id, equipment_id, return_date, condition, notes, returned_by
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                return_data.get('rental_id'),
                return_data.get('equipment_id'),
                return_data.get('return_date'),
                return_data.get('condition'),
                return_data.get('notes', ''),
                return_data.get('returned_by')
            ))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في إضافة سجل إرجاع المعدة: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def update_rental_status(self, rental_id: int, status: str, return_date: str = None) -> bool:
        """تحديث حالة الإيجار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if return_date:
                cursor.execute('''
                    UPDATE rentals
                    SET status = ?, actual_return_date = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, return_date, rental_id))
            else:
                cursor.execute('''
                    UPDATE rentals
                    SET status = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, rental_id))

            conn.commit()
            return cursor.rowcount > 0

        except sqlite3.Error as e:
            print(f"خطأ في تحديث حالة الإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def update_multi_rental_status(self, rental_group_id: int, status: str, return_date: str = None) -> bool:
        """تحديث حالة الإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if return_date:
                cursor.execute('''
                    UPDATE rental_groups
                    SET status = ?, actual_return_date = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, return_date, rental_group_id))
            else:
                cursor.execute('''
                    UPDATE rental_groups
                    SET status = ?, updated_date = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (status, rental_group_id))

            conn.commit()
            return cursor.rowcount > 0

        except sqlite3.Error as e:
            print(f"خطأ في تحديث حالة الإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_rental_by_id(self, rental_id: int) -> Dict:
        """الحصول على إيجار بالمعرف (نظام الخطوات المتعدد فقط)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # البحث في الإيجارات (نظام الخطوات المتعدد)
            cursor.execute('''
                SELECT rg.*
                FROM rental_groups rg
                WHERE rg.id = ?
            ''', (rental_id,))

            result = cursor.fetchone()
            if result:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, result))

            return None

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على الإيجار: {e}")
            return None
        finally:
            conn.close()

    def get_customer_by_id(self, customer_id: int) -> Dict:
        """الحصول على عميل بالمعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
            result = cursor.fetchone()

            if result:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, result))

            return None

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على العميل: {e}")
            return None
        finally:
            conn.close()

    # ==================== المعدات المفقودة ====================

    def add_lost_equipment(self, lost_data: Dict) -> bool:
        """إضافة معدة مفقودة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            lost_date = lost_data.get('loss_date', datetime.now().strftime('%Y-%m-%d'))

            cursor.execute('''
                INSERT INTO lost_equipment (
                    equipment_id, rental_id, rental_group_id, customer_id,
                    lost_date, rental_date, expected_return_date, rental_price,
                    compensation_amount, notes, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                lost_data.get('equipment_id'),
                lost_data.get('rental_id'),
                lost_data.get('rental_group_id'),
                lost_data.get('customer_id'),
                lost_date,
                lost_data.get('rental_date'),
                lost_data.get('expected_return_date'),
                lost_data.get('rental_price', 0.0),
                lost_data.get('replacement_cost', 0.0),
                lost_data.get('description', ''),
                lost_data.get('status', 'Lost')
            ))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في إضافة المعدة المفقودة: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_lost_equipment(self, status: str = None) -> List[Dict]:
        """الحصول على المعدات المفقودة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT
                    le.*,
                    e.name as equipment_name,
                    e.model as equipment_model,
                    e.serial_number as equipment_serial,
                    e.category as equipment_category,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    c.national_id as customer_national_id,
                    julianday('now') - julianday(le.lost_date) as days_lost
                FROM lost_equipment le
                LEFT JOIN equipment e ON le.equipment_id = e.id
                LEFT JOIN customers c ON le.customer_id = c.id
            '''

            params = []
            if status:
                query += ' WHERE le.status = ?'
                params.append(status)

            query += ' ORDER BY le.lost_date DESC'

            cursor.execute(query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المعدات المفقودة: {e}")
            return []
        finally:
            conn.close()

    def return_rental_quick(self, rental_id: int, return_date: str, condition: str = 'good') -> bool:
        """إرجاع سريع للإيجار الفردي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على معلومات الإيجار
            cursor.execute('SELECT equipment_id, status FROM rentals WHERE id = ?', (rental_id,))
            result = cursor.fetchone()

            if not result:
                return False

            equipment_id, current_status = result

            if current_status not in ['Active', 'Overdue']:
                return False

            # تحديث حالة الإيجار
            cursor.execute('''
                UPDATE rentals
                SET status = 'Returned', actual_return_date = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (return_date, rental_id))

            # تحديث حالة المعدة
            equipment_status = 'Available' if condition in ['excellent', 'good', 'fair'] else 'Maintenance'
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', (equipment_status, equipment_id))

            # إضافة سجل الإرجاع
            cursor.execute('''
                INSERT INTO equipment_return_log (
                    rental_id, equipment_id, return_date, condition, notes, returned_by
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (rental_id, equipment_id, return_date, condition, 'إرجاع سريع', 1))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في الإرجاع السريع: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def mark_rental_as_lost(self, rental_id: int, return_date: str, notes: str = '') -> bool:
        """تسجيل الإيجار الفردي كمفقود"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على معلومات الإيجار
            cursor.execute('''
                SELECT r.equipment_id, r.customer_id, r.rental_date, r.return_date, r.price, r.status,
                       e.name, e.daily_price
                FROM rentals r
                LEFT JOIN equipment e ON r.equipment_id = e.id
                WHERE r.id = ?
            ''', (rental_id,))

            result = cursor.fetchone()
            if not result:
                return False

            equipment_id, customer_id, rental_date, expected_return_date, rental_price, current_status, equipment_name, daily_price = result

            if current_status not in ['Active', 'Overdue']:
                return False

            # إضافة إلى المفقودات
            lost_data = {
                'equipment_id': equipment_id,
                'customer_id': customer_id,
                'rental_id': rental_id,
                'loss_date': return_date,
                'rental_date': rental_date,
                'expected_return_date': expected_return_date,
                'rental_price': rental_price or 0,
                'replacement_cost': (daily_price or 0) * 30,  # تقدير 30 يوم
                'description': notes or 'معدة مفقودة',
                'status': 'Lost'
            }

            self.add_lost_equipment(lost_data)

            # تحديث حالة الإيجار
            cursor.execute('''
                UPDATE rentals
                SET status = 'Lost', actual_return_date = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (return_date, rental_id))

            # تحديث حالة المعدة
            cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Lost', equipment_id))

            # إضافة سجل الإرجاع
            cursor.execute('''
                INSERT INTO equipment_return_log (
                    rental_id, equipment_id, return_date, condition, notes, returned_by
                ) VALUES (?, ?, ?, ?, ?, ?)
            ''', (rental_id, equipment_id, return_date, 'lost', notes, 1))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تسجيل فقدان الإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def mark_multi_rental_as_lost(self, rental_group_id: int, return_date: str, notes: str = '') -> bool:
        """تسجيل الإيجار المتعدد كمفقود"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # الحصول على معلومات مجموعة الإيجار
            cursor.execute('''
                SELECT customer_id, rental_date, return_date, total_price, status
                FROM rental_groups
                WHERE id = ?
            ''', (rental_group_id,))

            group_result = cursor.fetchone()
            if not group_result:
                return False

            customer_id, rental_date, expected_return_date, total_price, current_status = group_result

            if current_status not in ['Active', 'Overdue']:
                return False

            # الحصول على جميع عناصر الإيجار المتعدد
            cursor.execute('''
                SELECT ri.id, ri.equipment_id, e.name, e.daily_price
                FROM rental_items ri
                LEFT JOIN equipment e ON ri.equipment_id = e.id
                WHERE ri.rental_group_id = ? AND ri.status != 'Returned' AND ri.status != 'Lost'
            ''', (rental_group_id,))

            items = cursor.fetchall()

            if not items:
                return False

            # معالجة كل عنصر
            for item_id, equipment_id, equipment_name, daily_price in items:
                # إضافة إلى المفقودات
                lost_data = {
                    'equipment_id': equipment_id,
                    'customer_id': customer_id,
                    'rental_id': rental_group_id,
                    'rental_group_id': rental_group_id,
                    'loss_date': return_date,
                    'rental_date': rental_date,
                    'expected_return_date': expected_return_date,
                    'rental_price': total_price / len(items) if total_price else 0,  # توزيع السعر
                    'replacement_cost': (daily_price or 0) * 30,
                    'description': f'{notes} - {equipment_name}' if notes else f'معدة مفقودة - {equipment_name}',
                    'status': 'Lost'
                }

                self.add_lost_equipment(lost_data)

                # تحديث حالة العنصر
                cursor.execute('''
                    UPDATE rental_items
                    SET status = 'Lost', returned_date = CURRENT_TIMESTAMP,
                        condition_on_return = 'lost', notes = ?
                    WHERE id = ?
                ''', (notes, item_id))

                # تحديث حالة المعدة
                cursor.execute('UPDATE equipment SET status = ? WHERE id = ?', ('Lost', equipment_id))

                # إضافة سجل الإرجاع
                cursor.execute('''
                    INSERT INTO equipment_return_log (
                        rental_id, equipment_id, return_date, condition, notes, returned_by
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (rental_group_id, equipment_id, return_date, 'lost', notes, 1))

            # تحديث حالة مجموعة الإيجار
            cursor.execute('''
                UPDATE rental_groups
                SET status = 'Lost', updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (rental_group_id,))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تسجيل فقدان الإيجار المتعدد: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def create_multi_rental(self, rental_data: Dict) -> int:
        """إنشاء إيجار متعدد جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # إنشاء مجموعة الإيجار
            cursor.execute('''
                INSERT INTO rental_groups (
                    customer_id, rental_date, return_date, total_price,
                    status, notes, payment_status, created_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                rental_data['customer_id'],
                rental_data['rental_date'],
                rental_data['return_date'],
                rental_data['total_price'],
                'Active',
                rental_data.get('notes', ''),
                rental_data.get('payment_status', 'Pending')
            ))

            rental_group_id = cursor.lastrowid

            # إضافة عناصر الإيجار
            for item in rental_data['equipment_items']:
                cursor.execute('''
                    INSERT INTO rental_items (
                        rental_group_id, equipment_id, rental_price,
                        days_count, subtotal, status
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    rental_group_id,
                    item['equipment_id'],
                    item.get('daily_price', item.get('dailyPrice', 0)),
                    item.get('days', 1),
                    item.get('total_price', item.get('totalPrice', 0)),
                    'Active'
                ))

                # تحديث حالة المعدة إلى مؤجرة
                cursor.execute('''
                    UPDATE equipment SET status = 'Rented' WHERE id = ?
                ''', (item['equipment_id'],))

            conn.commit()
            return rental_group_id

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الإيجار المتعدد: {e}")
            conn.rollback()
            return None
        finally:
            conn.close()

    def add_rental_payment(self, payment_data: Dict) -> bool:
        """إضافة دفعة للإيجار"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO rental_payments (
                    rental_group_id, amount, payment_method,
                    payment_date, notes, created_date
                ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                payment_data['rental_group_id'],
                payment_data['amount'],
                payment_data['payment_method'],
                payment_data['payment_date'],
                payment_data.get('notes', '')
            ))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في إضافة دفعة الإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def update_customer(self, customer_id: int, customer_data: Dict) -> bool:
        """تحديث بيانات العميل"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE customers
                SET name = ?, phone = ?, address = ?, email = ?, notes = ?
                WHERE id = ?
            ''', (
                customer_data['name'],
                customer_data['phone'],
                customer_data.get('address', ''),
                customer_data.get('email', ''),
                customer_data.get('notes', ''),
                customer_id
            ))

            conn.commit()
            return cursor.rowcount > 0

        except sqlite3.Error as e:
            print(f"خطأ في تحديث العميل: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_equipment_rental_history(self, equipment_id: int) -> List[Dict]:
        """الحصول على تاريخ إيجارات معدة معينة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # البحث في الإيجارات المتعددة
            cursor.execute('''
                SELECT
                    rg.id as rental_id,
                    rg.customer_id,
                    c.name as customer_name,
                    rg.rental_date,
                    rg.return_date,
                    rg.actual_return_date,
                    ri.daily_price,
                    ri.rental_days,
                    ri.total_price as price,
                    rg.status,
                    'multi' as rental_type
                FROM rental_items ri
                JOIN rental_groups rg ON ri.rental_group_id = rg.id
                LEFT JOIN customers c ON rg.customer_id = c.id
                WHERE ri.equipment_id = ?
                ORDER BY rg.rental_date DESC
            ''', (equipment_id,))

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على تاريخ إيجارات المعدة: {e}")
            return []
        finally:
            conn.close()

    def get_multi_rental_by_id(self, rental_id: int) -> Optional[Dict]:
        """الحصول على إيجار متعدد بالمعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    rg.*,
                    c.name as customer_name,
                    c.phone as customer_phone
                FROM rental_groups rg
                LEFT JOIN customers c ON rg.customer_id = c.id
                WHERE rg.id = ?
            ''', (rental_id,))

            result = cursor.fetchone()
            if result:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, result))
            return None

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على الإيجار المتعدد: {e}")
            return None
        finally:
            conn.close()

    def return_multi_rental_quick(self, rental_id: int, return_date: str, condition: str = 'good', notes: str = '') -> bool:
        """إرجاع سريع للإيجار المتعدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود الإيجار
            cursor.execute('SELECT id, status FROM rental_groups WHERE id = ?', (rental_id,))
            rental = cursor.fetchone()

            if not rental:
                print(f"الإيجار {rental_id} غير موجود")
                return False

            if rental[1] not in ['Active', 'Overdue']:
                print(f"الإيجار {rental_id} بحالة {rental[1]} - لا يمكن إرجاعه")
                return False

            # تحديث حالة الإيجار
            cursor.execute('''
                UPDATE rental_groups
                SET status = 'Returned',
                    actual_return_date = ?,
                    notes = CASE
                        WHEN notes IS NULL OR notes = '' THEN ?
                        ELSE notes || ' | ' || ?
                    END
                WHERE id = ?
            ''', (return_date, notes, notes, rental_id))

            # تحديث حالة عناصر الإيجار
            cursor.execute('''
                UPDATE rental_items
                SET status = 'Returned',
                    returned_date = ?,
                    condition_on_return = ?,
                    notes = CASE
                        WHEN notes IS NULL OR notes = '' THEN ?
                        ELSE notes || ' | ' || ?
                    END
                WHERE rental_group_id = ? AND status = 'Active'
            ''', (return_date, condition, notes, notes, rental_id))

            # تحديث حالة المعدات إلى متاحة
            cursor.execute('''
                UPDATE equipment
                SET status = 'Available'
                WHERE id IN (
                    SELECT equipment_id
                    FROM rental_items
                    WHERE rental_group_id = ?
                )
            ''', (rental_id,))

            conn.commit()
            print(f"تم إرجاع الإيجار {rental_id} بنجاح")
            return True

        except sqlite3.Error as e:
            print(f"خطأ في الإرجاع السريع: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def mark_multi_rental_as_lost(self, rental_id: int, loss_date: str, notes: str) -> bool:
        """تسجيل إيجار متعدد كمفقود"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود الإيجار
            cursor.execute('SELECT id, status, customer_id FROM rental_groups WHERE id = ?', (rental_id,))
            rental = cursor.fetchone()

            if not rental:
                print(f"الإيجار {rental_id} غير موجود")
                return False

            if rental[1] not in ['Active', 'Overdue']:
                print(f"الإيجار {rental_id} بحالة {rental[1]} - لا يمكن تسجيله كمفقود")
                return False

            customer_id = rental[2]

            # الحصول على عناصر الإيجار النشطة
            cursor.execute('''
                SELECT ri.equipment_id, ri.rental_price, ri.days_count, ri.subtotal, e.name, e.model, e.serial_number
                FROM rental_items ri
                JOIN equipment e ON ri.equipment_id = e.id
                WHERE ri.rental_group_id = ? AND ri.status = 'Active'
            ''', (rental_id,))

            active_items = cursor.fetchall()

            if not active_items:
                print(f"لا توجد عناصر نشطة في الإيجار {rental_id}")
                return False

            # تحديث حالة الإيجار
            cursor.execute('''
                UPDATE rental_groups
                SET status = 'Lost',
                    actual_return_date = ?,
                    notes = CASE
                        WHEN notes IS NULL OR notes = '' THEN ?
                        ELSE notes || ' | ' || ?
                    END
                WHERE id = ?
            ''', (loss_date, f'مفقود: {notes}', f'مفقود: {notes}', rental_id))

            # تحديث حالة عناصر الإيجار
            cursor.execute('''
                UPDATE rental_items
                SET status = 'Lost',
                    returned_date = ?,
                    condition_on_return = 'lost',
                    notes = CASE
                        WHEN notes IS NULL OR notes = '' THEN ?
                        ELSE notes || ' | ' || ?
                    END
                WHERE rental_group_id = ? AND status = 'Active'
            ''', (loss_date, f'مفقود: {notes}', f'مفقود: {notes}', rental_id))

            # إضافة المعدات إلى قائمة المفقودات
            for item in active_items:
                equipment_id, rental_price, days_count, subtotal, name, model, serial_number = item

                cursor.execute('''
                    INSERT INTO lost_equipment (
                        equipment_id, customer_id, rental_id, loss_date,
                        reason, replacement_cost, status, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    equipment_id, customer_id, rental_id, loss_date,
                    notes, subtotal, 'Lost',
                    f'مفقود من الإيجار #{rental_id} - {name} ({model})'
                ))

                # تحديث حالة المعدة
                cursor.execute('''
                    UPDATE equipment
                    SET status = 'Lost'
                    WHERE id = ?
                ''', (equipment_id,))

            conn.commit()
            print(f"تم تسجيل الإيجار {rental_id} كمفقود مع {len(active_items)} معدة")
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تسجيل الفقدان: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_lost_equipment_by_customer(self, customer_id: int) -> List[Dict]:
        """الحصول على المعدات المفقودة لعميل محدد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT
                    le.*,
                    e.name as equipment_name,
                    e.model as equipment_model,
                    e.serial_number as equipment_serial,
                    e.category as equipment_category,
                    julianday('now') - julianday(le.lost_date) as days_lost
                FROM lost_equipment le
                LEFT JOIN equipment e ON le.equipment_id = e.id
                WHERE le.customer_id = ?
                ORDER BY le.lost_date DESC
            '''

            cursor.execute(query, (customer_id,))
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في الحصول على المعدات المفقودة للعميل: {e}")
            return []
        finally:
            conn.close()

    def update_lost_equipment_status(self, lost_equipment_id: int, status: str,
                                   compensation_paid: float = 0.0, notes: str = "") -> bool:
        """تحديث حالة المعدة المفقودة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE lost_equipment
                SET status = ?, compensation_paid = ?, notes = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (status, compensation_paid, notes, lost_equipment_id))

            # إذا تم العثور على المعدة، تحديث حالة المعدة
            if status == 'Found':
                cursor.execute('''
                    UPDATE equipment
                    SET status = 'Available'
                    WHERE id = (SELECT equipment_id FROM lost_equipment WHERE id = ?)
                ''', (lost_equipment_id,))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في تحديث حالة المعدة المفقودة: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    # ==================== سجل الإيجارات (أرشيف) ====================

    def archive_rental(self, rental_id: int = None, rental_group_id: int = None,
                      archive_reason: str = 'Auto_Archive') -> bool:
        """أرشفة إيجار إلى سجل الإيجارات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            archive_date = datetime.now().strftime('%Y-%m-%d')

            if rental_id:
                # أرشفة إيجار فردي
                cursor.execute('''
                    SELECT r.*, c.name as customer_name, e.name as equipment_name, e.model as equipment_model
                    FROM rentals r
                    LEFT JOIN customers c ON r.customer_id = c.id
                    LEFT JOIN equipment e ON r.equipment_id = e.id
                    WHERE r.id = ?
                ''', (rental_id,))

                rental_data = cursor.fetchone()
                if not rental_data:
                    return False

                equipment_data = f"{rental_data[13]} - {rental_data[14]}"  # equipment_name - equipment_model

                # تحديد الحالة بناءً على سبب الأرشفة
                if archive_reason == 'Lost':
                    status = 'Lost'
                elif archive_reason == 'Cancelled':
                    status = 'Cancelled'
                elif rental_data[6] == 'Returned':
                    status = 'Completed'
                else:
                    status = 'Overdue'

                cursor.execute('''
                    INSERT INTO rental_archive (
                        original_rental_id, rental_type, customer_id, equipment_data,
                        rental_date, expected_return_date, actual_return_date,
                        total_price, status, archive_reason, archive_date, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (rental_id, 'individual', rental_data[1], equipment_data,
                      rental_data[3], rental_data[4], rental_data[7],
                      rental_data[5], status, archive_reason, archive_date, rental_data[8]))

            elif rental_group_id:
                # أرشفة إيجار متعدد
                cursor.execute('''
                    SELECT rg.*, c.name as customer_name
                    FROM rental_groups rg
                    LEFT JOIN customers c ON rg.customer_id = c.id
                    WHERE rg.id = ?
                ''', (rental_group_id,))

                rental_data = cursor.fetchone()
                if not rental_data:
                    return False

                # الحصول على المعدات
                cursor.execute('''
                    SELECT e.name, e.model
                    FROM rental_items ri
                    LEFT JOIN equipment e ON ri.equipment_id = e.id
                    WHERE ri.rental_group_id = ?
                ''', (rental_group_id,))

                equipment_list = cursor.fetchall()
                equipment_data = ', '.join([f"{eq[0]} - {eq[1]}" for eq in equipment_list])

                # تحديد الحالة بناءً على سبب الأرشفة للإيجار المتعدد
                if archive_reason == 'Lost':
                    status = 'Lost'
                elif archive_reason == 'Cancelled':
                    status = 'Cancelled'
                elif rental_data[5] == 'Returned':
                    status = 'Completed'
                else:
                    status = 'Overdue'

                cursor.execute('''
                    INSERT INTO rental_archive (
                        original_rental_group_id, rental_type, customer_id, equipment_data,
                        rental_date, expected_return_date, actual_return_date,
                        total_price, status, archive_reason, archive_date, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (rental_group_id, 'multi', rental_data[1], equipment_data,
                      rental_data[2], rental_data[3], None,
                      rental_data[4], status, archive_reason, archive_date, rental_data[6]))

            conn.commit()
            return True

        except sqlite3.Error as e:
            print(f"خطأ في أرشفة الإيجار: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_rental_archive(self, days_limit: int = 30, rental_type: str = None) -> List[Dict]:
        """الحصول على سجل الإيجارات المؤرشفة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            query = '''
                SELECT
                    ra.*,
                    c.name as customer_name,
                    c.phone as customer_phone,
                    julianday('now') - julianday(ra.archive_date) as days_archived
                FROM rental_archive ra
                LEFT JOIN customers c ON ra.customer_id = c.id
                WHERE julianday('now') - julianday(ra.archive_date) <= ?
            '''

            params = [days_limit]

            if rental_type:
                query += ' AND ra.rental_type = ?'
                params.append(rental_type)

            query += ' ORDER BY ra.archive_date DESC'

            cursor.execute(query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في استرجاع سجل الإيجارات: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_detailed_report(self, filter_type: str = 'all', filter_value: str = '', limit: int = 0) -> List[Dict]:
        """الحصول على تقرير مفصل للمعدات حسب النوع/الموديل/الفئة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            base_query = '''
                SELECT e.*,
                       COUNT(r.id) as total_rentals,
                       COALESCE(SUM(r.price), 0) as total_revenue,
                       COALESCE(AVG(r.price), 0) as avg_rental_price,
                       MAX(r.rental_date) as last_rental_date,
                       CASE
                           WHEN e.status = 'Available' THEN 'متاحة'
                           WHEN e.status = 'Rented' THEN 'مؤجرة'
                           WHEN e.status = 'Maintenance' THEN 'صيانة'
                           WHEN e.status = 'Damaged' THEN 'تالفة'
                           WHEN e.status = 'Lost' THEN 'مفقودة'
                       END as status_arabic
                FROM equipment e
                LEFT JOIN rentals r ON e.id = r.equipment_id
            '''

            # إضافة فلتر حسب النوع
            where_clause = ''
            params = []

            if filter_type == 'category' and filter_value:
                where_clause = 'WHERE e.category = ?'
                params.append(filter_value)
            elif filter_type == 'model' and filter_value:
                where_clause = 'WHERE e.model LIKE ?'
                params.append(f'%{filter_value}%')
            elif filter_type == 'name' and filter_value:
                where_clause = 'WHERE e.name LIKE ?'
                params.append(f'%{filter_value}%')
            elif filter_type == 'status' and filter_value:
                where_clause = 'WHERE e.status = ?'
                params.append(filter_value)

            group_clause = 'GROUP BY e.id ORDER BY e.name'

            # إضافة حد العدد
            limit_clause = ''
            if limit > 0:
                limit_clause = f'LIMIT {limit}'

            final_query = f'{base_query} {where_clause} {group_clause} {limit_clause}'

            cursor.execute(final_query, params)
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء تقرير المعدات: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_categories_summary(self) -> List[Dict]:
        """الحصول على ملخص المعدات حسب الفئات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    category,
                    COUNT(*) as total_equipment,
                    COUNT(CASE WHEN status = 'Available' THEN 1 END) as available_count,
                    COUNT(CASE WHEN status = 'Rented' THEN 1 END) as rented_count,
                    COUNT(CASE WHEN status = 'Maintenance' THEN 1 END) as maintenance_count,
                    COUNT(CASE WHEN status = 'Damaged' THEN 1 END) as damaged_count,
                    COUNT(CASE WHEN status = 'Lost' THEN 1 END) as lost_count,
                    COALESCE(AVG(price), 0) as avg_price,
                    COALESCE(AVG(daily_rental_price), 0) as avg_daily_rental_price
                FROM equipment
                GROUP BY category
                ORDER BY category
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء ملخص الفئات: {e}")
            return []
        finally:
            conn.close()

    def get_equipment_models_summary(self) -> List[Dict]:
        """الحصول على ملخص المعدات حسب الموديلات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT
                    model,
                    category,
                    COUNT(*) as total_equipment,
                    COUNT(CASE WHEN status = 'Available' THEN 1 END) as available_count,
                    COUNT(CASE WHEN status = 'Rented' THEN 1 END) as rented_count,
                    COALESCE(AVG(price), 0) as avg_price,
                    COALESCE(AVG(daily_rental_price), 0) as avg_daily_rental_price
                FROM equipment
                WHERE model IS NOT NULL AND model != ''
                GROUP BY model, category
                ORDER BY category, model
            ''')

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء ملخص الموديلات: {e}")
            return []
        finally:
            conn.close()

    def cleanup_old_archive(self, days_to_keep: int = 30) -> bool:
        """تنظيف السجلات القديمة من الأرشيف"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                DELETE FROM rental_archive
                WHERE julianday('now') - julianday(archive_date) > ?
            ''', (days_to_keep,))

            deleted_count = cursor.rowcount
            conn.commit()

            if deleted_count > 0:
                print(f"تم حذف {deleted_count} سجل قديم من الأرشيف")

            return True

        except sqlite3.Error as e:
            print(f"خطأ في تنظيف الأرشيف: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
