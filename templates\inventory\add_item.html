{% extends "base.html" %}

{% block title %}إضافة عنصر جديد للمخزون{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .section-header {
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
    
    .category-specific {
        display: none;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-plus me-2"></i>إضافة عنصر جديد للمخزون</h2>
                    <p class="text-muted">إضافة عنصر جديد لإدارة المخزون والإكسسوارات</p>
                </div>
                <div>
                    <a href="{{ url_for('inventory_items') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ url_for('add_inventory_item') }}">
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <div class="section-header">
                        <h4><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h4>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم العنصر <span class="required-field">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                                <div class="form-help">أدخل اسم العنصر بوضوح</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفئة <span class="required-field">*</span></label>
                                <select class="form-select" name="category_id" id="categorySelect" required onchange="showCategoryFields()">
                                    <option value="">اختر الفئة</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الماركة</label>
                                <input type="text" class="form-control" name="brand">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الموديل</label>
                                <input type="text" class="form-control" name="model">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم القطعة</label>
                                <input type="text" class="form-control" name="part_number">
                                <div class="form-help">رقم القطعة من الشركة المصنعة</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المورد</label>
                                <select class="form-select" name="supplier_id">
                                    <option value="">اختر المورد</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                </div>

                <!-- Pricing & Stock -->
                <div class="form-section">
                    <div class="section-header">
                        <h4><i class="fas fa-dollar-sign me-2"></i>الأسعار والمخزون</h4>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">سعر الوحدة (ر.س)</label>
                                <input type="number" class="form-control" name="unit_price" step="0.01" min="0" value="0">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">سعر التأجير (ر.س)</label>
                                <input type="number" class="form-control" name="rental_price" step="0.01" min="0" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المخزون الحالي</label>
                                <input type="number" class="form-control" name="current_stock" min="0" value="0">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" class="form-control" name="min_stock_level" min="1" value="5">
                                <div class="form-help">سيتم تنبيهك عند الوصول لهذا المستوى</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Specific Fields -->
                
                <!-- البطاريات -->
                <div id="batteryFields" class="category-specific">
                    <h5><i class="fas fa-battery-full me-2"></i>معلومات البطارية</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">مستوى الشحن (%)</label>
                                <input type="number" class="form-control" name="battery_level" min="0" max="100">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">حالة البطارية</label>
                                <select class="form-select" name="battery_condition">
                                    <option value="">اختر الحالة</option>
                                    <option value="ممتاز">ممتاز</option>
                                    <option value="جيد">جيد</option>
                                    <option value="متوسط">متوسط</option>
                                    <option value="ضعيف">ضعيف</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الترايبودات -->
                <div id="tripodFields" class="category-specific">
                    <h5><i class="fas fa-camera-retro me-2"></i>معلومات الحامل</h5>
                    <div class="mb-3">
                        <label class="form-label">قدرة التحمل (كيلوغرام)</label>
                        <input type="number" class="form-control" name="weight_capacity" step="0.1" min="0">
                    </div>
                </div>

                <!-- كروت الذاكرة -->
                <div id="memoryFields" class="category-specific">
                    <h5><i class="fas fa-sd-card me-2"></i>معلومات كرت الذاكرة</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">حجم التخزين (جيجابايت)</label>
                                <input type="number" class="form-control" name="memory_size" min="1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">سرعة النقل</label>
                                <input type="text" class="form-control" name="memory_speed" placeholder="مثال: 170MB/s">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المعدات المتوافقة -->
                <div class="form-section">
                    <div class="section-header">
                        <h4><i class="fas fa-link me-2"></i>معلومات إضافية</h4>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المعدات المتوافقة</label>
                        <textarea class="form-control" name="compatible_equipment" rows="2" 
                                  placeholder="أدخل أسماء المعدات المتوافقة مع هذا العنصر"></textarea>
                        <div class="form-help">مثال: Canon EOS R5, R6, 5D Mark IV</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="form-section">
                    <div class="section-header">
                        <h4><i class="fas fa-info me-2"></i>نصائح</h4>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>نصائح لإضافة العناصر:</h6>
                        <ul class="mb-0">
                            <li>استخدم أسماء واضحة ومفصلة للعناصر</li>
                            <li>حدد الفئة المناسبة لتسهيل البحث</li>
                            <li>أدخل رقم القطعة إن وجد للمرجعية</li>
                            <li>حدد الحد الأدنى للمخزون لتجنب النفاد</li>
                            <li>أضف معلومات المعدات المتوافقة</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه:</h6>
                        <p class="mb-0">تأكد من صحة البيانات قبل الحفظ. بعض المعلومات قد تؤثر على عمليات التأجير.</p>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="form-section">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save me-2"></i>حفظ العنصر
                        </button>
                        <a href="{{ url_for('inventory_items') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
function showCategoryFields() {
    // إخفاء جميع الحقول الخاصة بالفئات
    document.querySelectorAll('.category-specific').forEach(el => {
        el.style.display = 'none';
    });
    
    const categorySelect = document.getElementById('categorySelect');
    const selectedOption = categorySelect.options[categorySelect.selectedIndex];
    const categoryName = selectedOption.text;
    
    // إظهار الحقول المناسبة حسب الفئة
    if (categoryName.includes('بطاريات')) {
        document.getElementById('batteryFields').style.display = 'block';
    } else if (categoryName.includes('ترايبودات') || categoryName.includes('حوامل')) {
        document.getElementById('tripodFields').style.display = 'block';
    } else if (categoryName.includes('ذاكرة') || categoryName.includes('كروت')) {
        document.getElementById('memoryFields').style.display = 'block';
    }
}

// التحقق من صحة النموذج قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    const category = document.querySelector('select[name="category_id"]').value;
    
    if (!name) {
        e.preventDefault();
        showAlert('danger', 'يرجى إدخال اسم العنصر');
        return;
    }
    
    if (!category) {
        e.preventDefault();
        showAlert('danger', 'يرجى اختيار فئة العنصر');
        return;
    }
});
</script>
{% endblock %}
