#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_equipment_page():
    """اختبار صفحة المعدات مع تسجيل دخول"""
    base_url = "http://127.0.0.1:5000"
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة المعدات
        print("🔍 اختبار صفحة المعدات...")
        response = session.get(f"{base_url}/equipment")
        
        if response.status_code == 200:
            print("✅ صفحة المعدات تعمل")
            print(f"📊 حجم المحتوى: {len(response.text)} حرف")
            
            # البحث عن البيانات في HTML
            content = response.text
            
            # فحص وجود الجدول
            if '<table' in content and 'tbody' in content:
                print("✅ يحتوي على جدول")
                
                # عد الصفوف
                row_count = content.count('<tr data-category=')
                print(f"📊 عدد صفوف المعدات: {row_count}")
                
                # فحص وجود أسماء المعدات
                if 'كاميرا' in content:
                    print("✅ يحتوي على أسماء المعدات")
                else:
                    print("❌ لا يحتوي على أسماء المعدات")
                
                # فحص JavaScript
                if 'equipment_list' in content:
                    print("✅ يحتوي على متغير equipment_list في JavaScript")
                else:
                    print("❌ لا يحتوي على متغير equipment_list")
                
                # البحث عن البيانات المرسلة
                if 'var equipment =' in content:
                    start = content.find('var equipment =')
                    end = content.find(';', start)
                    if start != -1 and end != -1:
                        equipment_data = content[start:end+1]
                        print(f"📊 بيانات المعدات في JavaScript:")
                        print(equipment_data[:200] + "...")
                
            else:
                print("❌ لا يحتوي على جدول")
        else:
            print(f"❌ خطأ في صفحة المعدات: {response.status_code}")
    else:
        print("❌ فشل تسجيل الدخول")

if __name__ == "__main__":
    test_equipment_page()
