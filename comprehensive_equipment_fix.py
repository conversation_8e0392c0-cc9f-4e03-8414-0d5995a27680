#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح شامل لمشاكل عرض المعدات
Comprehensive Equipment Display Fix
"""

import requests
import json
from datetime import datetime

def test_equipment_pages():
    """اختبار جميع صفحات المعدات"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔧 إصلاح شامل لمشاكل عرض المعدات")
    print("=" * 60)
    
    # إنشاء session للحفاظ على تسجيل الدخول
    session = requests.Session()
    
    # 1. تسجيل الدخول
    print("1️⃣ تسجيل الدخول...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    try:
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return
    
    # 2. اختبار صفحة المعدات الرئيسية
    print("\n2️⃣ اختبار صفحة المعدات الرئيسية...")
    try:
        response = session.get(f"{base_url}/equipment")
        if response.status_code == 200:
            content = response.text
            # فحص وجود البيانات في HTML
            if 'table' in content and 'tbody' in content:
                # عد عدد الصفوف
                row_count = content.count('<tr data-category=')
                print(f"✅ صفحة المعدات تعمل - تحتوي على {row_count} صف")
                
                # فحص وجود أسماء المعدات
                if 'كاميرا' in content or 'عدسة' in content:
                    print("✅ تحتوي على بيانات المعدات")
                else:
                    print("⚠️ لا تحتوي على بيانات المعدات المتوقعة")
            else:
                print("⚠️ صفحة المعدات لا تحتوي على جدول")
        else:
            print(f"❌ خطأ في صفحة المعدات: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الوصول لصفحة المعدات: {e}")
    
    # 3. اختبار API المعدات
    print("\n3️⃣ اختبار API المعدات...")
    endpoints = [
        ("/api/equipment/available", "المعدات المتاحة"),
        ("/api/equipment/all", "جميع المعدات"),
        ("/api/external-equipment/available", "المعدات الخارجية"),
    ]
    
    for endpoint, name in endpoints:
        try:
            response = session.get(f"{base_url}{endpoint}")
            if response.status_code == 200:
                data = response.json()
                count = len(data.get('equipment', data)) if isinstance(data, dict) else len(data)
                print(f"✅ {name}: {count} عنصر")
            else:
                print(f"❌ {name}: خطأ {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: خطأ - {e}")
    
    # 4. اختبار صفحة إنشاء الإيجار
    print("\n4️⃣ اختبار صفحة إنشاء الإيجار...")
    try:
        response = session.get(f"{base_url}/new_rental")
        if response.status_code == 200:
            content = response.text
            if 'المعدات المتاحة' in content and 'المعدات المختارة' in content:
                print("✅ صفحة إنشاء الإيجار تحتوي على أقسام المعدات")
            else:
                print("⚠️ صفحة إنشاء الإيجار لا تحتوي على أقسام المعدات المتوقعة")
        else:
            print(f"❌ خطأ في صفحة إنشاء الإيجار: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الوصول لصفحة إنشاء الإيجار: {e}")
    
    # 5. اختبار صفحة المكاتب
    print("\n5️⃣ اختبار صفحة المكاتب...")
    try:
        response = session.get(f"{base_url}/offices")
        if response.status_code == 200:
            content = response.text
            if 'المكاتب' in content:
                print("✅ صفحة المكاتب تعمل")
                
                # البحث عن روابط تفاصيل المكاتب
                office_links = content.count('/offices/')
                print(f"✅ تحتوي على {office_links} رابط مكتب")
            else:
                print("⚠️ صفحة المكاتب لا تحتوي على المحتوى المتوقع")
        else:
            print(f"❌ خطأ في صفحة المكاتب: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الوصول لصفحة المكاتب: {e}")
    
    # 6. اختبار صفحة تفاصيل مكتب
    print("\n6️⃣ اختبار صفحة تفاصيل مكتب...")
    try:
        response = session.get(f"{base_url}/offices/1")
        if response.status_code == 200:
            content = response.text
            if 'معدات المكتب' in content or 'المعدات المستقطبة' in content:
                print("✅ صفحة تفاصيل المكتب تحتوي على قسم المعدات")
            else:
                print("⚠️ صفحة تفاصيل المكتب لا تحتوي على قسم المعدات")
        else:
            print(f"❌ خطأ في صفحة تفاصيل المكتب: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الوصول لصفحة تفاصيل المكتب: {e}")
    
    # 7. اختبار API معدات المكتب
    print("\n7️⃣ اختبار API معدات المكتب...")
    try:
        response = session.get(f"{base_url}/api/offices/1/equipment")
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('equipment', data)) if isinstance(data, dict) else len(data)
            print(f"✅ معدات المكتب 1: {count} عنصر")
        else:
            print(f"❌ API معدات المكتب: خطأ {response.status_code}")
    except Exception as e:
        print(f"❌ API معدات المكتب: خطأ - {e}")

def analyze_issues():
    """تحليل المشاكل المحتملة"""
    print("\n" + "=" * 60)
    print("📊 تحليل المشاكل المحتملة")
    print("=" * 60)
    
    issues = [
        "1. صفحة المعدات لا تعرض البيانات رغم وجودها في قاعدة البيانات",
        "2. صفحة المكتب لا تعرض المعدات المستقطبة منه",
        "3. قد تكون هناك مشكلة في القوالب (templates)",
        "4. قد تكون هناك مشكلة في تمرير البيانات من الخادم للقالب",
        "5. قد تكون هناك مشكلة في JavaScript أو CSS تخفي البيانات"
    ]
    
    for issue in issues:
        print(f"⚠️ {issue}")

def suggest_solutions():
    """اقتراح الحلول"""
    print("\n" + "=" * 60)
    print("💡 الحلول المقترحة")
    print("=" * 60)
    
    solutions = [
        "1. فحص القوالب للتأكد من استخدام أسماء المتغيرات الصحيحة",
        "2. إضافة طباعة تشخيصية في routes لمراقبة البيانات المرسلة",
        "3. فحص JavaScript للتأكد من عدم إخفاء البيانات",
        "4. دمج المعدات الداخلية والخارجية في قائمة واحدة",
        "5. إصلاح ربط المعدات الخارجية بالمكاتب",
        "6. تحديث القوالب لعرض جميع أنواع المعدات"
    ]
    
    for solution in solutions:
        print(f"✅ {solution}")

def main():
    print("🚀 تشخيص شامل لمشاكل عرض المعدات")
    print("=" * 70)
    print(f"⏰ وقت التشخيص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_equipment_pages()
    analyze_issues()
    suggest_solutions()
    
    print("\n" + "=" * 70)
    print("✅ انتهى التشخيص")
    print("🌐 للوصول للنظام: http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
