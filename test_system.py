#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_api_endpoints():
    """اختبار API endpoints"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 اختبار API endpoints...")
    print("=" * 50)
    
    # اختبار 1: المعدات المتاحة
    try:
        response = requests.get(f"{base_url}/api/equipment/available")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ المعدات المتاحة: {len(data)} معدة")
        else:
            print(f"❌ خطأ في المعدات المتاحة: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API المعدات: {e}")
    
    # اختبار 2: المعدات الخارجية
    try:
        response = requests.get(f"{base_url}/api/external-equipment/available")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ المعدات الخارجية: {data.get('count', 0)} معدة")
        else:
            print(f"❌ خطأ في المعدات الخارجية: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API المعدات الخارجية: {e}")
    
    # اختبار 3: العملاء
    try:
        response = requests.get(f"{base_url}/api/customers")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ العملاء: {data.get('count', 0)} عميل")
        else:
            print(f"❌ خطأ في العملاء: {response.status_code}")
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ API العملاء: {e}")

def test_pages():
    """اختبار الصفحات"""
    base_url = "http://127.0.0.1:5000"
    
    print("\n🌐 اختبار الصفحات...")
    print("=" * 50)
    
    pages = [
        ("/equipment", "صفحة المعدات"),
        ("/new_rental", "صفحة إنشاء الإيجار"),
        ("/offices", "صفحة المكاتب"),
        ("/inventory", "صفحة المخزون")
    ]
    
    for url, name in pages:
        try:
            response = requests.get(f"{base_url}{url}")
            if response.status_code == 200:
                print(f"✅ {name}: يعمل بشكل صحيح")
            elif response.status_code == 302:
                print(f"🔄 {name}: إعادة توجيه (تحتاج تسجيل دخول)")
            else:
                print(f"❌ {name}: خطأ {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: خطأ في الاتصال - {e}")

def main():
    print("🚀 بدء اختبار النظام...")
    print("=" * 50)
    
    test_pages()
    test_api_endpoints()
    
    print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
