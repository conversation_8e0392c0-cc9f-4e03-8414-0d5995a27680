#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_customers_page():
    """اختبار صفحة العملاء"""
    base_url = "http://127.0.0.1:5000"
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة العملاء
        print("🔍 اختبار صفحة العملاء...")
        response = session.get(f"{base_url}/customers")
        
        if response.status_code == 200:
            content = response.text
            print("✅ صفحة العملاء تعمل")
            print(f"📊 حجم المحتوى: {len(content)} حرف")
            
            # فحص وجود الجدول
            if '<table' in content and 'tbody' in content:
                print("✅ يحتوي على جدول")
                
                # عد الصفوف
                row_count = content.count('<tr data-customer=')
                print(f"📊 عدد صفوف العملاء: {row_count}")
                
                # فحص وجود أسماء العملاء
                if 'أحمد' in content or 'محمد' in content:
                    print("✅ يحتوي على أسماء العملاء")
                else:
                    print("❌ لا يحتوي على أسماء العملاء")
                
                # فحص وجود أرقام الهواتف
                if '01' in content or '02' in content:
                    print("✅ يحتوي على أرقام هواتف")
                else:
                    print("❌ لا يحتوي على أرقام هواتف")
                
                # فحص الإحصائيات
                if 'إجمالي العملاء' in content:
                    print("✅ يحتوي على إحصائيات العملاء")
                else:
                    print("❌ لا يحتوي على إحصائيات العملاء")
                
            else:
                print("❌ لا يحتوي على جدول")
                
            # فحص رسالة "لا يوجد عملاء"
            if 'لا يوجد عملاء' in content:
                print("⚠️ يعرض رسالة 'لا يوجد عملاء'")
            else:
                print("✅ لا يعرض رسالة 'لا يوجد عملاء'")
                
        else:
            print(f"❌ خطأ في صفحة العملاء: {response.status_code}")
        
        # اختبار API العملاء
        print(f"\n🔍 اختبار API العملاء...")
        response = session.get(f"{base_url}/api/customers")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API العملاء يعمل")
            print(f"📊 عدد العملاء: {data.get('count', 0)}")
            
            if 'customers' in data and len(data['customers']) > 0:
                print("✅ يحتوي على بيانات العملاء")
                for i, customer in enumerate(data['customers'][:3]):
                    print(f"   {i+1}. {customer['name']} - {customer['phone']}")
            else:
                print("❌ لا يحتوي على بيانات العملاء")
        else:
            print(f"❌ خطأ في API العملاء: {response.status_code}")
            
    else:
        print("❌ فشل تسجيل الدخول")

if __name__ == "__main__":
    test_customers_page()
