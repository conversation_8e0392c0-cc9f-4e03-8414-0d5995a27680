# -*- coding: utf-8 -*-
"""
مسارات إدارة العملاء
Customer Management Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, jsonify, flash
import sqlite3
from datetime import datetime
import os

# استيراد الأدوات
from utils.decorators import login_required, permission_required, Permission
from utils.helpers import validate_phone, validate_national_id, safe_int
from config.settings import get_config

# إنشاء Blueprint
customers_bp = Blueprint('customers', __name__)

# الحصول على الإعدادات
config = get_config()

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(config.DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

@customers_bp.route('/')
@login_required
@permission_required(Permission.CUSTOMERS_VIEW)
def customers_list():
    """عرض قائمة العملاء"""
    try:
        page = safe_int(request.args.get('page', 1), 1)
        per_page = 20
        search = request.args.get('search', '').strip()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # بناء الاستعلام
        base_query = '''
            SELECT id, name, phone, email, national_id, address, created_at,
                   (SELECT COUNT(*) FROM rentals WHERE customer_id = customers.id) as rental_count
            FROM customers
        '''
        
        params = []
        if search:
            base_query += ' WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?'
            search_param = f'%{search}%'
            params.extend([search_param, search_param, search_param])
        
        # عدد العملاء الإجمالي
        count_query = f"SELECT COUNT(*) FROM ({base_query}) as filtered"
        cursor.execute(count_query, params)
        total_customers = cursor.fetchone()[0]
        
        # العملاء مع التصفح
        offset = (page - 1) * per_page
        query = f"{base_query} ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([per_page, offset])
        
        cursor.execute(query, params)
        customers = cursor.fetchall()
        
        conn.close()
        
        # حساب معلومات التصفح
        total_pages = (total_customers + per_page - 1) // per_page
        
        return render_template('customers/list.html',
                             customers=customers,
                             page=page,
                             total_pages=total_pages,
                             total_customers=total_customers,
                             search=search)
        
    except Exception as e:
        flash(f'خطأ في تحميل العملاء: {str(e)}', 'error')
        return render_template('customers/list.html', customers=[], page=1, total_pages=1)

@customers_bp.route('/add', methods=['GET', 'POST'])
@login_required
@permission_required(Permission.CUSTOMERS_ADD)
def add_customer():
    """إضافة عميل جديد"""
    if request.method == 'POST':
        try:
            # الحصول على البيانات
            name = request.form.get('name', '').strip()
            phone = request.form.get('phone', '').strip()
            email = request.form.get('email', '').strip()
            national_id = request.form.get('national_id', '').strip()
            address = request.form.get('address', '').strip()
            
            # التحقق من البيانات المطلوبة
            if not name:
                flash('اسم العميل مطلوب', 'error')
                return render_template('customers/add.html')
            
            if not phone:
                flash('رقم الهاتف مطلوب', 'error')
                return render_template('customers/add.html')
            
            # التحقق من صحة البيانات
            if not validate_phone(phone):
                flash('رقم الهاتف غير صحيح', 'error')
                return render_template('customers/add.html')
            
            if national_id and not validate_national_id(national_id):
                flash('الرقم القومي غير صحيح', 'error')
                return render_template('customers/add.html')
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # التحقق من عدم تكرار الهاتف
            cursor.execute('SELECT id FROM customers WHERE phone = ?', (phone,))
            if cursor.fetchone():
                flash('رقم الهاتف مسجل مسبقاً', 'error')
                conn.close()
                return render_template('customers/add.html')
            
            # التحقق من عدم تكرار الرقم القومي
            if national_id:
                cursor.execute('SELECT id FROM customers WHERE national_id = ?', (national_id,))
                if cursor.fetchone():
                    flash('الرقم القومي مسجل مسبقاً', 'error')
                    conn.close()
                    return render_template('customers/add.html')
            
            # إضافة العميل
            cursor.execute('''
                INSERT INTO customers (name, phone, email, national_id, address, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, phone, email, national_id, address, datetime.now().isoformat()))
            
            customer_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            flash(f'تم إضافة العميل "{name}" بنجاح', 'success')
            return redirect(url_for('customers.view_customer', customer_id=customer_id))
            
        except Exception as e:
            flash(f'خطأ في إضافة العميل: {str(e)}', 'error')
    
    return render_template('customers/add.html')

@customers_bp.route('/<int:customer_id>')
@login_required
@permission_required(Permission.CUSTOMERS_VIEW)
def view_customer(customer_id):
    """عرض تفاصيل العميل"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # بيانات العميل
        cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        customer = cursor.fetchone()
        
        if not customer:
            flash('العميل غير موجود', 'error')
            return redirect(url_for('customers.customers_list'))
        
        # إيجارات العميل
        cursor.execute('''
            SELECT r.*, 
                   GROUP_CONCAT(e.name) as equipment_names
            FROM rentals r
            LEFT JOIN rental_items ri ON r.id = ri.rental_id
            LEFT JOIN equipment e ON ri.equipment_id = e.id
            WHERE r.customer_id = ?
            GROUP BY r.id
            ORDER BY r.created_at DESC
        ''', (customer_id,))
        
        rentals = cursor.fetchall()
        
        conn.close()
        
        return render_template('customers/view.html',
                             customer=customer,
                             rentals=rentals)
        
    except Exception as e:
        flash(f'خطأ في تحميل بيانات العميل: {str(e)}', 'error')
        return redirect(url_for('customers.customers_list'))

@customers_bp.route('/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required(Permission.CUSTOMERS_EDIT)
def edit_customer(customer_id):
    """تعديل بيانات العميل"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # الحصول على بيانات العميل
        cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        customer = cursor.fetchone()
        
        if not customer:
            flash('العميل غير موجود', 'error')
            return redirect(url_for('customers.customers_list'))
        
        if request.method == 'POST':
            # الحصول على البيانات المحدثة
            name = request.form.get('name', '').strip()
            phone = request.form.get('phone', '').strip()
            email = request.form.get('email', '').strip()
            national_id = request.form.get('national_id', '').strip()
            address = request.form.get('address', '').strip()
            
            # التحقق من البيانات
            if not name:
                flash('اسم العميل مطلوب', 'error')
                return render_template('customers/edit.html', customer=customer)
            
            if not phone:
                flash('رقم الهاتف مطلوب', 'error')
                return render_template('customers/edit.html', customer=customer)
            
            if not validate_phone(phone):
                flash('رقم الهاتف غير صحيح', 'error')
                return render_template('customers/edit.html', customer=customer)
            
            if national_id and not validate_national_id(national_id):
                flash('الرقم القومي غير صحيح', 'error')
                return render_template('customers/edit.html', customer=customer)
            
            # التحقق من عدم تكرار الهاتف (باستثناء العميل الحالي)
            cursor.execute('SELECT id FROM customers WHERE phone = ? AND id != ?', (phone, customer_id))
            if cursor.fetchone():
                flash('رقم الهاتف مسجل مسبقاً لعميل آخر', 'error')
                return render_template('customers/edit.html', customer=customer)
            
            # التحقق من عدم تكرار الرقم القومي (باستثناء العميل الحالي)
            if national_id:
                cursor.execute('SELECT id FROM customers WHERE national_id = ? AND id != ?', (national_id, customer_id))
                if cursor.fetchone():
                    flash('الرقم القومي مسجل مسبقاً لعميل آخر', 'error')
                    return render_template('customers/edit.html', customer=customer)
            
            # تحديث البيانات
            cursor.execute('''
                UPDATE customers 
                SET name = ?, phone = ?, email = ?, national_id = ?, address = ?, updated_at = ?
                WHERE id = ?
            ''', (name, phone, email, national_id, address, datetime.now().isoformat(), customer_id))
            
            conn.commit()
            conn.close()
            
            flash(f'تم تحديث بيانات العميل "{name}" بنجاح', 'success')
            return redirect(url_for('customers.view_customer', customer_id=customer_id))
        
        conn.close()
        return render_template('customers/edit.html', customer=customer)
        
    except Exception as e:
        flash(f'خطأ في تعديل العميل: {str(e)}', 'error')
        return redirect(url_for('customers.customers_list'))

@customers_bp.route('/<int:customer_id>/delete', methods=['POST'])
@login_required
@permission_required(Permission.CUSTOMERS_DELETE)
def delete_customer(customer_id):
    """حذف العميل"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود العميل
        cursor.execute('SELECT name FROM customers WHERE id = ?', (customer_id,))
        customer = cursor.fetchone()
        
        if not customer:
            return jsonify({'success': False, 'error': 'العميل غير موجود'})
        
        # التحقق من عدم وجود إيجارات نشطة
        cursor.execute('SELECT COUNT(*) FROM rentals WHERE customer_id = ? AND status = "active"', (customer_id,))
        active_rentals = cursor.fetchone()[0]
        
        if active_rentals > 0:
            return jsonify({'success': False, 'error': 'لا يمكن حذف العميل لوجود إيجارات نشطة'})
        
        # حذف العميل
        cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': f'تم حذف العميل "{customer["name"]}" بنجاح'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': f'خطأ في حذف العميل: {str(e)}'})

# API للبحث السريع عن العملاء
@customers_bp.route('/api/search')
@login_required
def api_search_customers():
    """البحث السريع عن العملاء"""
    try:
        query = request.args.get('q', '').strip()
        limit = safe_int(request.args.get('limit', 10), 10)
        
        if not query:
            return jsonify([])
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, phone, email
            FROM customers
            WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
            ORDER BY name
            LIMIT ?
        ''', (f'%{query}%', f'%{query}%', f'%{query}%', limit))
        
        customers = cursor.fetchall()
        conn.close()
        
        result = []
        for customer in customers:
            result.append({
                'id': customer['id'],
                'name': customer['name'],
                'phone': customer['phone'],
                'email': customer['email']
            })
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
