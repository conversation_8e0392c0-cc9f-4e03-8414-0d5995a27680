{% extends "base.html" %}

{% block title %}إدارة الصلاحيات{% endblock %}

{% block extra_css %}
<style>
    .permission-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .permission-category {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0;
        padding: 1rem;
        margin-bottom: 0;
    }
    
    .permission-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }
    
    .permission-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }
    
    .permission-item.enabled {
        background-color: #d1edff;
        border-color: #0d6efd;
    }
    
    .permission-switch {
        transform: scale(1.2);
    }
    
    .user-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .user-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .user-card.selected {
        border: 2px solid #0d6efd;
        background-color: #e7f3ff;
    }
    
    .category-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-user-shield me-2 text-primary"></i>إدارة الصلاحيات المحدثة
                    </h2>
                    <p class="text-muted mb-0">إدارة صلاحيات المستخدمين مع نظام الأدوار الهجين</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="saveAllPermissions()" id="saveBtn">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                    <button class="btn btn-outline-secondary" onclick="resetPermissions()">
                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                    </button>
                    <button class="btn btn-info" onclick="showRoleInfo()">
                        <i class="fas fa-info-circle me-2"></i>معلومات الأدوار
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- قائمة المستخدمين -->
        <div class="col-lg-3">
            <div class="card permission-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>المستخدمون
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% for user in users %}
                    <div class="user-card p-3 border-bottom" onclick="selectUser({{ user.id }}, '{{ user.username }}', '{{ user.full_name }}', '{{ user.role }}')"
                         id="user-{{ user.id }}">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-user-circle fa-2x text-secondary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ user.full_name }}</h6>
                                <small class="text-muted">{{ user.username }}</small>
                                <div class="mt-1">
                                    {% if user.role == 'admin' %}
                                    <span class="badge bg-danger">مدير النظام</span>
                                    {% elif user.role == 'manager' %}
                                    <span class="badge bg-warning">مدير</span>
                                    {% else %}
                                    <span class="badge bg-info">موظف</span>
                                    {% endif %}

                                    <!-- عرض عدد الصلاحيات الأساسية -->
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            <i class="fas fa-key me-1"></i>
                                            {{ user.role_permissions|length }} صلاحية أساسية
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- صلاحيات المستخدم المحدد -->
        <div class="col-lg-9">
            <div id="permissionsContainer" style="display: none;">
                <div class="card permission-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-key me-2"></i>صلاحيات المستخدم: <span id="selectedUserName"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="permissionsContent">
                            <!-- سيتم تحميل الصلاحيات هنا -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- رسالة عدم اختيار مستخدم -->
            <div id="noUserSelected" class="text-center py-5">
                <i class="fas fa-user-plus fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">اختر مستخدماً لإدارة صلاحياته</h4>
                <p class="text-muted">انقر على أحد المستخدمين من القائمة الجانبية لبدء إدارة صلاحياته</p>
            </div>

            <!-- مؤشر التحميل -->
            <div class="loading-spinner" id="loadingSpinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2 text-muted">جاري تحميل الصلاحيات...</p>
            </div>
        </div>
    </div>
</div>

<!-- نافذة معلومات الأدوار -->
<div class="modal fade" id="roleInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>معلومات الأدوار والصلاحيات
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    {% for role_key, role_data in role_info.items() %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-header bg-{{ role_data.color }} text-white">
                                <h6 class="mb-0">{{ role_data.name }}</h6>
                            </div>
                            <div class="card-body">
                                <p class="small mb-2">
                                    <strong>عدد الصلاحيات الأساسية:</strong> {{ role_data.permissions|length }}
                                </p>
                                <div class="small">
                                    {% if role_key == 'admin' %}
                                    <p><i class="fas fa-crown text-warning"></i> جميع الصلاحيات</p>
                                    <p><i class="fas fa-users-cog"></i> إدارة المستخدمين</p>
                                    <p><i class="fas fa-cog"></i> إعدادات النظام</p>
                                    {% elif role_key == 'manager' %}
                                    <p><i class="fas fa-user-tie text-primary"></i> جميع الصلاحيات</p>
                                    <p><i class="fas fa-chart-line"></i> التقارير المتقدمة</p>
                                    <p><i class="fas fa-trash"></i> حذف البيانات</p>
                                    {% else %}
                                    <p><i class="fas fa-user text-info"></i> لا توجد صلاحيات افتراضية</p>
                                    <p><i class="fas fa-user-cog"></i> يحددها المدير</p>
                                    <p><i class="fas fa-hand-point-right"></i> حسب احتياجات العمل</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-lightbulb me-2"></i>نظام الصلاحيات المحدث</h6>
                    <ul class="mb-0 small">
                        <li><strong>مدير النظام والمدير:</strong> جميع الصلاحيات تلقائياً</li>
                        <li><strong>الموظفون:</strong> لا توجد صلاحيات افتراضية</li>
                        <li><strong>تحديد الصلاحيات:</strong> المدير يحدد صلاحيات كل موظف يدوياً</li>
                        <li><strong>المرونة:</strong> يمكن تعديل صلاحيات الموظفين في أي وقت</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedUserId = null;
    let currentPermissions = {};
    let originalPermissions = {};

    // اختيار مستخدم
    function selectUser(userId, username, fullName, role) {
        // إزالة التحديد السابق
        document.querySelectorAll('.user-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // تحديد المستخدم الجديد
        document.getElementById(`user-${userId}`).classList.add('selected');
        selectedUserId = userId;
        
        // تحديث اسم المستخدم
        document.getElementById('selectedUserName').textContent = `${fullName} (${username})`;
        
        // إخفاء رسالة عدم الاختيار وإظهار مؤشر التحميل
        document.getElementById('noUserSelected').style.display = 'none';
        document.getElementById('permissionsContainer').style.display = 'none';
        document.getElementById('loadingSpinner').style.display = 'block';
        
        // تحميل صلاحيات المستخدم
        loadUserPermissions(userId);
    }

    // تحميل صلاحيات المستخدم
    async function loadUserPermissions(userId) {
        try {
            const response = await fetch(`/api/user/${userId}/permissions`);
            const result = await response.json();
            
            if (result.success) {
                currentPermissions = {};
                originalPermissions = {};
                
                // تجميع الصلاحيات حسب الفئة
                const permissionsByCategory = {};
                result.permissions.forEach(perm => {
                    if (!permissionsByCategory[perm.category]) {
                        permissionsByCategory[perm.category] = [];
                    }
                    permissionsByCategory[perm.category].push(perm);
                    
                    // حفظ الصلاحيات الحالية
                    currentPermissions[perm.permission_key] = perm.has_permission;
                    originalPermissions[perm.permission_key] = perm.has_permission;
                });
                
                // عرض الصلاحيات
                displayPermissions(permissionsByCategory);
                
                // إخفاء مؤشر التحميل وإظهار الصلاحيات
                document.getElementById('loadingSpinner').style.display = 'none';
                document.getElementById('permissionsContainer').style.display = 'block';
            } else {
                showAlert('danger', result.error);
            }
        } catch (error) {
            console.error('Error loading permissions:', error);
            showAlert('danger', 'حدث خطأ في تحميل الصلاحيات');
        }
    }

    // عرض الصلاحيات المحدث
    function displayPermissions(permissionsByCategory) {
        const container = document.getElementById('permissionsContent');
        container.innerHTML = '';

        // أسماء الفئات بالعربية
        const categoryNames = {
            'equipment': 'المعدات',
            'customers': 'العملاء',
            'rentals': 'الإيجارات',
            'reports': 'التقارير',
            'inventory': 'المخزن',
            'system': 'النظام'
        };

        // ألوان الفئات
        const categoryColors = {
            'equipment': 'primary',
            'customers': 'success',
            'rentals': 'warning',
            'reports': 'info',
            'inventory': 'secondary',
            'system': 'danger'
        };

        Object.keys(permissionsByCategory).forEach(category => {
            const permissions = permissionsByCategory[category];
            const categoryName = categoryNames[category] || category;
            const categoryColor = categoryColors[category] || 'primary';

            const categoryHtml = `
                <div class="permission-card mb-4">
                    <div class="permission-category bg-${categoryColor}">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>${categoryName}
                        </h5>
                    </div>
                    <div class="permission-grid">
                        ${permissions.map(perm => `
                            <div class="permission-item ${perm.has_permission ? 'enabled' : ''}" id="perm-${perm.permission_key}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            ${perm.permission_name}
                                            ${perm.is_base_permission ? '<i class="fas fa-star text-warning ms-1" title="صلاحية أساسية للدور"></i>' : ''}
                                        </h6>
                                        <small class="text-muted">${perm.permission_description}</small>
                                        <div class="mt-1">
                                            ${perm.is_manager_only ? '<span class="badge bg-danger category-badge">للمدير فقط</span>' : ''}
                                            ${perm.is_base_permission ? '<span class="badge bg-warning category-badge">صلاحية أساسية</span>' : ''}
                                            ${perm.granted_by_username ? '<span class="badge bg-info category-badge">مخصصة</span>' : ''}
                                        </div>
                                        ${perm.granted_by_username && perm.granted_date ?
                                            `<small class="text-muted d-block mt-1">
                                                <i class="fas fa-user me-1"></i>بواسطة: ${perm.granted_by_username}
                                                <br><i class="fas fa-calendar me-1"></i>في: ${new Date(perm.granted_date).toLocaleDateString('ar-EG')}
                                            </small>` : ''}
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input permission-switch" type="checkbox"
                                               id="switch-${perm.permission_key}"
                                               ${perm.has_permission ? 'checked' : ''}
                                               ${perm.is_manager_only && !perm.is_base_permission ? 'disabled' : ''}
                                               ${perm.is_base_permission ? 'disabled title="لا يمكن تعديل الصلاحيات الأساسية"' : ''}
                                               onchange="togglePermission('${perm.permission_key}', this.checked)">
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            container.innerHTML += categoryHtml;
        });
    }

    // تبديل صلاحية
    function togglePermission(permissionKey, enabled) {
        currentPermissions[permissionKey] = enabled;
        
        // تحديث مظهر العنصر
        const permItem = document.getElementById(`perm-${permissionKey}`);
        if (enabled) {
            permItem.classList.add('enabled');
        } else {
            permItem.classList.remove('enabled');
        }
        
        // تفعيل زر الحفظ إذا كان هناك تغييرات
        updateSaveButton();
    }

    // تحديث زر الحفظ
    function updateSaveButton() {
        const hasChanges = JSON.stringify(currentPermissions) !== JSON.stringify(originalPermissions);
        const saveBtn = document.getElementById('saveBtn');
        
        if (hasChanges) {
            saveBtn.classList.remove('btn-success');
            saveBtn.classList.add('btn-warning');
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التغييرات *';
        } else {
            saveBtn.classList.remove('btn-warning');
            saveBtn.classList.add('btn-success');
            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التغييرات';
        }
    }

    // حفظ جميع الصلاحيات
    async function saveAllPermissions() {
        if (!selectedUserId) {
            showAlert('warning', 'يرجى اختيار مستخدم أولاً');
            return;
        }
        
        try {
            const response = await fetch(`/api/user/${selectedUserId}/permissions`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    permissions: currentPermissions
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
                originalPermissions = { ...currentPermissions };
                updateSaveButton();
            } else {
                showAlert('danger', result.error);
            }
        } catch (error) {
            console.error('Error saving permissions:', error);
            showAlert('danger', 'حدث خطأ في حفظ الصلاحيات');
        }
    }

    // إعادة تعيين الصلاحيات
    function resetPermissions() {
        if (!selectedUserId) {
            showAlert('warning', 'يرجى اختيار مستخدم أولاً');
            return;
        }
        
        if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
            currentPermissions = { ...originalPermissions };
            
            // تحديث واجهة المستخدم
            Object.keys(currentPermissions).forEach(permissionKey => {
                const checkbox = document.getElementById(`switch-${permissionKey}`);
                const permItem = document.getElementById(`perm-${permissionKey}`);
                
                if (checkbox) {
                    checkbox.checked = currentPermissions[permissionKey];
                    
                    if (currentPermissions[permissionKey]) {
                        permItem.classList.add('enabled');
                    } else {
                        permItem.classList.remove('enabled');
                    }
                }
            });
            
            updateSaveButton();
            showAlert('info', 'تم إعادة تعيين التغييرات');
        }
    }

    // عرض معلومات الأدوار
    function showRoleInfo() {
        const modal = new bootstrap.Modal(document.getElementById('roleInfoModal'));
        modal.show();
    }

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تلميحات للصلاحيات الأساسية
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
