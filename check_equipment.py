#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def check_equipment():
    """فحص المعدات في قاعدة البيانات"""
    config = get_config()
    conn = sqlite3.connect(config.DATABASE_PATH)
    cursor = conn.cursor()

    print('=== فحص المعدة رقم 1 ===')
    cursor.execute('SELECT id, name, status FROM equipment WHERE id = 1')
    equipment = cursor.fetchone()
    if equipment:
        print(f'ID: {equipment[0]}, Name: {equipment[1]}, Status: {equipment[2]}')
    else:
        print('المعدة رقم 1 غير موجودة')

    print('\n=== فحص أول 3 معدات متاحة ===')
    cursor.execute('SELECT id, name, status FROM equipment WHERE status = "Available" LIMIT 3')
    available = cursor.fetchall()
    for eq in available:
        print(f'ID: {eq[0]}, Name: {eq[1]}, Status: {eq[2]}')

    print('\n=== فحص constraints على جدول equipment ===')
    cursor.execute('SELECT sql FROM sqlite_master WHERE type="table" AND name="equipment"')
    table_sql = cursor.fetchone()
    if table_sql:
        print('بنية الجدول:')
        print(table_sql[0])

    conn.close()

if __name__ == '__main__':
    check_equipment()
