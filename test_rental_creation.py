#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime, timedelta

def test_rental_creation():
    """اختبار إنشاء الإيجار"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 اختبار إنشاء الإيجار")
    print("=" * 50)
    
    # بيانات الإيجار التجريبية
    today = datetime.now()
    tomorrow = today + timedelta(days=1)
    
    rental_data = {
        "customer_id": 1,  # أول عميل في قاعدة البيانات
        "rental_date": today.strftime('%Y-%m-%d'),
        "return_date": tomorrow.strftime('%Y-%m-%d'),
        "notes": "إيجار تجريبي للاختبار",
        "equipment": [
            {
                "id": 1,
                "daily_price": 100.0
            }
        ],
        "external_equipment": []
    }
    
    print(f"📅 تاريخ الإيجار: {rental_data['rental_date']}")
    print(f"📅 تاريخ الإرجاع: {rental_data['return_date']}")
    print(f"👤 العميل: {rental_data['customer_id']}")
    print(f"📦 المعدات: {len(rental_data['equipment'])}")
    
    # اختبار 1: إرسال بيانات صحيحة
    print("\n1️⃣ اختبار إنشاء إيجار ببيانات صحيحة...")
    try:
        response = requests.post(
            f"{base_url}/api/rental/create",
            json=rental_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ تم إنشاء الإيجار بنجاح!")
                print(f"   📋 رقم الإيجار: {result.get('rental_id')}")
                print(f"   💰 المبلغ الإجمالي: {result.get('total_amount')} ج.م")
                print(f"   📅 عدد الأيام: {result.get('days')}")
                print(f"   📝 الرسالة: {result.get('message', '')}")
            else:
                print(f"❌ فشل في إنشاء الإيجار: {result.get('error')}")
        elif response.status_code == 302:
            print("🔄 يحتاج تسجيل دخول")
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   📝 تفاصيل الخطأ: {error_data.get('error', 'غير محدد')}")
            except:
                print(f"   📝 نص الاستجابة: {response.text[:200]}...")
                
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # اختبار 2: بيانات ناقصة (بدون تاريخ)
    print("\n2️⃣ اختبار بيانات ناقصة (بدون تاريخ إيجار)...")
    invalid_data = rental_data.copy()
    invalid_data['rental_date'] = None
    
    try:
        response = requests.post(
            f"{base_url}/api/rental/create",
            json=invalid_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ تم رفض البيانات الناقصة بشكل صحيح: {result.get('error')}")
        else:
            print(f"⚠️ لم يتم رفض البيانات الناقصة: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
    
    # اختبار 3: تاريخ غير صحيح
    print("\n3️⃣ اختبار تاريخ غير صحيح...")
    invalid_date_data = rental_data.copy()
    invalid_date_data['rental_date'] = "تاريخ-غير-صحيح"
    
    try:
        response = requests.post(
            f"{base_url}/api/rental/create",
            json=invalid_date_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ تم رفض التاريخ غير الصحيح: {result.get('error')}")
        else:
            print(f"⚠️ لم يتم رفض التاريخ غير الصحيح: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def test_database_content():
    """فحص محتوى قاعدة البيانات"""
    print("\n💾 فحص قاعدة البيانات...")
    print("=" * 50)
    
    try:
        import sqlite3
        from config.settings import get_config
        
        config = get_config()
        conn = sqlite3.connect(config.DATABASE_PATH)
        cursor = conn.cursor()
        
        # فحص العملاء
        cursor.execute('SELECT id, name FROM customers LIMIT 3')
        customers = cursor.fetchall()
        print("👥 العملاء المتاحون:")
        for customer in customers:
            print(f"   {customer[0]}: {customer[1]}")
        
        # فحص المعدات المتاحة
        cursor.execute('SELECT id, name, daily_rental_price FROM equipment WHERE status = "Available" LIMIT 3')
        equipment = cursor.fetchall()
        print("\n📦 المعدات المتاحة:")
        for eq in equipment:
            print(f"   {eq[0]}: {eq[1]} - {eq[2]} ج.م/يوم")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

def main():
    print("🚀 اختبار إصلاح خطأ strptime في إنشاء الإيجار")
    print("=" * 60)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_database_content()
    test_rental_creation()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الاختبار")
    print("🌐 للوصول للنظام: http://127.0.0.1:5000")
    print("🔐 المستخدم: admin | كلمة المرور: admin123")

if __name__ == "__main__":
    main()
