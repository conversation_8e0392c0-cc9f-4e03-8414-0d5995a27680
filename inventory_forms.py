#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج إدارة المخزن
Inventory Management Forms
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
from typing import Dict, List, Optional, Callable
from datetime import datetime, date

class InventoryItemForm:
    """نموذج إضافة/تعديل قطعة غيار"""
    
    def __init__(self, parent, db_manager, categories: List[Dict], suppliers: List[Dict], 
                 item_data: Optional[Dict] = None, callback: Optional[Callable] = None):
        """تهيئة النموذج"""
        self.parent = parent
        self.db = db_manager
        self.categories = categories
        self.suppliers = suppliers
        self.item_data = item_data
        self.callback = callback
        self.result = None
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
        
        # إنشاء النافذة
        self.create_dialog()
    
    def create_dialog(self):
        """إنشاء نافذة النموذج"""
        self.dialog = tk.Toplevel(self.parent)
        title = "تعديل قطعة غيار" if self.item_data else "إضافة قطعة غيار جديدة"
        self.dialog.title(title)
        self.dialog.geometry("600x700")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تعبئة البيانات إذا كان في وضع التعديل
        if self.item_data:
            self.populate_fields()
    
    def create_interface(self):
        """إنشاء واجهة النموذج"""
        # الإطار الرئيسي مع شريط تمرير
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء Canvas وScrollbar
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # المعلومات الأساسية
        basic_frame = ttk.LabelFrame(scrollable_frame, text="المعلومات الأساسية", padding="10")
        basic_frame.pack(fill=tk.X, pady=(0, 10))
        
        # اسم القطعة
        ttk.Label(basic_frame, text="اسم القطعة:", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.name_var, font=self.arabic_font, width=40).grid(row=0, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # الفئة
        ttk.Label(basic_frame, text="الفئة:", font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=2)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(basic_frame, textvariable=self.category_var, state="readonly", width=37)
        self.category_combo['values'] = [cat['name'] for cat in self.categories]
        self.category_combo.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        self.category_combo.bind('<<ComboboxSelected>>', self.on_category_change)
        
        # العلامة التجارية والموديل
        ttk.Label(basic_frame, text="العلامة التجارية:", font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=2)
        self.brand_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.brand_var, font=self.arabic_font, width=18).grid(row=2, column=1, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        ttk.Label(basic_frame, text="الموديل:", font=self.arabic_font).grid(row=2, column=2, sticky=tk.W, pady=2, padx=(10, 0))
        self.model_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.model_var, font=self.arabic_font, width=18).grid(row=2, column=3, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # رقم القطعة
        ttk.Label(basic_frame, text="رقم القطعة:", font=self.arabic_font).grid(row=3, column=0, sticky=tk.W, pady=2)
        self.part_number_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.part_number_var, font=self.arabic_font, width=40).grid(row=3, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # الوصف
        ttk.Label(basic_frame, text="الوصف:", font=self.arabic_font).grid(row=4, column=0, sticky=tk.NW, pady=2)
        self.description_text = tk.Text(basic_frame, height=3, width=40, font=self.arabic_font)
        self.description_text.grid(row=4, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # معلومات التسعير والمخزون
        pricing_frame = ttk.LabelFrame(scrollable_frame, text="التسعير والمخزون", padding="10")
        pricing_frame.pack(fill=tk.X, pady=(0, 10))
        
        # سعر الشراء وسعر الإيجار
        ttk.Label(pricing_frame, text="سعر الشراء:", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.unit_price_var = tk.StringVar(value="0.0")
        ttk.Entry(pricing_frame, textvariable=self.unit_price_var, font=self.arabic_font, width=15).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(pricing_frame, text="سعر الإيجار:", font=self.arabic_font).grid(row=0, column=2, sticky=tk.W, pady=2, padx=(20, 0))
        self.rental_price_var = tk.StringVar(value="0.0")
        ttk.Entry(pricing_frame, textvariable=self.rental_price_var, font=self.arabic_font, width=15).grid(row=0, column=3, sticky=tk.W, pady=2, padx=(5, 0))
        
        # المخزون الحالي والحد الأدنى
        ttk.Label(pricing_frame, text="المخزون الحالي:", font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=2)
        self.current_stock_var = tk.StringVar(value="0")
        ttk.Entry(pricing_frame, textvariable=self.current_stock_var, font=self.arabic_font, width=15).grid(row=1, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(pricing_frame, text="الحد الأدنى:", font=self.arabic_font).grid(row=1, column=2, sticky=tk.W, pady=2, padx=(20, 0))
        self.min_stock_var = tk.StringVar(value="5")
        ttk.Entry(pricing_frame, textvariable=self.min_stock_var, font=self.arabic_font, width=15).grid(row=1, column=3, sticky=tk.W, pady=2, padx=(5, 0))
        
        # المورد
        ttk.Label(pricing_frame, text="المورد:", font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=2)
        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(pricing_frame, textvariable=self.supplier_var, state="readonly", width=30)
        supplier_names = [""] + [sup['name'] for sup in self.suppliers]
        self.supplier_combo['values'] = supplier_names
        self.supplier_combo.grid(row=2, column=1, columnspan=3, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # معلومات خاصة (حسب النوع)
        self.special_frame = ttk.LabelFrame(scrollable_frame, text="معلومات خاصة", padding="10")
        self.special_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إنشاء الحقول الخاصة
        self.create_special_fields()
        
        # معلومات إضافية
        additional_frame = ttk.LabelFrame(scrollable_frame, text="معلومات إضافية", padding="10")
        additional_frame.pack(fill=tk.X, pady=(0, 10))
        
        # تاريخ انتهاء الصلاحية
        ttk.Label(additional_frame, text="تاريخ انتهاء الصلاحية:", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.expiry_date_var = tk.StringVar()
        ttk.Entry(additional_frame, textvariable=self.expiry_date_var, font=self.arabic_font, width=15).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        ttk.Label(additional_frame, text="(YYYY-MM-DD)", font=self.arabic_font).grid(row=0, column=2, sticky=tk.W, pady=2, padx=(5, 0))
        
        # المعدات المتوافقة
        ttk.Label(additional_frame, text="المعدات المتوافقة:", font=self.arabic_font).grid(row=1, column=0, sticky=tk.NW, pady=2)
        self.compatible_equipment_text = tk.Text(additional_frame, height=2, width=40, font=self.arabic_font)
        self.compatible_equipment_text.grid(row=1, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # ملاحظات
        ttk.Label(additional_frame, text="ملاحظات:", font=self.arabic_font).grid(row=2, column=0, sticky=tk.NW, pady=2)
        self.notes_text = tk.Text(additional_frame, height=3, width=40, font=self.arabic_font)
        self.notes_text.grid(row=2, column=1, columnspan=2, sticky=tk.W+tk.E, pady=2, padx=(5, 0))
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(scrollable_frame)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(buttons_frame, text="حفظ", command=self.save_item).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.LEFT)
        
        # تكوين الشبكة
        basic_frame.columnconfigure(1, weight=1)
        basic_frame.columnconfigure(3, weight=1)
        pricing_frame.columnconfigure(1, weight=1)
        pricing_frame.columnconfigure(3, weight=1)
        additional_frame.columnconfigure(1, weight=1)
        
        # تعبئة Canvas
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def create_special_fields(self):
        """إنشاء الحقول الخاصة حسب نوع القطعة"""
        # مسح الحقول الموجودة
        for widget in self.special_frame.winfo_children():
            widget.destroy()
        
        # حقول البطاريات
        battery_frame = ttk.Frame(self.special_frame)
        battery_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(battery_frame, text="مستوى الشحن (%):", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.battery_level_var = tk.StringVar()
        ttk.Entry(battery_frame, textvariable=self.battery_level_var, font=self.arabic_font, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(battery_frame, text="حالة البطارية:", font=self.arabic_font).grid(row=0, column=2, sticky=tk.W, pady=2, padx=(20, 0))
        self.battery_condition_var = tk.StringVar()
        battery_condition_combo = ttk.Combobox(battery_frame, textvariable=self.battery_condition_var, state="readonly", width=15)
        battery_condition_combo['values'] = ["", "ممتاز", "جيد", "متوسط", "ضعيف"]
        battery_condition_combo.grid(row=0, column=3, sticky=tk.W, pady=2, padx=(5, 0))
        
        # حقول الترايبودات
        tripod_frame = ttk.Frame(self.special_frame)
        tripod_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(tripod_frame, text="قدرة التحمل (كغ):", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.weight_capacity_var = tk.StringVar()
        ttk.Entry(tripod_frame, textvariable=self.weight_capacity_var, font=self.arabic_font, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        # حقول كروت الذاكرة
        memory_frame = ttk.Frame(self.special_frame)
        memory_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(memory_frame, text="حجم الذاكرة (GB):", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.memory_size_var = tk.StringVar()
        ttk.Entry(memory_frame, textvariable=self.memory_size_var, font=self.arabic_font, width=10).grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        ttk.Label(memory_frame, text="السرعة:", font=self.arabic_font).grid(row=0, column=2, sticky=tk.W, pady=2, padx=(20, 0))
        self.memory_speed_var = tk.StringVar()
        ttk.Entry(memory_frame, textvariable=self.memory_speed_var, font=self.arabic_font, width=15).grid(row=0, column=3, sticky=tk.W, pady=2, padx=(5, 0))
        
        # تكوين الشبكة
        battery_frame.columnconfigure(1, weight=1)
        battery_frame.columnconfigure(3, weight=1)
        tripod_frame.columnconfigure(1, weight=1)
        memory_frame.columnconfigure(1, weight=1)
        memory_frame.columnconfigure(3, weight=1)
    
    def on_category_change(self, event=None):
        """معالج تغيير الفئة"""
        # يمكن إضافة منطق خاص حسب الفئة المختارة
        pass

    def populate_fields(self):
        """تعبئة الحقول في وضع التعديل"""
        if not self.item_data:
            return

        self.name_var.set(self.item_data.get('name', ''))
        self.brand_var.set(self.item_data.get('brand', ''))
        self.model_var.set(self.item_data.get('model', ''))
        self.part_number_var.set(self.item_data.get('part_number', ''))

        # تعبئة الفئة
        category_name = self.item_data.get('category_name', '')
        if category_name:
            self.category_var.set(category_name)

        # تعبئة المورد
        supplier_name = self.item_data.get('supplier_name', '')
        if supplier_name:
            self.supplier_var.set(supplier_name)

        # تعبئة الأسعار والمخزون
        self.unit_price_var.set(str(self.item_data.get('unit_price', 0.0)))
        self.rental_price_var.set(str(self.item_data.get('rental_price', 0.0)))
        self.current_stock_var.set(str(self.item_data.get('current_stock', 0)))
        self.min_stock_var.set(str(self.item_data.get('min_stock_level', 5)))

        # تعبئة الحقول الخاصة
        if self.item_data.get('battery_level'):
            self.battery_level_var.set(str(self.item_data['battery_level']))
        if self.item_data.get('battery_condition'):
            self.battery_condition_var.set(self.item_data['battery_condition'])
        if self.item_data.get('weight_capacity'):
            self.weight_capacity_var.set(str(self.item_data['weight_capacity']))
        if self.item_data.get('memory_size'):
            self.memory_size_var.set(str(self.item_data['memory_size']))
        if self.item_data.get('memory_speed'):
            self.memory_speed_var.set(self.item_data['memory_speed'])

        # تعبئة الحقول النصية
        if self.item_data.get('description'):
            self.description_text.insert(tk.END, self.item_data['description'])
        if self.item_data.get('compatible_equipment'):
            self.compatible_equipment_text.insert(tk.END, self.item_data['compatible_equipment'])
        if self.item_data.get('notes'):
            self.notes_text.insert(tk.END, self.item_data['notes'])
        if self.item_data.get('expiry_date'):
            self.expiry_date_var.set(self.item_data['expiry_date'])

    def validate_fields(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم القطعة")
            return False

        if not self.category_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار فئة القطعة")
            return False

        # التحقق من الأسعار
        try:
            float(self.unit_price_var.get())
            float(self.rental_price_var.get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أسعار صحيحة")
            return False

        # التحقق من المخزون
        try:
            int(self.current_stock_var.get())
            int(self.min_stock_var.get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كميات مخزون صحيحة")
            return False

        # التحقق من الحقول الخاصة
        if self.battery_level_var.get():
            try:
                level = int(self.battery_level_var.get())
                if level < 0 or level > 100:
                    messagebox.showerror("خطأ", "مستوى الشحن يجب أن يكون بين 0 و 100")
                    return False
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مستوى شحن صحيح")
                return False

        if self.weight_capacity_var.get():
            try:
                float(self.weight_capacity_var.get())
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال قدرة تحمل صحيحة")
                return False

        if self.memory_size_var.get():
            try:
                int(self.memory_size_var.get())
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال حجم ذاكرة صحيح")
                return False

        return True

    def get_category_id(self, category_name: str) -> Optional[int]:
        """الحصول على معرف الفئة من الاسم"""
        for category in self.categories:
            if category['name'] == category_name:
                return category['id']
        return None

    def get_supplier_id(self, supplier_name: str) -> Optional[int]:
        """الحصول على معرف المورد من الاسم"""
        if not supplier_name:
            return None
        for supplier in self.suppliers:
            if supplier['name'] == supplier_name:
                return supplier['id']
        return None

    def save_item(self):
        """حفظ قطعة الغيار"""
        if not self.validate_fields():
            return

        try:
            # جمع البيانات
            name = self.name_var.get().strip()
            category_id = self.get_category_id(self.category_var.get())
            brand = self.brand_var.get().strip() or None
            model = self.model_var.get().strip() or None
            part_number = self.part_number_var.get().strip() or None
            description = self.description_text.get(1.0, tk.END).strip() or None
            unit_price = float(self.unit_price_var.get())
            rental_price = float(self.rental_price_var.get())
            min_stock_level = int(self.min_stock_var.get())
            current_stock = int(self.current_stock_var.get())
            supplier_id = self.get_supplier_id(self.supplier_var.get())
            expiry_date = self.expiry_date_var.get().strip() or None

            # الحقول الخاصة
            battery_level = int(self.battery_level_var.get()) if self.battery_level_var.get() else None
            battery_condition = self.battery_condition_var.get() or None
            weight_capacity = float(self.weight_capacity_var.get()) if self.weight_capacity_var.get() else None
            memory_size = int(self.memory_size_var.get()) if self.memory_size_var.get() else None
            memory_speed = self.memory_speed_var.get().strip() or None
            compatible_equipment = self.compatible_equipment_text.get(1.0, tk.END).strip() or None
            notes = self.notes_text.get(1.0, tk.END).strip() or None

            # حفظ البيانات
            if self.item_data:  # تعديل
                success = self.db.update_inventory_item(
                    self.item_data['id'], name, category_id, brand, model, part_number,
                    description, unit_price, rental_price, min_stock_level, current_stock,
                    supplier_id, expiry_date, battery_level, battery_condition,
                    weight_capacity, memory_size, memory_speed, compatible_equipment, notes
                )
                action = "تحديث"
            else:  # إضافة جديدة
                success = self.db.add_inventory_item(
                    name, category_id, brand, model, part_number, description,
                    unit_price, rental_price, min_stock_level, current_stock,
                    supplier_id, expiry_date, battery_level, battery_condition,
                    weight_capacity, memory_size, memory_speed, compatible_equipment, notes
                )
                action = "إضافة"

            if success:
                messagebox.showinfo("نجح", f"تم {action} قطعة الغيار بنجاح")
                self.result = True
                if self.callback:
                    self.callback()
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", f"فشل في {action} قطعة الغيار")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def cancel(self):
        """إلغاء النموذج"""
        self.result = False
        self.dialog.destroy()

    def show(self) -> bool:
        """عرض النموذج وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result or False

class SupplierForm:
    """نموذج إضافة/تعديل مورد"""

    def __init__(self, parent, db_manager, supplier_data: Optional[Dict] = None,
                 callback: Optional[Callable] = None):
        """تهيئة النموذج"""
        self.parent = parent
        self.db = db_manager
        self.supplier_data = supplier_data
        self.callback = callback
        self.result = None

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء النافذة
        self.create_dialog()

    def create_dialog(self):
        """إنشاء نافذة النموذج"""
        self.dialog = tk.Toplevel(self.parent)
        title = "تعديل مورد" if self.supplier_data else "إضافة مورد جديد"
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")

        # إنشاء الواجهة
        self.create_interface()

        # تعبئة البيانات إذا كان في وضع التعديل
        if self.supplier_data:
            self.populate_fields()

    def create_interface(self):
        """إنشاء واجهة النموذج"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # اسم المورد
        ttk.Label(main_frame, text="اسم المورد:", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.name_var, font=self.arabic_font, width=40).grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # الشخص المسؤول
        ttk.Label(main_frame, text="الشخص المسؤول:", font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.contact_person_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.contact_person_var, font=self.arabic_font, width=40).grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # رقم الهاتف
        ttk.Label(main_frame, text="رقم الهاتف:", font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=5)
        self.phone_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.phone_var, font=self.arabic_font, width=40).grid(row=2, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # البريد الإلكتروني
        ttk.Label(main_frame, text="البريد الإلكتروني:", font=self.arabic_font).grid(row=3, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.email_var, font=self.arabic_font, width=40).grid(row=3, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # العنوان
        ttk.Label(main_frame, text="العنوان:", font=self.arabic_font).grid(row=4, column=0, sticky=tk.NW, pady=5)
        self.address_text = tk.Text(main_frame, height=3, width=40, font=self.arabic_font)
        self.address_text.grid(row=4, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # ملاحظات
        ttk.Label(main_frame, text="ملاحظات:", font=self.arabic_font).grid(row=5, column=0, sticky=tk.NW, pady=5)
        self.notes_text = tk.Text(main_frame, height=4, width=40, font=self.arabic_font)
        self.notes_text.grid(row=5, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=6, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="حفظ", command=self.save_supplier).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.LEFT)

        # تكوين الشبكة
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        main_frame.rowconfigure(5, weight=1)

    def populate_fields(self):
        """تعبئة الحقول في وضع التعديل"""
        if not self.supplier_data:
            return

        self.name_var.set(self.supplier_data.get('name', ''))
        self.contact_person_var.set(self.supplier_data.get('contact_person', ''))
        self.phone_var.set(self.supplier_data.get('phone', ''))
        self.email_var.set(self.supplier_data.get('email', ''))

        if self.supplier_data.get('address'):
            self.address_text.insert(tk.END, self.supplier_data['address'])
        if self.supplier_data.get('notes'):
            self.notes_text.insert(tk.END, self.supplier_data['notes'])

    def validate_fields(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
            return False
        return True

    def save_supplier(self):
        """حفظ المورد"""
        if not self.validate_fields():
            return

        try:
            name = self.name_var.get().strip()
            contact_person = self.contact_person_var.get().strip() or None
            phone = self.phone_var.get().strip() or None
            email = self.email_var.get().strip() or None
            address = self.address_text.get(1.0, tk.END).strip() or None
            notes = self.notes_text.get(1.0, tk.END).strip() or None

            if self.supplier_data:  # تعديل
                success = self.db.update_supplier(
                    self.supplier_data['id'], name, contact_person, phone, email, address, notes
                )
                action = "تحديث"
            else:  # إضافة جديدة
                success = self.db.add_supplier(name, contact_person, phone, email, address, notes)
                action = "إضافة"

            if success:
                messagebox.showinfo("نجح", f"تم {action} المورد بنجاح")
                self.result = True
                if self.callback:
                    self.callback()
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", f"فشل في {action} المورد")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def cancel(self):
        """إلغاء النموذج"""
        self.result = False
        self.dialog.destroy()

    def show(self) -> bool:
        """عرض النموذج وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result or False

class InventoryMovementForm:
    """نموذج إضافة حركة مخزون"""

    def __init__(self, parent, db_manager, items: List[Dict], suppliers: List[Dict],
                 movement_data: Optional[Dict] = None, callback: Optional[Callable] = None):
        """تهيئة النموذج"""
        self.parent = parent
        self.db = db_manager
        self.items = items
        self.suppliers = suppliers
        self.movement_data = movement_data
        self.callback = callback
        self.result = None

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء النافذة
        self.create_dialog()

    def create_dialog(self):
        """إنشاء نافذة النموذج"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("إضافة حركة مخزون")
        self.dialog.geometry("500x450")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")

        # إنشاء الواجهة
        self.create_interface()

    def create_interface(self):
        """إنشاء واجهة النموذج"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # القطعة
        ttk.Label(main_frame, text="القطعة:", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.item_var = tk.StringVar()
        self.item_combo = ttk.Combobox(main_frame, textvariable=self.item_var, state="readonly", width=40)
        item_names = [f"{item['name']} - {item['brand'] or ''} {item['model'] or ''}".strip() for item in self.items]
        self.item_combo['values'] = item_names
        self.item_combo.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # نوع الحركة
        ttk.Label(main_frame, text="نوع الحركة:", font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.movement_type_var = tk.StringVar()
        movement_type_combo = ttk.Combobox(main_frame, textvariable=self.movement_type_var, state="readonly", width=40)
        movement_types = [
            ("Purchase", "شراء"),
            ("Sale", "بيع"),
            ("Rental", "إيجار"),
            ("Return", "إرجاع"),
            ("Adjustment", "تعديل"),
            ("Damage", "تلف"),
            ("Loss", "فقدان")
        ]
        movement_type_combo['values'] = [f"{arabic} ({english})" for english, arabic in movement_types]
        movement_type_combo.grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))
        movement_type_combo.bind('<<ComboboxSelected>>', self.on_movement_type_change)

        # الكمية
        ttk.Label(main_frame, text="الكمية:", font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=5)
        self.quantity_var = tk.StringVar(value="1")
        ttk.Entry(main_frame, textvariable=self.quantity_var, font=self.arabic_font, width=40).grid(row=2, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # سعر الوحدة
        ttk.Label(main_frame, text="سعر الوحدة:", font=self.arabic_font).grid(row=3, column=0, sticky=tk.W, pady=5)
        self.unit_cost_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.unit_cost_var, font=self.arabic_font, width=40).grid(row=3, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # المورد
        ttk.Label(main_frame, text="المورد:", font=self.arabic_font).grid(row=4, column=0, sticky=tk.W, pady=5)
        self.supplier_var = tk.StringVar()
        self.supplier_combo = ttk.Combobox(main_frame, textvariable=self.supplier_var, state="readonly", width=40)
        supplier_names = [""] + [sup['name'] for sup in self.suppliers]
        self.supplier_combo['values'] = supplier_names
        self.supplier_combo.grid(row=4, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # رقم المرجع
        ttk.Label(main_frame, text="رقم المرجع:", font=self.arabic_font).grid(row=5, column=0, sticky=tk.W, pady=5)
        self.reference_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.reference_var, font=self.arabic_font, width=40).grid(row=5, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # تاريخ الحركة
        ttk.Label(main_frame, text="تاريخ الحركة:", font=self.arabic_font).grid(row=6, column=0, sticky=tk.W, pady=5)
        self.movement_date_var = tk.StringVar(value=date.today().isoformat())
        ttk.Entry(main_frame, textvariable=self.movement_date_var, font=self.arabic_font, width=40).grid(row=6, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # ملاحظات
        ttk.Label(main_frame, text="ملاحظات:", font=self.arabic_font).grid(row=7, column=0, sticky=tk.NW, pady=5)
        self.notes_text = tk.Text(main_frame, height=4, width=40, font=self.arabic_font)
        self.notes_text.grid(row=7, column=1, sticky=tk.W+tk.E, pady=5, padx=(10, 0))

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=8, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="حفظ", command=self.save_movement).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.LEFT)

        # تكوين الشبكة
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)

    def on_movement_type_change(self, event=None):
        """معالج تغيير نوع الحركة"""
        movement_type = self.movement_type_var.get()

        # إظهار/إخفاء حقول حسب نوع الحركة
        if "شراء" in movement_type or "بيع" in movement_type:
            # إظهار حقل المورد وسعر الوحدة
            pass
        else:
            # إخفاء حقل المورد
            pass

    def get_item_id(self, item_display: str) -> Optional[int]:
        """الحصول على معرف القطعة من النص المعروض"""
        for i, item in enumerate(self.items):
            display = f"{item['name']} - {item['brand'] or ''} {item['model'] or ''}".strip()
            if display == item_display:
                return item['id']
        return None

    def get_supplier_id(self, supplier_name: str) -> Optional[int]:
        """الحصول على معرف المورد من الاسم"""
        if not supplier_name:
            return None
        for supplier in self.suppliers:
            if supplier['name'] == supplier_name:
                return supplier['id']
        return None

    def get_movement_type_code(self, movement_display: str) -> str:
        """الحصول على كود نوع الحركة من النص المعروض"""
        movement_types = {
            "شراء": "Purchase",
            "بيع": "Sale",
            "إيجار": "Rental",
            "إرجاع": "Return",
            "تعديل": "Adjustment",
            "تلف": "Damage",
            "فقدان": "Loss"
        }

        for arabic, english in movement_types.items():
            if arabic in movement_display:
                return english
        return "Adjustment"

    def validate_fields(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.item_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار القطعة")
            return False

        if not self.movement_type_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار نوع الحركة")
            return False

        try:
            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
            return False

        if self.unit_cost_var.get():
            try:
                float(self.unit_cost_var.get())
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر وحدة صحيح")
                return False

        return True

    def save_movement(self):
        """حفظ حركة المخزون"""
        if not self.validate_fields():
            return

        try:
            item_id = self.get_item_id(self.item_var.get())
            movement_type = self.get_movement_type_code(self.movement_type_var.get())
            quantity = int(self.quantity_var.get())
            unit_cost = float(self.unit_cost_var.get()) if self.unit_cost_var.get() else None
            supplier_id = self.get_supplier_id(self.supplier_var.get())
            reference_number = self.reference_var.get().strip() or None
            movement_date = self.movement_date_var.get().strip() or None
            notes = self.notes_text.get(1.0, tk.END).strip() or None

            success = self.db.add_inventory_movement(
                item_id, movement_type, quantity, unit_cost, supplier_id,
                reference_number, notes, movement_date
            )

            if success:
                messagebox.showinfo("نجح", "تم إضافة حركة المخزون بنجاح")
                self.result = True
                if self.callback:
                    self.callback()
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة حركة المخزون")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def cancel(self):
        """إلغاء النموذج"""
        self.result = False
        self.dialog.destroy()

    def show(self) -> bool:
        """عرض النموذج وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result or False

class RentalInventoryForm:
    """نموذج إضافة قطع غيار للإيجار"""

    def __init__(self, parent, db_manager, rental_id: int, callback: Optional[Callable] = None):
        """تهيئة النموذج"""
        self.parent = parent
        self.db = db_manager
        self.rental_id = rental_id
        self.callback = callback
        self.result = None
        self.selected_items = []

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء النافذة
        self.create_dialog()

    def create_dialog(self):
        """إنشاء نافذة النموذج"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("إضافة قطع غيار للإيجار")
        self.dialog.geometry("800x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_available_items()

    def create_interface(self):
        """إنشاء واجهة النموذج"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title_label = ttk.Label(main_frame, text=f"إضافة قطع غيار للإيجار رقم {self.rental_id}",
                               font=self.arabic_font)
        title_label.pack(pady=(0, 10))

        # إطار البحث
        search_frame = ttk.Frame(main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="البحث:", font=self.arabic_font).pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, font=self.arabic_font, width=30)
        search_entry.pack(side=tk.LEFT, padx=(5, 0))
        search_entry.bind('<KeyRelease>', self.on_search_change)

        # إطار القوائم
        lists_frame = ttk.Frame(main_frame)
        lists_frame.pack(fill=tk.BOTH, expand=True)

        # قائمة القطع المتاحة
        available_frame = ttk.LabelFrame(lists_frame, text="القطع المتاحة")
        available_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        self.available_listbox = tk.Listbox(available_frame, font=self.arabic_font)
        available_scrollbar = ttk.Scrollbar(available_frame, orient=tk.VERTICAL,
                                           command=self.available_listbox.yview)
        self.available_listbox.configure(yscrollcommand=available_scrollbar.set)

        self.available_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار التحكم
        buttons_frame = ttk.Frame(lists_frame)
        buttons_frame.pack(side=tk.LEFT, padx=10)

        ttk.Button(buttons_frame, text="إضافة →",
                  command=self.add_item).pack(pady=5)
        ttk.Button(buttons_frame, text="← إزالة",
                  command=self.remove_item).pack(pady=5)

        # قائمة القطع المختارة
        selected_frame = ttk.LabelFrame(lists_frame, text="القطع المختارة")
        selected_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # جدول القطع المختارة
        selected_columns = ("name", "quantity", "price")
        self.selected_tree = ttk.Treeview(selected_frame, columns=selected_columns, show="headings", height=15)

        self.selected_tree.heading("name", text="اسم القطعة")
        self.selected_tree.heading("quantity", text="الكمية")
        self.selected_tree.heading("price", text="سعر الإيجار")

        self.selected_tree.column("name", width=200)
        self.selected_tree.column("quantity", width=80)
        self.selected_tree.column("price", width=100)

        selected_scrollbar = ttk.Scrollbar(selected_frame, orient=tk.VERTICAL,
                                          command=self.selected_tree.yview)
        self.selected_tree.configure(yscrollcommand=selected_scrollbar.set)

        self.selected_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # أزرار الحفظ والإلغاء
        action_buttons_frame = ttk.Frame(main_frame)
        action_buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(action_buttons_frame, text="حفظ",
                  command=self.save_items).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_buttons_frame, text="إلغاء",
                  command=self.cancel).pack(side=tk.LEFT)

        # إجمالي السعر
        self.total_label = ttk.Label(action_buttons_frame, text="الإجمالي: 0.00",
                                    font=self.arabic_font)
        self.total_label.pack(side=tk.RIGHT)

    def load_available_items(self):
        """تحميل القطع المتاحة"""
        try:
            # الحصول على القطع التي لديها مخزون متاح
            items = self.db.get_inventory_items()
            available_items = [item for item in items if item.get('current_stock', 0) > 0]

            self.available_items = available_items
            self.display_available_items(available_items)

        except Exception as e:
            print(f"خطأ في تحميل القطع المتاحة: {e}")

    def display_available_items(self, items):
        """عرض القطع المتاحة"""
        self.available_listbox.delete(0, tk.END)

        for item in items:
            display_text = f"{item['name']} - {item.get('brand', '')} {item.get('model', '')} (متوفر: {item.get('current_stock', 0)})"
            self.available_listbox.insert(tk.END, display_text.strip())

    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        search_term = self.search_var.get().strip().lower()

        if search_term:
            filtered_items = [
                item for item in self.available_items
                if search_term in item.get('name', '').lower() or
                   search_term in item.get('brand', '').lower() or
                   search_term in item.get('model', '').lower()
            ]
        else:
            filtered_items = self.available_items

        self.display_available_items(filtered_items)

    def add_item(self):
        """إضافة قطعة للقائمة المختارة"""
        selection = self.available_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قطعة لإضافتها")
            return

        # الحصول على القطعة المختارة
        selected_index = selection[0]
        display_text = self.available_listbox.get(selected_index)

        # البحث عن القطعة في القائمة
        item = None
        for available_item in self.available_items:
            item_display = f"{available_item['name']} - {available_item.get('brand', '')} {available_item.get('model', '')} (متوفر: {available_item.get('current_stock', 0)})"
            if item_display.strip() == display_text:
                item = available_item
                break

        if not item:
            messagebox.showerror("خطأ", "لم يتم العثور على القطعة")
            return

        # التحقق من عدم إضافة القطعة مسبقاً
        for selected_item in self.selected_items:
            if selected_item['item_id'] == item['id']:
                messagebox.showwarning("تحذير", "هذه القطعة مضافة مسبقاً")
                return

        # طلب الكمية وسعر الإيجار
        quantity_dialog = QuantityPriceDialog(self.dialog, item)
        result = quantity_dialog.show()

        if result:
            quantity, rental_price = result

            # التحقق من توفر الكمية
            if quantity > item.get('current_stock', 0):
                messagebox.showerror("خطأ", "الكمية المطلوبة أكبر من المتوفر")
                return

            # إضافة القطعة للقائمة المختارة
            selected_item = {
                'item_id': item['id'],
                'name': item['name'],
                'quantity': quantity,
                'rental_price': rental_price
            }

            self.selected_items.append(selected_item)
            self.update_selected_display()

    def remove_item(self):
        """إزالة قطعة من القائمة المختارة"""
        selection = self.selected_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قطعة لإزالتها")
            return

        # الحصول على فهرس القطعة
        item_id = self.selected_tree.item(selection[0])['values'][0]

        # إزالة القطعة من القائمة
        self.selected_items = [item for item in self.selected_items if item['name'] != item_id]
        self.update_selected_display()

    def update_selected_display(self):
        """تحديث عرض القطع المختارة"""
        # مسح العرض السابق
        for item in self.selected_tree.get_children():
            self.selected_tree.delete(item)

        # إضافة القطع المختارة
        total = 0
        for item in self.selected_items:
            values = (
                item['name'],
                item['quantity'],
                f"{item['rental_price']:.2f}"
            )
            self.selected_tree.insert("", tk.END, values=values)
            total += item['quantity'] * item['rental_price']

        # تحديث الإجمالي
        self.total_label.config(text=f"الإجمالي: {total:.2f}")

    def save_items(self):
        """حفظ القطع المختارة"""
        if not self.selected_items:
            messagebox.showwarning("تحذير", "يرجى اختيار قطع غيار لإضافتها")
            return

        try:
            # تحضير البيانات للحفظ
            items_data = []
            for item in self.selected_items:
                items_data.append({
                    'item_id': item['item_id'],
                    'quantity': item['quantity'],
                    'rental_price': item['rental_price']
                })

            # حفظ القطع في قاعدة البيانات
            success = self.db.add_rental_inventory_items(self.rental_id, items_data)

            if success:
                messagebox.showinfo("نجح", "تم إضافة قطع الغيار للإيجار بنجاح")
                self.result = True
                if self.callback:
                    self.callback()
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة قطع الغيار للإيجار")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def cancel(self):
        """إلغاء النموذج"""
        self.result = False
        self.dialog.destroy()

    def show(self) -> bool:
        """عرض النموذج وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result or False

class QuantityPriceDialog:
    """نافذة إدخال الكمية وسعر الإيجار"""

    def __init__(self, parent, item_data: Dict):
        """تهيئة النافذة"""
        self.parent = parent
        self.item_data = item_data
        self.result = None

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء النافذة
        self.create_dialog()

    def create_dialog(self):
        """إنشاء النافذة"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("تحديد الكمية والسعر")
        self.dialog.geometry("400x250")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 100}+{self.parent.winfo_rooty() + 100}")

        # إنشاء الواجهة
        self.create_interface()

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات القطعة
        info_label = ttk.Label(main_frame, text=f"القطعة: {self.item_data['name']}",
                              font=self.arabic_font)
        info_label.pack(pady=(0, 10))

        stock_label = ttk.Label(main_frame, text=f"المتوفر: {self.item_data.get('current_stock', 0)}",
                               font=self.arabic_font)
        stock_label.pack(pady=(0, 20))

        # الكمية
        quantity_frame = ttk.Frame(main_frame)
        quantity_frame.pack(fill=tk.X, pady=5)

        ttk.Label(quantity_frame, text="الكمية:", font=self.arabic_font).pack(side=tk.LEFT)
        self.quantity_var = tk.StringVar(value="1")
        quantity_spinbox = ttk.Spinbox(quantity_frame, textvariable=self.quantity_var,
                                      from_=1, to=self.item_data.get('current_stock', 1),
                                      width=10)
        quantity_spinbox.pack(side=tk.RIGHT)

        # سعر الإيجار
        price_frame = ttk.Frame(main_frame)
        price_frame.pack(fill=tk.X, pady=5)

        ttk.Label(price_frame, text="سعر الإيجار:", font=self.arabic_font).pack(side=tk.LEFT)
        self.price_var = tk.StringVar(value=str(self.item_data.get('rental_price', 0.0)))
        ttk.Entry(price_frame, textvariable=self.price_var, width=15).pack(side=tk.RIGHT)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(buttons_frame, text="موافق", command=self.save).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.LEFT)

    def save(self):
        """حفظ البيانات"""
        try:
            quantity = int(self.quantity_var.get())
            price = float(self.price_var.get())

            if quantity <= 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون أكبر من صفر")
                return

            if quantity > self.item_data.get('current_stock', 0):
                messagebox.showerror("خطأ", "الكمية أكبر من المتوفر")
                return

            if price < 0:
                messagebox.showerror("خطأ", "السعر يجب أن يكون أكبر من أو يساوي صفر")
                return

            self.result = (quantity, price)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

    def cancel(self):
        """إلغاء النافذة"""
        self.result = None
        self.dialog.destroy()

    def show(self):
        """عرض النافذة وانتظار النتيجة"""
        self.dialog.wait_window()
        return self.result
