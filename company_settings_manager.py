#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير إعدادات الشركة - إدارة الشعار والهوية البصرية
Company Settings Manager - Handle logo and branding
"""

import os
import shutil
from datetime import datetime
from typing import Optional, Dict, Tuple
from PIL import Image
import uuid

class CompanySettingsManager:
    """مدير إعدادات الشركة والهوية البصرية"""
    
    def __init__(self, db_manager, base_path: str = "company_assets"):
        """تهيئة مدير إعدادات الشركة"""
        self.db = db_manager
        self.base_path = base_path
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.svg'}
        self.max_file_size = 10 * 1024 * 1024  # 10 MB
        self.logo_sizes = {
            'original': None,
            'large': (400, 400),
            'medium': (200, 200),
            'small': (100, 100),
            'thumbnail': (50, 50)
        }
        
        # إنشاء المجلدات المطلوبة
        self._create_directories()
    
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            self.base_path,
            os.path.join(self.base_path, "logos"),
            os.path.join(self.base_path, "backgrounds"),
            os.path.join(self.base_path, "templates"),
            os.path.join(self.base_path, "exports")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def validate_logo_file(self, file_path: str) -> Tuple[bool, str]:
        """التحقق من صحة ملف الشعار"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                return False, "الملف غير موجود"
            
            # التحقق من حجم الملف
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return False, f"حجم الملف كبير جداً (أقصى حجم: {self.max_file_size // (1024*1024)} MB)"
            
            # التحقق من امتداد الملف
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return False, f"صيغة الملف غير مدعومة. الصيغ المدعومة: {', '.join(self.supported_formats)}"
            
            # التحقق من صحة الصورة (إلا إذا كانت SVG)
            if file_ext != '.svg':
                with Image.open(file_path) as img:
                    img.verify()
            
            return True, "الشعار صحيح"
            
        except Exception as e:
            return False, f"خطأ في التحقق من الشعار: {str(e)}"
    
    def resize_logo(self, input_path: str, output_path: str, size: Tuple[int, int]) -> bool:
        """تغيير حجم الشعار"""
        try:
            # تخطي SVG files
            if input_path.lower().endswith('.svg'):
                shutil.copy2(input_path, output_path)
                return True
            
            with Image.open(input_path) as img:
                # تحويل إلى RGBA للحفاظ على الشفافية
                if img.mode != 'RGBA':
                    img = img.convert('RGBA')
                
                # تغيير الحجم مع الحفاظ على النسبة
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # إنشاء صورة جديدة بالحجم المطلوب مع خلفية شفافة
                new_img = Image.new('RGBA', size, (255, 255, 255, 0))
                
                # وضع الصورة في المنتصف
                x = (size[0] - img.width) // 2
                y = (size[1] - img.height) // 2
                new_img.paste(img, (x, y), img)
                
                # حفظ الصورة
                new_img.save(output_path, 'PNG', optimize=True)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تغيير حجم الشعار: {e}")
            return False
    
    def save_company_logo(self, source_path: str) -> Tuple[bool, str, Optional[str]]:
        """حفظ شعار الشركة بأحجام مختلفة"""
        try:
            # التحقق من صحة الشعار
            is_valid, message = self.validate_logo_file(source_path)
            if not is_valid:
                return False, message, None
            
            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            file_ext = os.path.splitext(source_path)[1].lower()
            
            # حفظ الشعار الأصلي
            original_filename = f"logo_original_{timestamp}_{unique_id}{file_ext}"
            original_path = os.path.join(self.base_path, "logos", original_filename)
            shutil.copy2(source_path, original_path)
            
            # إنشاء أحجام مختلفة للشعار
            logo_paths = {'original': f"logos/{original_filename}"}
            
            for size_name, size_dimensions in self.logo_sizes.items():
                if size_name == 'original' or not size_dimensions:
                    continue
                
                size_filename = f"logo_{size_name}_{timestamp}_{unique_id}.png"
                size_path = os.path.join(self.base_path, "logos", size_filename)
                
                if self.resize_logo(original_path, size_path, size_dimensions):
                    logo_paths[size_name] = f"logos/{size_filename}"
            
            # تحديث إعدادات قاعدة البيانات
            self.db.update_company_setting('company_logo', logo_paths['original'])
            
            # حفظ مسارات الأحجام المختلفة
            for size_name, path in logo_paths.items():
                if size_name != 'original':
                    self.db.update_company_setting(f'company_logo_{size_name}', path)
            
            return True, "تم حفظ الشعار بنجاح", logo_paths['original']
            
        except Exception as e:
            return False, f"خطأ في حفظ الشعار: {str(e)}", None
    
    def get_logo_path(self, size: str = 'original') -> Optional[str]:
        """الحصول على مسار الشعار بحجم محدد"""
        try:
            if size == 'original':
                setting_key = 'company_logo'
            else:
                setting_key = f'company_logo_{size}'
            
            relative_path = self.db.get_company_setting(setting_key)
            if relative_path:
                full_path = os.path.join(self.base_path, relative_path.replace('logos/', ''))
                if os.path.exists(full_path):
                    return full_path
            
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على مسار الشعار: {e}")
            return None
    
    def delete_current_logo(self) -> bool:
        """حذف الشعار الحالي"""
        try:
            # حذف جميع أحجام الشعار
            for size_name in self.logo_sizes.keys():
                logo_path = self.get_logo_path(size_name)
                if logo_path and os.path.exists(logo_path):
                    os.remove(logo_path)
                
                # مسح الإعداد من قاعدة البيانات
                if size_name == 'original':
                    self.db.update_company_setting('company_logo', '')
                else:
                    self.db.update_company_setting(f'company_logo_{size_name}', '')
            
            return True
            
        except Exception as e:
            print(f"خطأ في حذف الشعار: {e}")
            return False
    
    def get_company_info(self) -> Dict[str, str]:
        """الحصول على معلومات الشركة الكاملة"""
        return self.db.get_all_company_settings()
    
    def update_company_info(self, settings: Dict[str, str]) -> bool:
        """تحديث معلومات الشركة"""
        return self.db.update_multiple_company_settings(settings)
    
    def export_branding_package(self, export_path: str) -> Tuple[bool, str]:
        """تصدير حزمة الهوية البصرية"""
        try:
            # إنشاء مجلد التصدير
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_dir = os.path.join(export_path, f"branding_package_{timestamp}")
            os.makedirs(export_dir, exist_ok=True)
            
            # نسخ جميع أحجام الشعار
            logos_dir = os.path.join(export_dir, "logos")
            os.makedirs(logos_dir, exist_ok=True)
            
            for size_name in self.logo_sizes.keys():
                logo_path = self.get_logo_path(size_name)
                if logo_path and os.path.exists(logo_path):
                    filename = f"logo_{size_name}{os.path.splitext(logo_path)[1]}"
                    shutil.copy2(logo_path, os.path.join(logos_dir, filename))
            
            # إنشاء ملف معلومات الشركة
            company_info = self.get_company_info()
            info_file = os.path.join(export_dir, "company_info.txt")
            
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write("معلومات الشركة\n")
                f.write("=" * 50 + "\n\n")
                
                for key, value in company_info.items():
                    if not key.startswith('company_logo_'):
                        f.write(f"{key}: {value}\n")
                
                f.write(f"\nتاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            return True, f"تم تصدير حزمة الهوية البصرية إلى: {export_dir}"
            
        except Exception as e:
            return False, f"خطأ في تصدير حزمة الهوية البصرية: {str(e)}"
    
    def get_logo_info(self) -> Dict[str, any]:
        """الحصول على معلومات الشعار الحالي"""
        info = {
            'has_logo': False,
            'sizes': {},
            'created_date': None
        }
        
        try:
            original_path = self.get_logo_path('original')
            if original_path and os.path.exists(original_path):
                info['has_logo'] = True
                info['created_date'] = datetime.fromtimestamp(os.path.getctime(original_path))
                
                # معلومات الأحجام المختلفة
                for size_name in self.logo_sizes.keys():
                    size_path = self.get_logo_path(size_name)
                    if size_path and os.path.exists(size_path):
                        file_size = os.path.getsize(size_path)
                        
                        # الحصول على أبعاد الصورة
                        try:
                            if not size_path.lower().endswith('.svg'):
                                with Image.open(size_path) as img:
                                    dimensions = img.size
                            else:
                                dimensions = ('SVG', 'Vector')
                        except:
                            dimensions = ('Unknown', 'Unknown')
                        
                        info['sizes'][size_name] = {
                            'path': size_path,
                            'size_bytes': file_size,
                            'size_mb': round(file_size / (1024 * 1024), 2),
                            'dimensions': dimensions
                        }
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات الشعار: {e}")
        
        return info

# مثال على الاستخدام
if __name__ == "__main__":
    from database import DatabaseManager
    
    db = DatabaseManager()
    settings_manager = CompanySettingsManager(db)
    
    print("مدير إعدادات الشركة جاهز للاستخدام")
    print(f"الصيغ المدعومة: {settings_manager.supported_formats}")
    print(f"أقصى حجم للملف: {settings_manager.max_file_size // (1024*1024)} MB")
