{% extends "base.html" %}

{% block title %}لوحة تحكم المخزون{% endblock %}

{% block extra_css %}
<style>
    .inventory-card {
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    
    .inventory-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .low-stock-card {
        border-left-color: #dc3545;
    }
    
    .expiring-card {
        border-left-color: #ffc107;
    }
    
    .category-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .alert-item {
        border-radius: 8px;
        margin-bottom: 10px;
        padding: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-boxes me-2"></i>لوحة تحكم المخزون</h2>
                    <p class="text-muted">إدارة شاملة لجميع عناصر المخزون والإكسسوارات</p>
                </div>
                <div>
                    <a href="{{ url_for('inventory_items') }}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>عرض جميع العناصر
                    </a>
                    <a href="{{ url_for('add_inventory_item') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة عنصر جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-primary">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <h5>البطاريات</h5>
                    <div class="stat-number">{{ stats.batteries_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-success">
                        <i class="fas fa-plug"></i>
                    </div>
                    <h5>الكابلات</h5>
                    <div class="stat-number">{{ stats.cables_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-warning">
                        <i class="fas fa-charging-station"></i>
                    </div>
                    <h5>أجهزة الشحن</h5>
                    <div class="stat-number">{{ stats.chargers_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-info">
                        <i class="fas fa-camera-retro"></i>
                    </div>
                    <h5>الحوامل</h5>
                    <div class="stat-number">{{ stats.tripods_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Categories -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-purple">
                        <i class="fas fa-sd-card"></i>
                    </div>
                    <h5>بطاقات الذاكرة</h5>
                    <div class="stat-number">{{ stats.memory_cards_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-secondary">
                        <i class="fas fa-filter"></i>
                    </div>
                    <h5>الفلاتر</h5>
                    <div class="stat-number">{{ stats.filters_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card inventory-card">
                <div class="card-body text-center">
                    <div class="category-icon text-dark">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h5>إكسسوارات أخرى</h5>
                    <div class="stat-number">{{ stats.other_accessories_count if stats else 0 }}</div>
                    <small class="text-muted">عنصر متاح</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Section -->
    <div class="row">
        <!-- Low Stock Items -->
        <div class="col-md-6">
            <div class="card low-stock-card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        عناصر بمخزون منخفض
                    </h5>
                </div>
                <div class="card-body">
                    {% if low_stock_items %}
                        {% for item in low_stock_items[:5] %}
                        <div class="alert-item bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ item.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ item.brand }} {{ item.model }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-danger">{{ item.current_stock }}</span>
                                    <br>
                                    <small class="text-muted">الحد الأدنى: {{ item.min_stock_level }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        {% if low_stock_items|length > 5 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('inventory_items', low_stock_only=true) }}" class="btn btn-outline-danger btn-sm">
                                عرض جميع العناصر ({{ low_stock_items|length }})
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>جميع العناصر بمستوى مخزون جيد</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Expiring Items -->
        <div class="col-md-6">
            <div class="card expiring-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        عناصر تنتهي صلاحيتها قريباً
                    </h5>
                </div>
                <div class="card-body">
                    {% if expiring_items %}
                        {% for item in expiring_items[:5] %}
                        <div class="alert-item bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ item.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ item.brand }} {{ item.model }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-warning text-dark">{{ item.days_to_expiry }} يوم</span>
                                    <br>
                                    <small class="text-muted">{{ item.expiry_date }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        {% if expiring_items|length > 5 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('inventory_items', expiring_soon=true) }}" class="btn btn-outline-warning btn-sm">
                                عرض جميع العناصر ({{ expiring_items|length }})
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-check fa-3x mb-3"></i>
                            <p>لا توجد عناصر تنتهي صلاحيتها قريباً</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('add_inventory_item') }}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-plus me-2"></i>إضافة عنصر جديد
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('inventory_items', low_stock_only=true) }}" class="btn btn-outline-danger w-100 mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>المخزون المنخفض
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-success w-100 mb-2" onclick="exportInventoryReport()">
                                <i class="fas fa-file-excel me-2"></i>تصدير تقرير
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-outline-info w-100 mb-2" onclick="printInventoryList()">
                                <i class="fas fa-print me-2"></i>طباعة قائمة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportInventoryReport() {
    // TODO: تنفيذ تصدير تقرير المخزون
    showAlert('info', 'سيتم تنفيذ هذه الميزة قريباً');
}

function printInventoryList() {
    // TODO: تنفيذ طباعة قائمة المخزون
    showAlert('info', 'سيتم تنفيذ هذه الميزة قريباً');
}
</script>
{% endblock %}
