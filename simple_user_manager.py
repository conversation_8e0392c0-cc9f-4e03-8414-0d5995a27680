#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المستخدمين المبسط - يعمل مع قاعدة البيانات الحالية
"""

import sqlite3
import hashlib
from typing import Dict, List, Optional, Tuple
from enum import Enum

class UserRole(Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    MANAGER = "manager"
    EMPLOYEE = "employee"

class Permission(Enum):
    """الصلاحيات"""
    # إدارة المعدات
    EQUIPMENT_VIEW = "equipment_view"
    EQUIPMENT_ADD = "equipment_add"
    EQUIPMENT_EDIT = "equipment_edit"
    EQUIPMENT_DELETE = "equipment_delete"
    
    # إدارة العملاء
    CUSTOMERS_VIEW = "customers_view"
    CUSTOMERS_ADD = "customers_add"
    CUSTOMERS_EDIT = "customers_edit"
    CUSTOMERS_DELETE = "customers_delete"
    
    # إدارة الإيجارات
    RENTALS_VIEW = "rentals_view"
    RENTALS_ADD = "rentals_add"
    RENTALS_EDIT = "rentals_edit"
    RENTALS_DELETE = "rentals_delete"
    RENTALS_RETURN = "rentals_return"
    
    # التقارير
    REPORTS_VIEW = "reports_view"
    REPORTS_EXPORT = "reports_export"

    # إدارة الفواتير
    INVOICES_VIEW = "invoices_view"
    INVOICES_MANAGE = "invoices_manage"
    
    # النظام
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    SYSTEM_SETTINGS = "system_settings"
    
    # إدارة المستخدمين
    USERS_VIEW = "users_view"
    USERS_ADD = "users_add"
    USERS_EDIT = "users_edit"
    USERS_DELETE = "users_delete"

    # إدارة المخزون
    INVENTORY_VIEW = "inventory_view"
    INVENTORY_ADD = "inventory_add"
    INVENTORY_EDIT = "inventory_edit"
    INVENTORY_DELETE = "inventory_delete"

class SimpleUserManager:
    """مدير المستخدمين المبسط"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.current_user = None
        
        # إعداد الصلاحيات لكل دور
        self.role_permissions = {
            UserRole.ADMIN: list(Permission),  # جميع الصلاحيات
            UserRole.MANAGER: list(Permission),  # جميع الصلاحيات
            UserRole.EMPLOYEE: []  # لا توجد صلاحيات افتراضية - يحددها المدير
        }
    
    def authenticate(self, username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """تسجيل دخول المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # البحث عن المستخدم
            cursor.execute("""
                SELECT id, username, password, full_name, role, is_active, failed_login_attempts
                FROM users WHERE username = ?
            """, (username,))
            
            user_data = cursor.fetchone()
            
            if not user_data:
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", None
            
            user_id, db_username, stored_password, full_name, role, is_active, failed_attempts = user_data
            
            # التحقق من حالة المستخدم
            if not is_active:
                return False, "الحساب معطل. يرجى الاتصال بالمدير", None
            
            # التحقق من كلمة المرور
            expected_hash = hashlib.sha256(password.encode()).hexdigest()
            if expected_hash == stored_password:
                # تسجيل دخول ناجح
                cursor.execute("UPDATE users SET failed_login_attempts = 0 WHERE id = ?", (user_id,))
                conn.commit()
                
                # حفظ بيانات المستخدم الحالي
                user_role = UserRole(role)
                self.current_user = {
                    'id': user_id,
                    'username': db_username,
                    'full_name': full_name,
                    'role': user_role,
                    'permissions': self.get_user_permissions(user_role)
                }
                
                conn.close()
                return True, "تم تسجيل الدخول بنجاح", self.current_user
            
            else:
                # كلمة مرور خاطئة
                failed_attempts += 1
                
                if failed_attempts >= 5:
                    cursor.execute("UPDATE users SET failed_login_attempts = ?, is_active = 0 WHERE id = ?", 
                                 (failed_attempts, user_id))
                    conn.commit()
                    conn.close()
                    return False, "تم تعطيل الحساب بسبب المحاولات المتكررة. يرجى الاتصال بالمدير", None
                
                else:
                    cursor.execute("UPDATE users SET failed_login_attempts = ? WHERE id = ?", 
                                 (failed_attempts, user_id))
                    remaining_attempts = 5 - failed_attempts
                    conn.commit()
                    conn.close()
                    return False, f"كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining_attempts}", None
        
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            return False, "حدث خطأ في النظام", None

    def logout(self):
        """تسجيل خروج المستخدم"""
        if self.current_user:
            print(f"تسجيل خروج المستخدم: {self.current_user['username']}")
            self.current_user = None
        return True

    def get_user_permissions(self, role: UserRole) -> List[Permission]:
        """الحصول على صلاحيات الدور"""
        return self.role_permissions.get(role, [])
    
    def has_permission(self, permission: Permission) -> bool:
        """التحقق من صلاحية المستخدم"""
        if not self.current_user:
            return False
        return permission in self.current_user['permissions']
    
    def add_user(self, username: str, password: str, full_name: str, role: UserRole,
                 email: str = "", phone: str = "") -> Tuple[bool, str]:
        """إضافة مستخدم جديد"""
        if not self.has_permission(Permission.USERS_ADD):
            return False, "ليس لديك صلاحية إضافة المستخدمين"
        
        if len(username) < 3:
            return False, "اسم المستخدم يجب أن يكون 3 أحرف على الأقل"
        
        if len(password) < 6:
            return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"
        
        if not full_name.strip():
            return False, "الاسم الكامل مطلوب"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود المستخدم
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                conn.close()
                return False, "اسم المستخدم موجود بالفعل"
            
            # تشفير كلمة المرور
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            
            # إضافة المستخدم
            cursor.execute("""
                INSERT INTO users (username, password, full_name, role, email, phone)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (username, password_hash, full_name, role.value, email, phone))
            
            conn.commit()
            conn.close()
            
            return True, f"تم إضافة المستخدم {username} بنجاح"
        
        except Exception as e:
            return False, f"خطأ في إضافة المستخدم: {str(e)}"
    
    def get_users(self) -> List[Dict]:
        """الحصول على قائمة المستخدمين"""
        if not self.has_permission(Permission.USERS_VIEW):
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, username, full_name, role, email, phone, is_active, created_at, last_login
                FROM users ORDER BY id
            """)
            
            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'full_name': row[2],
                    'role': row[3],
                    'email': row[4],
                    'phone': row[5],
                    'is_active': bool(row[6]),
                    'created_at': row[7],
                    'last_login': row[8]
                })
            
            conn.close()
            return users
        
        except Exception as e:
            print(f"خطأ في الحصول على المستخدمين: {e}")
            return []
    
    def update_user(self, user_id: int, full_name: str = None, role: UserRole = None,
                   email: str = None, phone: str = None, is_active: bool = None) -> Tuple[bool, str]:
        """تحديث بيانات المستخدم"""
        if not self.has_permission(Permission.USERS_EDIT):
            return False, "ليس لديك صلاحية تعديل المستخدمين"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # بناء استعلام التحديث
            updates = []
            params = []
            
            if full_name is not None:
                updates.append("full_name = ?")
                params.append(full_name)
            
            if role is not None:
                updates.append("role = ?")
                params.append(role.value)
            
            if email is not None:
                updates.append("email = ?")
                params.append(email)
            
            if phone is not None:
                updates.append("phone = ?")
                params.append(phone)
            
            if is_active is not None:
                updates.append("is_active = ?")
                params.append(is_active)
            
            if not updates:
                return False, "لا توجد بيانات للتحديث"
            
            params.append(user_id)
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"
            
            cursor.execute(query, params)
            
            if cursor.rowcount == 0:
                conn.close()
                return False, "المستخدم غير موجود"
            
            conn.commit()
            conn.close()
            
            return True, "تم تحديث المستخدم بنجاح"
        
        except Exception as e:
            return False, f"خطأ في تحديث المستخدم: {str(e)}"
    
    def delete_user(self, user_id: int) -> Tuple[bool, str]:
        """حذف مستخدم"""
        if not self.has_permission(Permission.USERS_DELETE):
            return False, "ليس لديك صلاحية حذف المستخدمين"
        
        # منع حذف المستخدم الحالي
        if self.current_user and self.current_user['id'] == user_id:
            return False, "لا يمكن حذف المستخدم الحالي"
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود المستخدم
            cursor.execute("SELECT username FROM users WHERE id = ?", (user_id,))
            user_data = cursor.fetchone()
            
            if not user_data:
                conn.close()
                return False, "المستخدم غير موجود"
            
            username = user_data[0]
            
            # حذف المستخدم
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
            
            conn.commit()
            conn.close()
            
            return True, f"تم حذف المستخدم {username} بنجاح"
        
        except Exception as e:
            return False, f"خطأ في حذف المستخدم: {str(e)}"
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, username, full_name, role, email, phone, is_active, created_at, last_login
                FROM users WHERE username = ?
            """, (username,))
            
            result = cursor.fetchone()
            if result:
                user_dict = {
                    'id': result[0],
                    'username': result[1],
                    'full_name': result[2],
                    'role': result[3],
                    'email': result[4],
                    'phone': result[5],
                    'is_active': bool(result[6]),
                    'created_at': result[7],
                    'last_login': result[8]
                }
                conn.close()
                return user_dict
            
            conn.close()
            return None
        
        except Exception as e:
            print(f"خطأ في الحصول على المستخدم: {e}")
            return None
