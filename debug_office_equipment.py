#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def debug_office_equipment():
    """فحص ربط المعدات الخارجية بالمكاتب"""
    print("🔍 فحص ربط المعدات الخارجية بالمكاتب")
    print("=" * 60)
    
    try:
        config = get_config()
        conn = sqlite3.connect(config.DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # فحص المكاتب
        cursor.execute('SELECT id, name FROM offices ORDER BY id')
        offices = cursor.fetchall()
        
        print(f"📋 المكاتب الموجودة ({len(offices)}):")
        for office in offices:
            print(f"   {office['id']}. {office['name']}")

        # فحص المعدات الخارجية
        cursor.execute('''
            SELECT id, equipment_name, office_id, supplier_name, status
            FROM external_equipment
            ORDER BY id
        ''')
        external_equipment = cursor.fetchall()
        
        print(f"\n📋 المعدات الخارجية ({len(external_equipment)}):")
        for eq in external_equipment:
            office_name = "غير مربوط"
            if eq['office_id']:
                cursor.execute('SELECT name FROM offices WHERE id = ?', (eq['office_id'],))
                office_row = cursor.fetchone()
                if office_row:
                    office_name = office_row['name']
            
            print(f"   {eq['id']}. {eq['equipment_name']} - مكتب: {office_name} (ID: {eq['office_id']}) - حالة: {eq['status']}")

        # فحص معدات كل مكتب
        print(f"\n📋 معدات كل مكتب:")
        for office in offices:
            cursor.execute('''
                SELECT COUNT(*) as count
                FROM external_equipment
                WHERE office_id = ?
            ''', (office['id'],))
            count = cursor.fetchone()['count']
            
            cursor.execute('''
                SELECT equipment_name, status
                FROM external_equipment
                WHERE office_id = ?
            ''', (office['id'],))
            equipment_list = cursor.fetchall()
            
            print(f"   🏢 {office['name']} (ID: {office['id']}): {count} معدة")
            for eq in equipment_list:
                print(f"      - {eq['equipment_name']} ({eq['status']})")

        # اقتراح ربط المعدة الخارجية بمكتب
        if len(external_equipment) > 0 and len(offices) > 0:
            unlinked_equipment = [eq for eq in external_equipment if not eq['office_id']]
            if unlinked_equipment:
                print(f"\n💡 اقتراح: ربط المعدات غير المربوطة بمكاتب")
                for eq in unlinked_equipment:
                    # البحث عن مكتب بنفس اسم المورد
                    matching_office = None
                    for office in offices:
                        if eq['supplier_name'] and eq['supplier_name'].lower() in office['name'].lower():
                            matching_office = office
                            break
                    
                    if matching_office:
                        print(f"   🔗 ربط '{eq['equipment_name']}' بمكتب '{matching_office['name']}'")
                        cursor.execute('''
                            UPDATE external_equipment 
                            SET office_id = ? 
                            WHERE id = ?
                        ''', (matching_office['id'], eq['id']))
                        conn.commit()
                        print(f"   ✅ تم الربط بنجاح")
                    else:
                        print(f"   ⚠️ لم يتم العثور على مكتب مناسب لـ '{eq['equipment_name']}'")

        conn.close()

    except Exception as e:
        print(f"❌ خطأ في فحص ربط المعدات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_office_equipment()
