{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-users me-2 text-primary"></i>
                        إدارة العملاء
                    </h1>
                    <p class="text-muted mb-0">إدارة وتتبع جميع العملاء</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showAddCustomerModal()">
                        <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                    </button>
                    <button class="btn btn-success ms-2" onclick="refreshCustomers()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">البحث عن عميل:</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="ابحث بالاسم أو رقم الهاتف..." onkeyup="filterCustomers()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-secondary w-100" onclick="clearSearch()">
                                <i class="fas fa-times me-2"></i>مسح البحث
                            </button>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-info w-100" onclick="refreshCustomers()">
                                <i class="fas fa-sync me-2"></i>تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>قائمة العملاء
                        <span class="badge bg-primary ms-2" id="customersCount">{{ customers|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="customersTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم العميل</th>
                                    <th>رقم الهاتف</th>
                                    <th>الرقم القومي</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>عدد الإيجارات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customers %}
                                <tr>
                                    <td>{{ customer.id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="fas fa-user text-white"></i>
                                            </div>
                                            <strong>{{ customer.name }}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone me-1"></i>{{ customer.phone }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if customer.national_id %}
                                        <span class="badge bg-secondary">{{ customer.national_id }}</span>
                                        {% else %}
                                        <small class="text-muted">غير محدد</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ customer.created_date or 'غير محدد' }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ customer.rental_count or 0 }}</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editCustomer({{ customer.id }}, '{{ customer.name }}', '{{ customer.phone }}', '{{ customer.national_id or '' }}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="{{ url_for('customer_details', customer_id=customer.id) }}"
                                           class="btn btn-sm btn-outline-info me-1" title="عرض التفاصيل والإيجارات">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer({{ customer.id }}, '{{ customer.name }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Customer Modal -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل:</label>
                                <input type="text" class="form-control" id="customerName" required placeholder="أدخل اسم العميل الكامل">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف:</label>
                                <input type="tel" class="form-control" id="customerPhone" required placeholder="مثال: 01234567890">
                                <div class="form-text">يرجى إدخال رقم هاتف صحيح</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرقم القومي (اختياري):</label>
                                <input type="text" class="form-control" id="customerNationalId" placeholder="أدخل الرقم القومي">
                                <div class="form-text">14 رقم للمصريين</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ملاحظات (اختياري):</label>
                                <textarea class="form-control" id="customerNotes" rows="2" placeholder="أي ملاحظات إضافية عن العميل"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Photo Upload Section -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الصورة الشخصية (اختياري):</label>
                                <input type="file" class="form-control" id="customerPhoto" accept="image/*">
                                <div class="form-text">JPG, PNG, GIF - أقصى حجم 5MB</div>
                                <div id="photoPreview" class="mt-2" style="display: none;">
                                    <img id="photoPreviewImg" src="" alt="معاينة الصورة" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">صورة بطاقة الهوية (اختياري):</label>
                                <input type="file" class="form-control" id="customerIdPhoto" accept="image/*">
                                <div class="form-text">JPG, PNG, GIF - أقصى حجم 5MB</div>
                                <div id="idPhotoPreview" class="mt-2" style="display: none;">
                                    <img id="idPhotoPreviewImg" src="" alt="معاينة صورة البطاقة" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                    <i class="fas fa-save me-2"></i>حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Customer Modal -->
<div class="modal fade" id="editCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>تعديل بيانات العميل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCustomerForm">
                    <input type="hidden" id="editCustomerId">

                    <!-- معلومات أساسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-warning border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-1"></i>اسم العميل:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="editCustomerName" required
                                   placeholder="أدخل اسم العميل الكامل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-phone me-1"></i>رقم الهاتف:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="tel" class="form-control" id="editCustomerPhone" required
                                   placeholder="مثال: 01234567890">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-id-card me-1"></i>الرقم القومي:
                            </label>
                            <input type="text" class="form-control" id="editCustomerNationalId"
                                   placeholder="أدخل الرقم القومي (14 رقم)">
                            <div class="form-text">14 رقم للمصريين (اختياري)</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-envelope me-1"></i>البريد الإلكتروني:
                            </label>
                            <input type="email" class="form-control" id="editCustomerEmail"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-info border-bottom pb-2 mb-3">
                                <i class="fas fa-sticky-note me-2"></i>معلومات إضافية
                            </h6>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-comment me-1"></i>ملاحظات:
                            </label>
                            <textarea class="form-control" id="editCustomerNotes" rows="3"
                                      placeholder="أي ملاحظات إضافية عن العميل..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="updateCustomer()">
                    <i class="fas fa-save me-1"></i>حفظ التعديلات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Details Modal -->
<div class="modal fade" id="customerDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="customerDetailsContent">
                <!-- سيتم ملء المحتوى ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filter customers
    function filterCustomers() {
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        const rows = document.querySelectorAll('#customersTable tbody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            
            if (!searchInput || text.includes(searchInput)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        document.getElementById('customersCount').textContent = visibleCount;
    }
    
    // Clear search
    function clearSearch() {
        document.getElementById('searchInput').value = '';
        filterCustomers();
    }
    
    // Refresh customers
    function refreshCustomers() {
        location.reload();
    }
    
    // Show add customer modal
    function showAddCustomerModal() {
        const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
        modal.show();
    }
    
    // Save customer
    async function saveCustomer() {
        const name = document.getElementById('customerName').value.trim();
        const phone = document.getElementById('customerPhone').value.trim();
        const nationalId = document.getElementById('customerNationalId').value.trim();
        const notes = document.getElementById('customerNotes').value.trim();
        const photoFile = document.getElementById('customerPhoto').files[0];
        const idPhotoFile = document.getElementById('customerIdPhoto').files[0];

        if (!name || !phone) {
            showAlert('danger', 'يرجى ملء الاسم ورقم الهاتف');
            return;
        }

        // التحقق من صحة رقم الهاتف
        const phoneRegex = /^[0-9+\-\s()]+$/;
        if (!phoneRegex.test(phone)) {
            showAlert('danger', 'يرجى إدخال رقم هاتف صحيح');
            return;
        }

        // التحقق من الرقم القومي إذا تم إدخاله
        if (nationalId && nationalId.length !== 14) {
            showAlert('danger', 'الرقم القومي يجب أن يكون 14 رقم');
            return;
        }

        try {
            // إنشاء العميل أولاً
            const customerData = {
                name: name,
                phone: phone,
                national_id: nationalId || null,
                notes: notes
            };

            const response = await fetch('/api/customers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(customerData)
            });

            const result = await response.json();

            if (!result.success) {
                showAlert('danger', result.error);
                return;
            }

            // رفع الصور إذا تم اختيارها
            const customerId = result.customer_id; // نحتاج لإضافة هذا في الاستجابة

            if (photoFile) {
                await uploadCustomerPhoto(customerId, photoFile, 'profile');
            }

            if (idPhotoFile) {
                await uploadCustomerPhoto(customerId, idPhotoFile, 'id');
            }

            showAlert('success', 'تم إضافة العميل بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();

            // مسح النموذج
            document.getElementById('addCustomerForm').reset();
            clearPhotoPreview();

            // تحديث الصفحة
            setTimeout(() => {
                location.reload();
            }, 1000);

        } catch (error) {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في إضافة العميل');
        }
    }

    // Upload customer photo
    async function uploadCustomerPhoto(customerId, file, photoType) {
        const formData = new FormData();
        formData.append('photo', file);
        formData.append('customer_id', customerId);
        formData.append('photo_type', photoType);

        const response = await fetch('/api/upload/customer_photo', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (!result.success) {
            console.error('Photo upload failed:', result.error);
        }

        return result;
    }
    
    // Clear photo preview
    function clearPhotoPreview() {
        document.getElementById('photoPreview').style.display = 'none';
        document.getElementById('idPhotoPreview').style.display = 'none';
    }

    // Preview photo
    function previewPhoto(input, previewId, imgId) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById(imgId).src = e.target.result;
                document.getElementById(previewId).style.display = 'block';
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // Edit customer
    function editCustomer(id, name, phone, nationalId = '') {
        // ملء النموذج بالبيانات الحالية
        document.getElementById('editCustomerId').value = id;
        document.getElementById('editCustomerName').value = name;
        document.getElementById('editCustomerPhone').value = phone;
        document.getElementById('editCustomerNationalId').value = nationalId || '';
        document.getElementById('editCustomerEmail').value = '';
        document.getElementById('editCustomerNotes').value = '';

        // إظهار النموذج
        const modal = new bootstrap.Modal(document.getElementById('editCustomerModal'));
        modal.show();
    }

    // Update customer
    function updateCustomer() {
        const id = document.getElementById('editCustomerId').value;
        const name = document.getElementById('editCustomerName').value.trim();
        const phone = document.getElementById('editCustomerPhone').value.trim();
        const nationalId = document.getElementById('editCustomerNationalId').value.trim();
        const email = document.getElementById('editCustomerEmail').value.trim();
        const notes = document.getElementById('editCustomerNotes').value.trim();

        if (!name || !phone) {
            showAlert('danger', 'يرجى ملء الاسم ورقم الهاتف على الأقل');
            return;
        }

        // التحقق من صحة رقم الهاتف
        const phoneRegex = /^[0-9+\-\s()]+$/;
        if (!phoneRegex.test(phone)) {
            showAlert('danger', 'يرجى إدخال رقم هاتف صحيح');
            return;
        }

        // التحقق من الرقم القومي إذا تم إدخاله
        if (nationalId && nationalId.length !== 14) {
            showAlert('danger', 'الرقم القومي يجب أن يكون 14 رقم');
            return;
        }

        // التحقق من البريد الإلكتروني إذا تم إدخاله
        if (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showAlert('danger', 'يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
        }

        const data = {
            name: name,
            phone: phone,
            national_id: nationalId || null,
            email: email || null,
            notes: notes || null
        };

        fetch(`/api/customers/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('editCustomerModal')).hide();
                location.reload();
            } else {
                showAlert('danger', data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في تعديل العميل');
        });
    }
    
    // View customer rentals
    function viewCustomerRentals(id) {
        // إظهار نافذة تفاصيل العميل
        const modal = new bootstrap.Modal(document.getElementById('customerDetailsModal'));
        
        // محتوى مؤقت
        document.getElementById('customerDetailsContent').innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p>جاري تحميل تفاصيل العميل...</p>
            </div>
        `;
        
        modal.show();
        
        // محاكاة تحميل البيانات
        setTimeout(() => {
            document.getElementById('customerDetailsContent').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    ميزة عرض تفاصيل العميل وإيجاراته قيد التطوير
                </div>
                <p>سيتم عرض:</p>
                <ul>
                    <li>تفاصيل العميل الكاملة</li>
                    <li>تاريخ جميع الإيجارات</li>
                    <li>الإيجارات النشطة</li>
                    <li>إحصائيات العميل</li>
                </ul>
            `;
        }, 1000);
    }
    
    // Delete customer
    function deleteCustomer(id, name) {
        if (confirm(`هل أنت متأكد من حذف العميل "${name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!\n\nملاحظة: لا يمكن حذف العميل إذا كان لديه إيجارات نشطة.`)) {
            fetch(`/api/customers/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    location.reload();
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ في حذف العميل');
            });
        }
    }

    // Refresh customers
    function refreshCustomers() {
        location.reload();
    }
    
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // تركيز تلقائي على حقل البحث
        document.getElementById('searchInput').focus();

        // إضافة مؤثرات بصرية للجدول
        const rows = document.querySelectorAll('#customersTable tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f8f9fa';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });

        // إضافة معاينة الصور
        document.getElementById('customerPhoto').addEventListener('change', function() {
            previewPhoto(this, 'photoPreview', 'photoPreviewImg');
        });

        document.getElementById('customerIdPhoto').addEventListener('change', function() {
            previewPhoto(this, 'idPhotoPreview', 'idPhotoPreviewImg');
        });
    });
</script>

<style>
    .avatar-sm {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .table tbody tr {
        transition: background-color 0.2s ease;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa !important;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
</style>
{% endblock %}
