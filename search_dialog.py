#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة البحث المتقدم
Advanced Search Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import font
from typing import Dict, List, Optional
import threading
from datetime import date

from advanced_search import AdvancedSearchEngine, SearchScope, SearchFilter, SortOrder

class AdvancedSearchDialog:
    """نافذة البحث المتقدم"""
    
    def __init__(self, parent):
        """تهيئة نافذة البحث المتقدم"""
        self.parent = parent
        self.search_engine = AdvancedSearchEngine()
        self.current_results = {}
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("البحث المتقدم")
        self.dialog.geometry("1200x800")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{parent.winfo_rootx() + 50}+{parent.winfo_rooty() + 50}")
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
        
        # قائمة الفلاتر الحالية
        self.current_filters = []
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحديث الإحصائيات
        self.update_statistics()
    
    def create_interface(self):
        """إنشاء واجهة البحث المتقدم"""
        # إنشاء إطار رئيسي مع تقسيم
        main_paned = ttk.PanedWindow(self.dialog, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # الجانب الأيسر: خيارات البحث
        self.create_search_panel(main_paned)
        
        # الجانب الأيمن: النتائج
        self.create_results_panel(main_paned)
    
    def create_search_panel(self, parent):
        """إنشاء لوحة البحث"""
        search_frame = ttk.Frame(parent)
        parent.add(search_frame, weight=1)
        
        # البحث السريع
        quick_search_frame = ttk.LabelFrame(search_frame, text="البحث السريع")
        quick_search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # حقل البحث
        search_entry_frame = ttk.Frame(quick_search_frame)
        search_entry_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(search_entry_frame, text="البحث:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_entry_frame, textvariable=self.search_var,
                                     font=self.arabic_font)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        
        # ربط Enter للبحث
        self.search_entry.bind('<Return>', lambda e: self.perform_search())
        
        # نطاق البحث
        scope_frame = ttk.Frame(quick_search_frame)
        scope_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(scope_frame, text="البحث في:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.scope_var = tk.StringVar(value="all")
        scope_options = [
            ("الكل", "all"),
            ("المعدات", "equipment"),
            ("العملاء", "customers"),
            ("الإيجارات", "rentals")
        ]
        
        for text, value in scope_options:
            ttk.Radiobutton(scope_frame, text=text, variable=self.scope_var,
                           value=value, font=self.arabic_font).pack(side=tk.LEFT, padx=5)
        
        # أزرار البحث
        search_buttons_frame = ttk.Frame(quick_search_frame)
        search_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(search_buttons_frame, text="بحث", 
                  command=self.perform_search).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(search_buttons_frame, text="مسح", 
                  command=self.clear_search).pack(side=tk.LEFT)
        
        # الفلاتر المتقدمة
        filters_frame = ttk.LabelFrame(search_frame, text="الفلاتر المتقدمة")
        filters_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إضافة فلتر جديد
        add_filter_frame = ttk.Frame(filters_frame)
        add_filter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(add_filter_frame, text="إضافة فلتر:", 
                 font=self.arabic_font_bold).pack(anchor=tk.W)
        
        # اختيار الجدول
        table_frame = ttk.Frame(add_filter_frame)
        table_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(table_frame, text="الجدول:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.filter_table_var = tk.StringVar()
        self.filter_table_combo = ttk.Combobox(table_frame, textvariable=self.filter_table_var,
                                              state="readonly", width=15)
        self.filter_table_combo['values'] = ["equipment", "customers", "rentals"]
        self.filter_table_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.filter_table_combo.bind('<<ComboboxSelected>>', self.on_table_change)
        
        # اختيار الحقل
        field_frame = ttk.Frame(add_filter_frame)
        field_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(field_frame, text="الحقل:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.filter_field_var = tk.StringVar()
        self.filter_field_combo = ttk.Combobox(field_frame, textvariable=self.filter_field_var,
                                              state="readonly", width=15)
        self.filter_field_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.filter_field_combo.bind('<<ComboboxSelected>>', self.on_field_change)
        
        # اختيار المشغل
        operator_frame = ttk.Frame(add_filter_frame)
        operator_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(operator_frame, text="المشغل:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.filter_operator_var = tk.StringVar()
        self.filter_operator_combo = ttk.Combobox(operator_frame, textvariable=self.filter_operator_var,
                                                 state="readonly", width=15)
        self.filter_operator_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # إدخال القيمة
        value_frame = ttk.Frame(add_filter_frame)
        value_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(value_frame, text="القيمة:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.filter_value_var = tk.StringVar()
        self.filter_value_entry = ttk.Entry(value_frame, textvariable=self.filter_value_var,
                                           font=self.arabic_font, width=15)
        self.filter_value_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # زر إضافة الفلتر
        ttk.Button(add_filter_frame, text="إضافة فلتر", 
                  command=self.add_filter).pack(pady=5)
        
        # قائمة الفلاتر الحالية
        current_filters_frame = ttk.Frame(filters_frame)
        current_filters_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        ttk.Label(current_filters_frame, text="الفلاتر الحالية:", 
                 font=self.arabic_font_bold).pack(anchor=tk.W)
        
        # قائمة الفلاتر
        self.filters_listbox = tk.Listbox(current_filters_frame, font=self.arabic_font, height=6)
        filters_scrollbar = ttk.Scrollbar(current_filters_frame, orient=tk.VERTICAL,
                                         command=self.filters_listbox.yview)
        self.filters_listbox.configure(yscrollcommand=filters_scrollbar.set)
        
        self.filters_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        filters_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار إدارة الفلاتر
        filter_buttons_frame = ttk.Frame(filters_frame)
        filter_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(filter_buttons_frame, text="حذف فلتر", 
                  command=self.remove_filter).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_buttons_frame, text="مسح الكل", 
                  command=self.clear_filters).pack(side=tk.LEFT)
        
        # الإحصائيات
        stats_frame = ttk.LabelFrame(search_frame, text="الإحصائيات")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD,
                                 font=self.arabic_font, state=tk.DISABLED)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL,
                                       command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_results_panel(self, parent):
        """إنشاء لوحة النتائج"""
        results_frame = ttk.Frame(parent)
        parent.add(results_frame, weight=2)
        
        # شريط أدوات النتائج
        results_toolbar = ttk.Frame(results_frame)
        results_toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(results_toolbar, text="النتائج:", 
                 font=self.arabic_font_bold).pack(side=tk.LEFT)
        
        self.results_count_label = ttk.Label(results_toolbar, text="0 نتيجة", 
                                           font=self.arabic_font)
        self.results_count_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # أزرار التصدير
        ttk.Button(results_toolbar, text="تصدير CSV", 
                  command=self.export_csv).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(results_toolbar, text="تصدير JSON", 
                  command=self.export_json).pack(side=tk.RIGHT)
        
        # دفتر تبويبات النتائج
        self.results_notebook = ttk.Notebook(results_frame)
        self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # تبويب المعدات
        self.create_equipment_results_tab()
        
        # تبويب العملاء
        self.create_customers_results_tab()
        
        # تبويب الإيجارات
        self.create_rentals_results_tab()
    
    def create_equipment_results_tab(self):
        """إنشاء تبويب نتائج المعدات"""
        equipment_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(equipment_frame, text="المعدات")
        
        # جدول المعدات
        equipment_columns = ("id", "name", "model", "category", "serial_number", "status")
        self.equipment_tree = ttk.Treeview(equipment_frame, columns=equipment_columns, show="headings")
        
        # تكوين الأعمدة
        column_headers = {
            "id": "المعرف",
            "name": "اسم المعدة",
            "model": "الموديل",
            "category": "النوع",
            "serial_number": "الرقم التسلسلي",
            "status": "الحالة"
        }
        
        for col in equipment_columns:
            self.equipment_tree.heading(col, text=column_headers[col])
            self.equipment_tree.column(col, width=120)
        
        # شريط التمرير
        equipment_scrollbar = ttk.Scrollbar(equipment_frame, orient=tk.VERTICAL,
                                           command=self.equipment_tree.yview)
        self.equipment_tree.configure(yscrollcommand=equipment_scrollbar.set)
        
        self.equipment_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        equipment_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_customers_results_tab(self):
        """إنشاء تبويب نتائج العملاء"""
        customers_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(customers_frame, text="العملاء")
        
        # جدول العملاء
        customer_columns = ("id", "name", "phone", "email", "created_date")
        self.customers_tree = ttk.Treeview(customers_frame, columns=customer_columns, show="headings")
        
        # تكوين الأعمدة
        column_headers = {
            "id": "المعرف",
            "name": "اسم العميل",
            "phone": "رقم الهاتف",
            "email": "البريد الإلكتروني",
            "created_date": "تاريخ التسجيل"
        }
        
        for col in customer_columns:
            self.customers_tree.heading(col, text=column_headers[col])
            self.customers_tree.column(col, width=150)
        
        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(customers_frame, orient=tk.VERTICAL,
                                           command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)
        
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        customers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_rentals_results_tab(self):
        """إنشاء تبويب نتائج الإيجارات"""
        rentals_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(rentals_frame, text="الإيجارات")
        
        # جدول الإيجارات
        rental_columns = ("id", "customer_name", "equipment_name", "rental_date", 
                         "return_date", "price", "status")
        self.rentals_tree = ttk.Treeview(rentals_frame, columns=rental_columns, show="headings")
        
        # تكوين الأعمدة
        column_headers = {
            "id": "رقم الإيجار",
            "customer_name": "العميل",
            "equipment_name": "المعدة",
            "rental_date": "تاريخ الإيجار",
            "return_date": "تاريخ الإرجاع",
            "price": "السعر",
            "status": "الحالة"
        }
        
        for col in rental_columns:
            self.rentals_tree.heading(col, text=column_headers[col])
            self.rentals_tree.column(col, width=120)
        
        # شريط التمرير
        rentals_scrollbar = ttk.Scrollbar(rentals_frame, orient=tk.VERTICAL,
                                         command=self.rentals_tree.yview)
        self.rentals_tree.configure(yscrollcommand=rentals_scrollbar.set)
        
        self.rentals_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rentals_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def on_table_change(self, event=None):
        """معالج تغيير الجدول"""
        table = self.filter_table_var.get()
        if table:
            # تحديث قائمة الحقول
            fields = self.search_engine.get_searchable_fields(table)
            field_names = list(fields.keys())
            self.filter_field_combo['values'] = field_names

            # مسح الاختيارات السابقة
            self.filter_field_var.set("")
            self.filter_operator_var.set("")
            self.filter_value_var.set("")

    def on_field_change(self, event=None):
        """معالج تغيير الحقل"""
        table = self.filter_table_var.get()
        field = self.filter_field_var.get()

        if table and field:
            # الحصول على نوع الحقل
            fields = self.search_engine.get_searchable_fields(table)
            field_type = fields.get(field, {}).get('type', 'text')

            # تحديث قائمة المشغلات
            operators = self.search_engine.get_operators_for_field_type(field_type)
            operator_values = [op[1] for op in operators]  # النصوص المعروضة
            self.filter_operator_combo['values'] = operator_values

            # حفظ تطابق المشغلات
            self.operator_mapping = {op[1]: op[0] for op in operators}

            # مسح الاختيارات السابقة
            self.filter_operator_var.set("")
            self.filter_value_var.set("")

    def add_filter(self):
        """إضافة فلتر جديد"""
        table = self.filter_table_var.get()
        field = self.filter_field_var.get()
        operator_display = self.filter_operator_var.get()
        value = self.filter_value_var.get().strip()

        if not all([table, field, operator_display, value]):
            messagebox.showwarning("تحذير", "يرجى ملء جميع حقول الفلتر")
            return

        # تحويل المشغل المعروض إلى المشغل الفعلي
        operator = self.operator_mapping.get(operator_display, operator_display)

        # إنشاء الفلتر
        search_filter = SearchFilter(field, operator, value, table)
        self.current_filters.append(search_filter)

        # تحديث قائمة الفلاتر
        self.update_filters_display()

        # مسح الحقول
        self.filter_table_var.set("")
        self.filter_field_var.set("")
        self.filter_operator_var.set("")
        self.filter_value_var.set("")

        # تحديث قوائم الحقول والمشغلات
        self.filter_field_combo['values'] = []
        self.filter_operator_combo['values'] = []

    def remove_filter(self):
        """حذف فلتر محدد"""
        selection = self.filters_listbox.curselection()
        if selection:
            index = selection[0]
            del self.current_filters[index]
            self.update_filters_display()

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.current_filters.clear()
        self.update_filters_display()

    def update_filters_display(self):
        """تحديث عرض الفلاتر"""
        self.filters_listbox.delete(0, tk.END)

        for filter_obj in self.current_filters:
            # تنسيق عرض الفلتر
            display_text = f"{filter_obj.table}.{filter_obj.field} {filter_obj.operator} {filter_obj.value}"
            self.filters_listbox.insert(tk.END, display_text)

    def perform_search(self):
        """تنفيذ البحث"""
        query = self.search_var.get().strip()
        scope_value = self.scope_var.get()

        # تحويل نطاق البحث
        scope_mapping = {
            "all": SearchScope.ALL,
            "equipment": SearchScope.EQUIPMENT,
            "customers": SearchScope.CUSTOMERS,
            "rentals": SearchScope.RENTALS
        }
        scope = scope_mapping.get(scope_value, SearchScope.ALL)

        # تنفيذ البحث في خيط منفصل
        def search_thread():
            try:
                results = self.search_engine.search(
                    query=query,
                    scope=scope,
                    filters=self.current_filters,
                    limit=500
                )

                # تحديث الواجهة في الخيط الرئيسي
                self.dialog.after(0, lambda: self.display_results(results))

            except Exception as e:
                error_msg = f"خطأ في البحث: {str(e)}"
                self.dialog.after(0, lambda: messagebox.showerror("خطأ", error_msg))

        threading.Thread(target=search_thread, daemon=True).start()

    def display_results(self, results: Dict):
        """عرض نتائج البحث"""
        self.current_results = results

        # تحديث عداد النتائج
        total_count = results.get('total_count', 0)
        self.results_count_label.config(text=f"{total_count} نتيجة")

        # عرض نتائج المعدات
        self.display_equipment_results(results.get('equipment', []))

        # عرض نتائج العملاء
        self.display_customer_results(results.get('customers', []))

        # عرض نتائج الإيجارات
        self.display_rental_results(results.get('rentals', []))

    def display_equipment_results(self, equipment_list: List[Dict]):
        """عرض نتائج المعدات"""
        # مسح النتائج السابقة
        for item in self.equipment_tree.get_children():
            self.equipment_tree.delete(item)

        # إضافة النتائج الجديدة
        for equipment in equipment_list:
            values = (
                equipment.get('id', ''),
                equipment.get('name', ''),
                equipment.get('model', ''),
                equipment.get('category', ''),
                equipment.get('serial_number', ''),
                equipment.get('status', '')
            )
            self.equipment_tree.insert("", tk.END, values=values)

    def display_customer_results(self, customers_list: List[Dict]):
        """عرض نتائج العملاء"""
        # مسح النتائج السابقة
        for item in self.customers_tree.get_children():
            self.customers_tree.delete(item)

        # إضافة النتائج الجديدة
        for customer in customers_list:
            values = (
                customer.get('id', ''),
                customer.get('name', ''),
                customer.get('phone', ''),
                customer.get('email', ''),
                customer.get('created_date', '')
            )
            self.customers_tree.insert("", tk.END, values=values)

    def display_rental_results(self, rentals_list: List[Dict]):
        """عرض نتائج الإيجارات"""
        # مسح النتائج السابقة
        for item in self.rentals_tree.get_children():
            self.rentals_tree.delete(item)

        # إضافة النتائج الجديدة
        for rental in rentals_list:
            values = (
                rental.get('id', ''),
                rental.get('customer_name', ''),
                rental.get('equipment_name', ''),
                rental.get('rental_date', ''),
                rental.get('return_date', ''),
                rental.get('price', ''),
                rental.get('status', '')
            )
            self.rentals_tree.insert("", tk.END, values=values)

    def clear_search(self):
        """مسح البحث"""
        self.search_var.set("")
        self.scope_var.set("all")
        self.clear_filters()

        # مسح النتائج
        self.current_results = {}
        self.display_results({})

    def update_statistics(self):
        """تحديث الإحصائيات"""
        def stats_thread():
            try:
                stats = self.search_engine.get_search_statistics()
                stats_text = self.format_statistics(stats)

                # تحديث الواجهة في الخيط الرئيسي
                self.dialog.after(0, lambda: self.display_statistics(stats_text))

            except Exception as e:
                error_msg = f"خطأ في تحديث الإحصائيات: {str(e)}"
                self.dialog.after(0, lambda: print(error_msg))

        threading.Thread(target=stats_thread, daemon=True).start()

    def format_statistics(self, stats: Dict) -> str:
        """تنسيق الإحصائيات للعرض"""
        if not stats:
            return "لا توجد إحصائيات متاحة"

        text = "📊 إحصائيات النظام:\n\n"

        text += "🔧 المعدات:\n"
        text += f"  • الإجمالي: {stats.get('total_equipment', 0)}\n"
        text += f"  • المتاحة: {stats.get('available_equipment', 0)}\n"
        text += f"  • المؤجرة: {stats.get('rented_equipment', 0)}\n\n"

        text += "👥 العملاء:\n"
        text += f"  • الإجمالي: {stats.get('total_customers', 0)}\n\n"

        text += "📋 الإيجارات:\n"
        text += f"  • الإجمالي: {stats.get('total_rentals', 0)}\n"
        text += f"  • النشطة: {stats.get('active_rentals', 0)}\n"
        text += f"  • المكتملة: {stats.get('returned_rentals', 0)}\n"
        text += f"  • المتأخرة: {stats.get('overdue_rentals', 0)}\n"

        return text

    def display_statistics(self, stats_text: str):
        """عرض الإحصائيات"""
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_text)
        self.stats_text.config(state=tk.DISABLED)

    def export_csv(self):
        """تصدير النتائج إلى CSV"""
        if not self.current_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ النتائج كـ CSV",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if file_path:
                csv_data = self.search_engine.export_search_results(self.current_results, "csv")
                with open(file_path, 'w', encoding='utf-8-sig') as f:
                    f.write(csv_data)

                messagebox.showinfo("نجح", f"تم تصدير النتائج إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النتائج: {str(e)}")

    def export_json(self):
        """تصدير النتائج إلى JSON"""
        if not self.current_results:
            messagebox.showwarning("تحذير", "لا توجد نتائج للتصدير")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ النتائج كـ JSON",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                json_data = self.search_engine.export_search_results(self.current_results, "json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(json_data)

                messagebox.showinfo("نجح", f"تم تصدير النتائج إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير النتائج: {str(e)}")

    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()

class QuickSearchWidget:
    """عنصر البحث السريع للتضمين في التطبيق الرئيسي"""

    def __init__(self, parent, on_search_callback=None):
        """تهيئة عنصر البحث السريع"""
        self.parent = parent
        self.on_search_callback = on_search_callback
        self.search_engine = AdvancedSearchEngine()

        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)

        # إنشاء الواجهة
        self.create_widget()

    def create_widget(self):
        """إنشاء عنصر البحث السريع"""
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.parent)

        # حقل البحث
        search_frame = ttk.Frame(self.main_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(search_frame, text="🔍", font=self.arabic_font).pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var,
                                     font=self.arabic_font, width=30)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # ربط Enter للبحث
        self.search_entry.bind('<Return>', lambda e: self.perform_quick_search())
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        # زر البحث المتقدم
        ttk.Button(search_frame, text="بحث متقدم",
                  command=self.show_advanced_search).pack(side=tk.RIGHT, padx=(5, 0))

    def on_search_change(self, event=None):
        """معالج تغيير نص البحث"""
        # يمكن إضافة البحث التلقائي هنا
        pass

    def perform_quick_search(self):
        """تنفيذ البحث السريع"""
        query = self.search_var.get().strip()
        if query and self.on_search_callback:
            results = self.search_engine.quick_search(query)
            self.on_search_callback(results)

    def show_advanced_search(self):
        """عرض نافذة البحث المتقدم"""
        dialog = AdvancedSearchDialog(self.parent)
        dialog.show()

    def pack(self, **kwargs):
        """تطبيق pack على الإطار الرئيسي"""
        self.main_frame.pack(**kwargs)

    def grid(self, **kwargs):
        """تطبيق grid على الإطار الرئيسي"""
        self.main_frame.grid(**kwargs)
