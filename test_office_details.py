#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_office_details():
    """اختبار صفحة تفاصيل المكتب"""
    base_url = "http://127.0.0.1:5000"
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة تفاصيل المكتب 3 (معدات الضوء الذهبي)
        office_id = 3
        print(f"🔍 اختبار صفحة تفاصيل المكتب {office_id}...")
        response = session.get(f"{base_url}/offices/{office_id}")
        
        if response.status_code == 200:
            content = response.text
            print("✅ صفحة تفاصيل المكتب تعمل")
            print(f"📊 حجم المحتوى: {len(content)} حرف")
            
            # فحص محتوى الصفحة
            if 'معدات الضوء الذهبي' in content:
                print("✅ يحتوي على اسم المكتب")
            else:
                print("❌ لا يحتوي على اسم المكتب")
            
            if 'معدات المكتب' in content or 'المعدات المستقطبة' in content:
                print("✅ يحتوي على قسم المعدات")
            else:
                print("❌ لا يحتوي على قسم المعدات")
            
            if 'Sony A7R V' in content:
                print("✅ يحتوي على المعدة الخارجية")
            else:
                print("❌ لا يحتوي على المعدة الخارجية")
            
            # عد الصفوف في جدول المعدات
            equipment_rows = content.count('<tr data-equipment=')
            print(f"📊 عدد صفوف المعدات: {equipment_rows}")
            
        else:
            print(f"❌ خطأ في صفحة تفاصيل المكتب: {response.status_code}")
        
        # اختبار API معدات المكتب
        print(f"\n🔍 اختبار API معدات المكتب {office_id}...")
        response = session.get(f"{base_url}/api/offices/{office_id}/equipment")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API معدات المكتب يعمل")
            print(f"📊 عدد المعدات: {data.get('count', 0)}")
            
            if 'equipment' in data and len(data['equipment']) > 0:
                print("✅ يحتوي على بيانات المعدات")
                for i, eq in enumerate(data['equipment']):
                    print(f"   {i+1}. {eq['equipment_name']} - {eq['status']}")
            else:
                print("❌ لا يحتوي على بيانات المعدات")
        else:
            print(f"❌ خطأ في API معدات المكتب: {response.status_code}")
            
    else:
        print("❌ فشل تسجيل الدخول")

if __name__ == "__main__":
    test_office_details()
