#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def add_office_id_column():
    """إضافة عمود office_id إلى جدول external_equipment"""
    config = get_config()
    conn = sqlite3.connect(config.DATABASE_PATH)
    cursor = conn.cursor()

    print('إضافة عمود office_id إلى جدول external_equipment...')
    try:
        cursor.execute('ALTER TABLE external_equipment ADD COLUMN office_id INTEGER')
        print('✅ تم إضافة العمود بنجاح')
        
        # ربط المعدة الموجودة بمكتب (إذا وجد)
        cursor.execute('SELECT id FROM offices WHERE name LIKE "%الضوء%" LIMIT 1')
        office = cursor.fetchone()
        if office:
            cursor.execute('UPDATE external_equipment SET office_id = ? WHERE supplier_name LIKE "%الضوء%"', (office[0],))
            print(f'✅ تم ربط المعدة بالمكتب {office[0]}')
        
        conn.commit()
        print('✅ تم حفظ التغييرات')
        
    except Exception as e:
        if 'duplicate column name' in str(e):
            print('⚠️ العمود موجود بالفعل')
        else:
            print(f'❌ خطأ: {e}')

    conn.close()

if __name__ == '__main__':
    add_office_id_column()
