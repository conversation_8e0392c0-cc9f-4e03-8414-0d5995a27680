# -*- coding: utf-8 -*-
"""
مولد الفواتير PDF
Invoice Generator for Equipment Rental System
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
import io
from database import DatabaseManager

class InvoiceGenerator:
    """مولد الفواتير PDF"""
    
    def __init__(self):
        """تهيئة مولد الفواتير"""
        self.db = DatabaseManager()
        self.setup_fonts()
        self.company_info = {
            'name': 'شركة تأجير المعدات',
            'address': 'العنوان: شارع الرئيسي، المدينة، الدولة',
            'phone': 'الهاتف: +20 ************',
            'email': 'البريد الإلكتروني: <EMAIL>',
            'website': 'الموقع الإلكتروني: www.equipment-rental.com'
        }
        self.logo_path = self.get_company_logo_path()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية مع دعم أفضل للنص العربي"""
        try:
            # محاولة تحميل خط عربي من مجلد الخطوط
            font_paths = [
                os.path.join(os.path.dirname(__file__), '..', 'static', 'fonts', 'Cairo-Regular.ttf'),
                os.path.join(os.path.dirname(__file__), '..', 'static', 'fonts', 'Amiri-Regular.ttf'),
                os.path.join(os.path.dirname(__file__), '..', 'static', 'fonts', 'NotoSansArabic-Regular.ttf')
            ]

            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    self.arabic_font = 'Arabic'
                    print(f"تم تحميل الخط العربي: {font_path}")
                    return

            # تحميل خطوط عربية من النظام (Windows)
            try:
                system_fonts = [
                    ('C:/Windows/Fonts/tahoma.ttf', 'Tahoma'),
                    ('C:/Windows/Fonts/arial.ttf', 'Arial'),
                    ('C:/Windows/Fonts/calibri.ttf', 'Calibri'),
                    ('C:/Windows/Fonts/segoeui.ttf', 'Segoe UI'),
                    # خطوط عربية متخصصة
                    ('C:/Windows/Fonts/arabtype.ttf', 'Arabic Typesetting'),
                    ('C:/Windows/Fonts/trado.ttf', 'Traditional Arabic'),
                    ('C:/Windows/Fonts/simpo.ttf', 'Simplified Arabic')
                ]

                for font_path, font_name in system_fonts:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont('SystemArabic', font_path))
                            self.arabic_font = 'SystemArabic'
                            print(f"تم تحميل خط النظام: {font_name} ({font_path})")
                            return
                        except Exception as font_e:
                            print(f"فشل تحميل الخط {font_name}: {font_e}")
                            continue

            except Exception as sys_e:
                print(f"خطأ في البحث عن خطوط النظام: {sys_e}")

            # استخدام خط افتراضي إذا لم يكن الخط العربي متوفراً
            self.arabic_font = 'Helvetica'
            print("تحذير: لم يتم العثور على خط عربي مناسب، سيتم استخدام Helvetica")

        except Exception as e:
            print(f"تحذير: خطأ عام في إعداد الخطوط: {e}")
            self.arabic_font = 'Helvetica'

    def format_arabic_text(self, text):
        """تنسيق النص العربي لضمان العرض الصحيح مع دعم الترتيب الثنائي الاتجاه"""
        if not text:
            return ""

        try:
            # تنظيف النص من الأحرف الخاصة
            text = str(text).strip()

            # محاولة استخدام مكتبة python-bidi للترتيب الصحيح
            try:
                from bidi.algorithm import get_display
                from arabic_reshaper import reshape

                # إعادة تشكيل النص العربي
                reshaped_text = reshape(text)

                # تطبيق خوارزمية الترتيب الثنائي الاتجاه
                bidi_text = get_display(reshaped_text)

                return bidi_text

            except ImportError:
                print("تحذير: مكتبات python-bidi أو arabic-reshaper غير مثبتة")
                # استخدام الطريقة البديلة
                return self._fallback_arabic_format(text)

        except Exception as e:
            print(f"خطأ في تنسيق النص العربي: {e}")
            return self._fallback_arabic_format(text)

    def _fallback_arabic_format(self, text):
        """طريقة بديلة لتنسيق النص العربي"""
        try:
            # تنظيف النص وإضافة مسافات صحيحة
            words = text.split()
            formatted_words = []

            for word in words:
                # التأكد من أن كل كلمة تحتوي على أحرف صالحة
                if word and len(word.strip()) > 0:
                    formatted_words.append(word.strip())

            return " ".join(formatted_words)

        except Exception as e:
            print(f"خطأ في الطريقة البديلة لتنسيق النص: {e}")
            return str(text) if text else ""

    def format_currency(self, amount):
        """تنسيق المبالغ المالية"""
        try:
            if isinstance(amount, (int, float)):
                formatted_amount = f"{amount:,.2f}"
                return self.format_arabic_text(f"{formatted_amount} ريال سعودي")
            return self.format_arabic_text(str(amount))
        except Exception as e:
            print(f"خطأ في تنسيق المبلغ: {e}")
            return str(amount)

    def format_date(self, date_str):
        """تنسيق التواريخ"""
        try:
            from datetime import datetime
            if isinstance(date_str, str):
                # محاولة تحويل التاريخ لتنسيق أفضل
                try:
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                    formatted_date = date_obj.strftime('%d/%m/%Y')
                    return formatted_date
                except:
                    return date_str
            return str(date_str)
        except Exception as e:
            print(f"خطأ في تنسيق التاريخ: {e}")
            return str(date_str)

    def get_company_logo_path(self):
        """الحصول على مسار لوجو الشركة"""
        possible_paths = [
            os.path.join(os.path.dirname(__file__), '..', 'static', 'images', 'logo.png'),
            os.path.join(os.path.dirname(__file__), '..', 'static', 'images', 'logo.jpg'),
            os.path.join(os.path.dirname(__file__), '..', 'static', 'uploads', 'logo.png'),
            os.path.join(os.path.dirname(__file__), '..', 'static', 'uploads', 'logo.jpg')
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path
        return None
    
    def create_styles(self):
        """إنشاء أنماط النص"""
        styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        styles.add(ParagraphStyle(
            name='ArabicTitle',
            parent=styles['Title'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        # نمط النص العربي العادي
        styles.add(ParagraphStyle(
            name='ArabicNormal',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_RIGHT,
            spaceAfter=6
        ))
        
        # نمط النص العربي المحاذي للوسط
        styles.add(ParagraphStyle(
            name='ArabicCenter',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=6
        ))
        
        # نمط العناوين الفرعية
        styles.add(ParagraphStyle(
            name='ArabicHeading',
            parent=styles['Heading2'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_RIGHT,
            spaceAfter=10,
            textColor=colors.darkblue
        ))
        
        return styles
    
    def generate_invoice_number(self, rental_id, rental_type='individual'):
        """إنشاء رقم فاتورة فريد"""
        # الحصول على رقم الفاتورة التسلسلي
        invoice_number = self.db.get_next_sequence_number('invoice')
        if invoice_number:
            # حفظ رقم الفاتورة في قاعدة البيانات
            self.save_invoice_number_to_rental(rental_id, rental_type, invoice_number)
            return invoice_number
        else:
            # في حالة فشل الحصول على رقم تسلسلي، استخدم الطريقة القديمة
            timestamp = datetime.now().strftime("%Y%m%d")
            if rental_type == 'multi':
                return f"INV-MR{rental_id}-{timestamp}"
            else:
                return f"INV-{rental_id}-{timestamp}"

    def save_invoice_number_to_rental(self, rental_id, rental_type, invoice_number):
        """حفظ رقم الفاتورة في جدول الإيجار"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            if rental_type == 'multi':
                cursor.execute('''
                    UPDATE rental_groups
                    SET invoice_number = ?
                    WHERE id = ?
                ''', (invoice_number, rental_id))
            else:
                cursor.execute('''
                    UPDATE rentals
                    SET invoice_number = ?
                    WHERE id = ?
                ''', (invoice_number, rental_id))

            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في حفظ رقم الفاتورة: {e}")
    
    def create_individual_rental_invoice(self, rental_data, customer_data, equipment_data, output_path=None):
        """إنشاء فاتورة للإيجار الفردي"""
        try:
            # إعداد المستند
            if output_path is None:
                buffer = io.BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm, 
                                      topMargin=2*cm, bottomMargin=2*cm)
            else:
                doc = SimpleDocTemplate(output_path, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm, 
                                      topMargin=2*cm, bottomMargin=2*cm)
            
            # إنشاء الأنماط
            styles = self.create_styles()
            story = []
            
            # رقم الفاتورة
            invoice_number = self.generate_invoice_number(rental_data['id'], 'individual')
            
            # إضافة اللوجو إذا كان متوفراً
            if self.logo_path:
                try:
                    from reportlab.platypus import Image
                    logo = Image(self.logo_path, width=3*cm, height=3*cm)
                    logo.hAlign = 'CENTER'
                    story.append(logo)
                    story.append(Spacer(1, 0.5*cm))
                except Exception as e:
                    print(f"تحذير: لا يمكن إضافة اللوجو: {e}")

            # عنوان الفاتورة
            story.append(Paragraph("فاتورة إيجار معدة", styles['ArabicTitle']))
            story.append(Spacer(1, 0.5*cm))

            # معلومات الشركة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text(self.company_info['name']), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text(self.company_info['address']), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text(self.company_info['phone']), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text(self.company_info['email']), styles['ArabicCenter']))
            story.append(Spacer(1, 1*cm))

            # معلومات الفاتورة مع تنسيق النص العربي
            rental_number = rental_data.get('rental_number', f"R-{rental_data['id']:04d}")
            invoice_info_data = [
                [self.format_arabic_text('رقم الفاتورة:'), self.format_arabic_text(invoice_number)],
                [self.format_arabic_text('تاريخ الإصدار:'), self.format_date(datetime.now().strftime("%Y-%m-%d"))],
                [self.format_arabic_text('رقم الإيجار:'), self.format_arabic_text(rental_number)],
                [self.format_arabic_text('تاريخ الإيجار:'), self.format_date(rental_data['rental_date'])],
                [self.format_arabic_text('تاريخ الإرجاع المتوقع:'), self.format_date(rental_data['return_date'])]
            ]
            
            invoice_info_table = Table(invoice_info_data, colWidths=[4*cm, 6*cm])
            invoice_info_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(invoice_info_table)
            story.append(Spacer(1, 1*cm))
            
            # معلومات العميل مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("معلومات العميل"), styles['ArabicHeading']))
            customer_info_data = [
                [self.format_arabic_text('اسم العميل:'), self.format_arabic_text(customer_data['name'])],
                [self.format_arabic_text('رقم الهاتف:'), self.format_arabic_text(customer_data.get('phone', 'غير محدد'))],
                [self.format_arabic_text('الرقم القومي:'), self.format_arabic_text(customer_data.get('national_id', 'غير محدد'))]
            ]
            
            customer_info_table = Table(customer_info_data, colWidths=[4*cm, 6*cm])
            customer_info_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(customer_info_table)
            story.append(Spacer(1, 1*cm))
            
            # تفاصيل المعدة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("تفاصيل المعدة المؤجرة"), styles['ArabicHeading']))
            equipment_data_table = [
                [self.format_arabic_text('البيان'), self.format_arabic_text('التفاصيل')],
                [self.format_arabic_text('اسم المعدة'), self.format_arabic_text(equipment_data['name'])],
                [self.format_arabic_text('الموديل'), self.format_arabic_text(equipment_data.get('model', 'غير محدد'))],
                [self.format_arabic_text('الفئة'), self.format_arabic_text(equipment_data.get('category', 'غير محدد'))],
                [self.format_arabic_text('السعر اليومي'), f"{equipment_data.get('daily_price', 0)} ج.م"],
                [self.format_arabic_text('عدد الأيام'), str(self.calculate_rental_days(rental_data['rental_date'], rental_data['return_date']))],
                [self.format_arabic_text('إجمالي المبلغ'), f"{rental_data['price']} ج.م"]
            ]
            
            equipment_table = Table(equipment_data_table, colWidths=[5*cm, 7*cm])
            equipment_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (-1, -1), (-1, -1), colors.lightyellow)
            ]))
            
            story.append(equipment_table)
            story.append(Spacer(1, 1*cm))
            
            # الإجمالي النهائي
            total_data = [
                [self.format_arabic_text('الإجمالي النهائي:'), self.format_currency(rental_data['price'])]
            ]
            
            total_table = Table(total_data, colWidths=[8*cm, 4*cm])
            total_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 14),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 0), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 2, colors.black)
            ]))
            
            story.append(total_table)
            story.append(Spacer(1, 2*cm))
            
            # ملاحظات مع تنسيق النص العربي
            if rental_data.get('notes'):
                story.append(Paragraph(self.format_arabic_text("ملاحظات:"), styles['ArabicHeading']))
                story.append(Paragraph(self.format_arabic_text(rental_data['notes']), styles['ArabicNormal']))
                story.append(Spacer(1, 1*cm))

            # تذييل الفاتورة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("شكراً لاختياركم خدماتنا"), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text("نتطلع لخدمتكم مرة أخرى"), styles['ArabicCenter']))
            
            # بناء المستند
            doc.build(story)
            
            if output_path is None:
                buffer.seek(0)
                return buffer
            else:
                return output_path
                
        except Exception as e:
            print(f"خطأ في إنشاء فاتورة الإيجار الفردي: {e}")
            return None
    
    def calculate_rental_days(self, start_date, end_date):
        """حساب عدد أيام الإيجار"""
        try:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d")
            return (end - start).days + 1
        except:
            return 1

    def create_multi_rental_invoice(self, rental_group_data, customer_data, rental_items, output_path=None):
        """إنشاء فاتورة للإيجار المتعدد"""
        try:
            # إعداد المستند
            if output_path is None:
                buffer = io.BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                                      topMargin=2*cm, bottomMargin=2*cm)
            else:
                doc = SimpleDocTemplate(output_path, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                                      topMargin=2*cm, bottomMargin=2*cm)

            # إنشاء الأنماط
            styles = self.create_styles()
            story = []

            # رقم الفاتورة
            invoice_number = self.generate_invoice_number(rental_group_data['id'], 'multi')

            # إضافة اللوجو إذا كان متوفراً
            if self.logo_path:
                try:
                    from reportlab.platypus import Image
                    logo = Image(self.logo_path, width=3*cm, height=3*cm)
                    logo.hAlign = 'CENTER'
                    story.append(logo)
                    story.append(Spacer(1, 0.5*cm))
                except Exception as e:
                    print(f"تحذير: لا يمكن إضافة اللوجو: {e}")

            # عنوان الفاتورة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("فاتورة إيجار متعدد"), styles['ArabicTitle']))
            story.append(Spacer(1, 0.5*cm))

            # معلومات الشركة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text(self.company_info['name']), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text(self.company_info['address']), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text(self.company_info['phone']), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text(self.company_info['email']), styles['ArabicCenter']))
            story.append(Spacer(1, 1*cm))

            # معلومات الفاتورة مع تنسيق النص العربي
            rental_number = rental_group_data.get('rental_number', f"MR-{rental_group_data['id']:04d}")
            invoice_info_data = [
                [self.format_arabic_text('رقم الفاتورة:'), self.format_arabic_text(invoice_number)],
                [self.format_arabic_text('تاريخ الإصدار:'), self.format_date(datetime.now().strftime("%Y-%m-%d"))],
                [self.format_arabic_text('رقم الإيجار المتعدد:'), self.format_arabic_text(rental_number)],
                [self.format_arabic_text('تاريخ الإيجار:'), self.format_date(rental_group_data['rental_date'])],
                [self.format_arabic_text('تاريخ الإرجاع المتوقع:'), self.format_date(rental_group_data['return_date'])],
                [self.format_arabic_text('عدد المعدات:'), str(len(rental_items))]
            ]

            invoice_info_table = Table(invoice_info_data, colWidths=[4*cm, 6*cm])
            invoice_info_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(invoice_info_table)
            story.append(Spacer(1, 1*cm))

            # معلومات العميل مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("معلومات العميل"), styles['ArabicHeading']))
            customer_info_data = [
                [self.format_arabic_text('اسم العميل:'), self.format_arabic_text(customer_data['name'])],
                [self.format_arabic_text('رقم الهاتف:'), self.format_arabic_text(customer_data.get('phone', 'غير محدد'))],
                [self.format_arabic_text('الرقم القومي:'), self.format_arabic_text(customer_data.get('national_id', 'غير محدد'))]
            ]

            customer_info_table = Table(customer_info_data, colWidths=[4*cm, 6*cm])
            customer_info_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
                ('ALIGN', (1, 0), (1, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(customer_info_table)
            story.append(Spacer(1, 1*cm))

            # تفاصيل المعدات المؤجرة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("تفاصيل المعدات المؤجرة"), styles['ArabicHeading']))

            # رأس الجدول مع تنسيق النص العربي
            equipment_data_table = [
                [self.format_arabic_text('#'), self.format_arabic_text('اسم المعدة'),
                 self.format_arabic_text('الفئة'), self.format_arabic_text('السعر اليومي'),
                 self.format_arabic_text('عدد الأيام'), self.format_arabic_text('المجموع الفرعي')]
            ]

            # إضافة بيانات كل معدة مع تنسيق النص العربي
            total_amount = 0
            rental_days = self.calculate_rental_days(rental_group_data['rental_date'], rental_group_data['return_date'])

            for i, item in enumerate(rental_items, 1):
                equipment_data_table.append([
                    str(i),
                    self.format_arabic_text(item['equipment_name']),
                    self.format_arabic_text(item.get('equipment_category', 'غير محدد')),
                    self.format_currency(item['rental_price']),
                    str(rental_days),
                    self.format_currency(item['subtotal'])
                ])
                total_amount += float(item['subtotal'])

            # إضافة صف الإجمالي مع تنسيق النص العربي
            equipment_data_table.append([
                '', '', '', '', self.format_arabic_text('الإجمالي:'), self.format_currency(total_amount)
            ])

            equipment_table = Table(equipment_data_table, colWidths=[1*cm, 4*cm, 2.5*cm, 2*cm, 1.5*cm, 2*cm])
            equipment_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('GRID', (0, 0), (-1, -2), 1, colors.black),
                ('BACKGROUND', (-2, -1), (-1, -1), colors.lightgreen),
                ('GRID', (-2, -1), (-1, -1), 2, colors.black),
                ('FONTSIZE', (-2, -1), (-1, -1), 11),
                ('FONTNAME', (-2, -1), (-1, -1), self.arabic_font)
            ]))

            story.append(equipment_table)
            story.append(Spacer(1, 1*cm))

            # الإجمالي النهائي مع تنسيق النص العربي
            final_total_data = [
                [self.format_arabic_text('الإجمالي النهائي:'), self.format_currency(rental_group_data['total_price'])]
            ]

            final_total_table = Table(final_total_data, colWidths=[8*cm, 4*cm])
            final_total_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 14),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('BACKGROUND', (0, 0), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 2, colors.black)
            ]))

            story.append(final_total_table)
            story.append(Spacer(1, 2*cm))

            # ملاحظات مع تنسيق النص العربي
            if rental_group_data.get('notes'):
                story.append(Paragraph(self.format_arabic_text("ملاحظات:"), styles['ArabicHeading']))
                story.append(Paragraph(self.format_arabic_text(rental_group_data['notes']), styles['ArabicNormal']))
                story.append(Spacer(1, 1*cm))

            # تذييل الفاتورة مع تنسيق النص العربي
            story.append(Paragraph(self.format_arabic_text("شكراً لاختياركم خدماتنا"), styles['ArabicCenter']))
            story.append(Paragraph(self.format_arabic_text("نتطلع لخدمتكم مرة أخرى"), styles['ArabicCenter']))

            # بناء المستند
            doc.build(story)

            if output_path is None:
                buffer.seek(0)
                return buffer
            else:
                return output_path

        except Exception as e:
            print(f"خطأ في إنشاء فاتورة الإيجار المتعدد: {e}")
            return None
