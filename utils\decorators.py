# -*- coding: utf-8 -*-
"""
Decorators for the rental management system
"""

from functools import wraps
from flask import session, redirect, url_for, jsonify, request
import json

def login_required(f):
    """Decorator للتحقق من تسجيل الدخول"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            if request.is_json:
                return jsonify({'error': 'يجب تسجيل الدخول أولاً'}), 401
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """Decorator للتحقق من صلاحيات المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if session.get('role') != 'admin':
            if request.is_json:
                return jsonify({'error': 'غير مصرح لك بالوصول'}), 403
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def manager_required(f):
    """Decorator للتحقق من صلاحيات المدير أو المشرف"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if session.get('role') not in ['admin', 'manager']:
            if request.is_json:
                return jsonify({'error': 'غير مصرح لك بالوصول'}), 403
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def employee_cannot_delete(f):
    """Decorator لمنع الموظفين من الحذف"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if session.get('role') == 'employee' and request.method == 'DELETE':
            return jsonify({'error': 'غير مصرح لك بحذف البيانات'}), 403
        return f(*args, **kwargs)
    return decorated_function

class Permission:
    """فئة الصلاحيات"""
    # صلاحيات العملاء
    CUSTOMERS_VIEW = 'customers_view'
    CUSTOMERS_ADD = 'customers_add'
    CUSTOMERS_EDIT = 'customers_edit'
    CUSTOMERS_DELETE = 'customers_delete'
    
    # صلاحيات المعدات
    EQUIPMENT_VIEW = 'equipment_view'
    EQUIPMENT_ADD = 'equipment_add'
    EQUIPMENT_EDIT = 'equipment_edit'
    EQUIPMENT_DELETE = 'equipment_delete'
    
    # صلاحيات الإيجارات
    RENTALS_VIEW = 'rentals_view'
    RENTALS_ADD = 'rentals_add'
    RENTALS_EDIT = 'rentals_edit'
    RENTALS_DELETE = 'rentals_delete'
    RENTALS_RETURN = 'rentals_return'
    
    # صلاحيات التقارير
    REPORTS_VIEW = 'reports_view'
    REPORTS_EXPORT = 'reports_export'
    
    # صلاحيات النظام
    SYSTEM_SETTINGS = 'system_settings'
    USER_MANAGEMENT = 'user_management'
    BACKUP_RESTORE = 'backup_restore'

def permission_required(permission):
    """Decorator للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            # المدير له جميع الصلاحيات
            if session.get('role') == 'admin':
                return f(*args, **kwargs)
            
            # فحص الصلاحيات للأدوار الأخرى
            user_role = session.get('role')
            
            # صلاحيات المدير
            if user_role == 'manager':
                manager_permissions = [
                    Permission.CUSTOMERS_VIEW, Permission.CUSTOMERS_ADD, Permission.CUSTOMERS_EDIT,
                    Permission.EQUIPMENT_VIEW, Permission.EQUIPMENT_ADD, Permission.EQUIPMENT_EDIT,
                    Permission.RENTALS_VIEW, Permission.RENTALS_ADD, Permission.RENTALS_EDIT, Permission.RENTALS_RETURN,
                    Permission.REPORTS_VIEW, Permission.REPORTS_EXPORT
                ]
                if permission in manager_permissions:
                    return f(*args, **kwargs)
            
            # صلاحيات الموظف
            elif user_role == 'employee':
                employee_permissions = [
                    Permission.CUSTOMERS_VIEW, Permission.CUSTOMERS_ADD,
                    Permission.EQUIPMENT_VIEW,
                    Permission.RENTALS_VIEW, Permission.RENTALS_ADD, Permission.RENTALS_RETURN,
                    Permission.REPORTS_VIEW
                ]
                if permission in employee_permissions:
                    return f(*args, **kwargs)
            
            # غير مصرح
            if request.is_json:
                return jsonify({'error': 'غير مصرح لك بهذا الإجراء'}), 403
            return redirect(url_for('dashboard'))
        
        return decorated_function
    return decorator
