#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة حالات المعدات
Equipment Status Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
import sqlite3
from typing import List, Dict, Optional

class EquipmentStatusManager:
    """مدير حالات المعدات"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self.setup_status_table()
    
    def setup_status_table(self):
        """إعداد جدول حالات المعدات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول حالات المعدات المخصصة
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS equipment_statuses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    display_name TEXT NOT NULL,
                    description TEXT,
                    color TEXT DEFAULT '#000000',
                    is_available BOOLEAN DEFAULT 0,
                    is_system BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إدراج الحالات الافتراضية
            default_statuses = [
                ('Available', 'متاح', 'المعدة متاحة للتأجير', '#28a745', 1, 1),
                ('Rented', 'مؤجر', 'المعدة مؤجرة حالياً', '#dc3545', 0, 1),
                ('Maintenance', 'صيانة', 'المعدة في الصيانة', '#ffc107', 0, 1),
                ('Damaged', 'تالف', 'المعدة تالفة وتحتاج إصلاح', '#6c757d', 0, 0),
                ('Lost', 'مفقود', 'المعدة مفقودة', '#343a40', 0, 0)
            ]
            
            for status_data in default_statuses:
                cursor.execute("""
                    INSERT OR IGNORE INTO equipment_statuses 
                    (name, display_name, description, color, is_available, is_system)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, status_data)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في إعداد جدول حالات المعدات: {e}")
    
    def get_all_statuses(self) -> List[Dict]:
        """الحصول على جميع حالات المعدات"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, display_name, description, color, is_available, is_system
                FROM equipment_statuses
                ORDER BY is_system DESC, display_name
            """)
            
            columns = [description[0] for description in cursor.description]
            statuses = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            conn.close()
            return statuses
            
        except Exception as e:
            print(f"خطأ في الحصول على حالات المعدات: {e}")
            return []
    
    def get_available_statuses(self) -> List[Dict]:
        """الحصول على الحالات المتاحة للتأجير"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, name, display_name, description, color
                FROM equipment_statuses
                WHERE is_available = 1
                ORDER BY display_name
            """)
            
            columns = [description[0] for description in cursor.description]
            statuses = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            conn.close()
            return statuses
            
        except Exception as e:
            print(f"خطأ في الحصول على الحالات المتاحة: {e}")
            return []
    
    def add_custom_status(self, name: str, display_name: str, description: str = "", 
                         color: str = "#000000", is_available: bool = False) -> bool:
        """إضافة حالة مخصصة جديدة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO equipment_statuses 
                (name, display_name, description, color, is_available, is_system)
                VALUES (?, ?, ?, ?, ?, 0)
            """, (name, display_name, description, color, is_available))
            
            conn.commit()
            conn.close()
            return True
            
        except sqlite3.IntegrityError:
            return False  # الاسم موجود مسبقاً
        except Exception as e:
            print(f"خطأ في إضافة الحالة المخصصة: {e}")
            return False
    
    def update_status(self, status_id: int, display_name: str, description: str = "", 
                     color: str = "#000000", is_available: bool = False) -> bool:
        """تحديث حالة موجودة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE equipment_statuses 
                SET display_name = ?, description = ?, color = ?, is_available = ?
                WHERE id = ? AND is_system = 0
            """, (display_name, description, color, is_available, status_id))
            
            conn.commit()
            success = cursor.rowcount > 0
            conn.close()
            return success
            
        except Exception as e:
            print(f"خطأ في تحديث الحالة: {e}")
            return False
    
    def delete_status(self, status_id: int) -> bool:
        """حذف حالة مخصصة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # التحقق من عدم استخدام الحالة
            cursor.execute("SELECT name FROM equipment_statuses WHERE id = ?", (status_id,))
            result = cursor.fetchone()
            if not result:
                return False
            
            status_name = result[0]
            
            # التحقق من استخدام الحالة في المعدات
            cursor.execute("SELECT COUNT(*) FROM equipment WHERE status = ?", (status_name,))
            if cursor.fetchone()[0] > 0:
                return False  # الحالة مستخدمة
            
            # حذف الحالة (فقط الحالات غير النظامية)
            cursor.execute("DELETE FROM equipment_statuses WHERE id = ? AND is_system = 0", (status_id,))
            
            conn.commit()
            success = cursor.rowcount > 0
            conn.close()
            return success
            
        except Exception as e:
            print(f"خطأ في حذف الحالة: {e}")
            return False

class EquipmentStatusDialog:
    """نافذة إدارة حالات المعدات"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db = db_manager
        self.status_manager = EquipmentStatusManager(db_manager)
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
        
        self.create_dialog()
        self.load_statuses()
    
    def create_dialog(self):
        """إنشاء نافذة الحوار"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("إدارة حالات المعدات")
        self.dialog.geometry("700x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="إدارة حالات المعدات", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # شريط الأدوات
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(toolbar, text="إضافة حالة جديدة", 
                  command=self.add_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تعديل", 
                  command=self.edit_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="حذف", 
                  command=self.delete_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="تحديث", 
                  command=self.load_statuses).pack(side=tk.RIGHT)
        
        # جدول الحالات
        columns = ('id', 'display_name', 'description', 'is_available', 'is_system', 'color')
        self.tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=15)
        
        # تكوين الأعمدة
        self.tree.heading('id', text='المعرف')
        self.tree.heading('display_name', text='اسم الحالة')
        self.tree.heading('description', text='الوصف')
        self.tree.heading('is_available', text='متاح للتأجير')
        self.tree.heading('is_system', text='حالة نظامية')
        self.tree.heading('color', text='اللون')
        
        self.tree.column('id', width=60)
        self.tree.column('display_name', width=120)
        self.tree.column('description', width=200)
        self.tree.column('is_available', width=100)
        self.tree.column('is_system', width=100)
        self.tree.column('color', width=80)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # زر الإغلاق
        ttk.Button(main_frame, text="إغلاق", command=self.close).pack(pady=(10, 0))
    
    def load_statuses(self):
        """تحميل حالات المعدات"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # تحميل الحالات
        statuses = self.status_manager.get_all_statuses()
        
        for status in statuses:
            available_text = "نعم" if status['is_available'] else "لا"
            system_text = "نعم" if status['is_system'] else "لا"
            
            # إدراج البيانات مع لون الخلفية
            item_id = self.tree.insert('', tk.END, values=(
                status['id'],
                status['display_name'],
                status['description'] or '',
                available_text,
                system_text,
                status['color']
            ))
            
            # تطبيق لون الخلفية إذا كان متاحاً
            if status['color'] and status['color'] != '#000000':
                try:
                    self.tree.set(item_id, 'color', '●')
                except:
                    pass
    
    def add_status(self):
        """إضافة حالة جديدة"""
        dialog = StatusEditDialog(self.dialog, self.status_manager)
        if dialog.show():
            self.load_statuses()
    
    def edit_status(self):
        """تعديل حالة محددة"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار حالة للتعديل")
            return
        
        item = self.tree.item(selected[0])
        status_id = item['values'][0]
        is_system = item['values'][4] == "نعم"
        
        if is_system:
            messagebox.showwarning("تحذير", "لا يمكن تعديل الحالات النظامية")
            return
        
        # الحصول على بيانات الحالة
        statuses = self.status_manager.get_all_statuses()
        status_data = None
        for status in statuses:
            if status['id'] == status_id:
                status_data = status
                break
        
        if status_data:
            dialog = StatusEditDialog(self.dialog, self.status_manager, status_data)
            if dialog.show():
                self.load_statuses()
    
    def delete_status(self):
        """حذف حالة محددة"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار حالة للحذف")
            return
        
        item = self.tree.item(selected[0])
        status_id = item['values'][0]
        status_name = item['values'][1]
        is_system = item['values'][4] == "نعم"
        
        if is_system:
            messagebox.showwarning("تحذير", "لا يمكن حذف الحالات النظامية")
            return
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الحالة '{status_name}'؟"):
            if self.status_manager.delete_status(status_id):
                messagebox.showinfo("نجح", "تم حذف الحالة بنجاح")
                self.load_statuses()
            else:
                messagebox.showerror("خطأ", "فشل في حذف الحالة. قد تكون مستخدمة في معدات موجودة")
    
    def close(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()

class StatusEditDialog:
    """نافذة تعديل/إضافة حالة"""
    
    def __init__(self, parent, status_manager, status_data=None):
        self.parent = parent
        self.status_manager = status_manager
        self.status_data = status_data
        self.result = False
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
        
        self.create_dialog()
        if status_data:
            self.load_data()
    
    def create_dialog(self):
        """إنشاء نافذة الحوار"""
        title = "تعديل الحالة" if self.status_data else "إضافة حالة جديدة"
        
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 100}+{self.parent.winfo_rooty() + 100}")
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # نموذج البيانات
        form_frame = ttk.LabelFrame(main_frame, text="بيانات الحالة", padding="15")
        form_frame.pack(fill=tk.X, pady=(0, 20))
        form_frame.columnconfigure(1, weight=1)
        
        # اسم الحالة (بالإنجليزية)
        if not self.status_data:  # فقط عند الإضافة
            ttk.Label(form_frame, text="اسم الحالة (EN):", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)
            self.name_var = tk.StringVar()
            ttk.Entry(form_frame, textvariable=self.name_var, font=self.arabic_font).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # اسم الحالة المعروض
        row_offset = 0 if self.status_data else 1
        ttk.Label(form_frame, text="اسم الحالة المعروض:", font=self.arabic_font).grid(row=row_offset, column=0, sticky=tk.W, pady=5)
        self.display_name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.display_name_var, font=self.arabic_font).grid(row=row_offset, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # الوصف
        ttk.Label(form_frame, text="الوصف:", font=self.arabic_font).grid(row=row_offset+1, column=0, sticky=tk.W, pady=5)
        self.description_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.description_var, font=self.arabic_font).grid(row=row_offset+1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # متاح للتأجير
        self.is_available_var = tk.BooleanVar()
        ttk.Checkbutton(form_frame, text="متاح للتأجير", variable=self.is_available_var, 
                       font=self.arabic_font).grid(row=row_offset+2, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # اللون
        ttk.Label(form_frame, text="لون الحالة:", font=self.arabic_font).grid(row=row_offset+3, column=0, sticky=tk.W, pady=5)
        self.color_var = tk.StringVar(value="#000000")
        color_frame = ttk.Frame(form_frame)
        color_frame.grid(row=row_offset+3, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Entry(color_frame, textvariable=self.color_var, width=10).pack(side=tk.LEFT)
        ttk.Label(color_frame, text="(مثال: #ff0000 للأحمر)", font=("Arial", 8)).pack(side=tk.LEFT, padx=(5, 0))
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        save_text = "حفظ التعديلات" if self.status_data else "إضافة"
        ttk.Button(buttons_frame, text=save_text, command=self.save).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", command=self.cancel).pack(side=tk.RIGHT)
    
    def load_data(self):
        """تحميل بيانات الحالة للتعديل"""
        if self.status_data:
            self.display_name_var.set(self.status_data['display_name'])
            self.description_var.set(self.status_data['description'] or '')
            self.is_available_var.set(self.status_data['is_available'])
            self.color_var.set(self.status_data['color'] or '#000000')
    
    def save(self):
        """حفظ الحالة"""
        display_name = self.display_name_var.get().strip()
        description = self.description_var.get().strip()
        is_available = self.is_available_var.get()
        color = self.color_var.get().strip()
        
        if not display_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم الحالة المعروض")
            return
        
        if self.status_data:
            # تعديل حالة موجودة
            success = self.status_manager.update_status(
                self.status_data['id'], display_name, description, color, is_available
            )
        else:
            # إضافة حالة جديدة
            name = self.name_var.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الحالة بالإنجليزية")
                return
            
            success = self.status_manager.add_custom_status(
                name, display_name, description, color, is_available
            )
        
        if success:
            action = "تعديل" if self.status_data else "إضافة"
            messagebox.showinfo("نجح", f"تم {action} الحالة بنجاح")
            self.result = True
            self.dialog.destroy()
        else:
            action = "تعديل" if self.status_data else "إضافة"
            messagebox.showerror("خطأ", f"فشل في {action} الحالة")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
        return self.result
