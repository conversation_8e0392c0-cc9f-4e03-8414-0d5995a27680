#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إعادة تعيين كلمة مرور المدير
Admin Password Reset Tool
"""

import sqlite3
import hashlib
import secrets
from user_manager import User<PERSON><PERSON>

def reset_admin_password():
    """إعادة تعيين كلمة مرور المدير إلى القيمة الافتراضية"""
    
    db_path = "rental_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود المستخدم admin
        cursor.execute("SELECT id, username FROM users WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"تم العثور على المستخدم: {admin_user[1]} (ID: {admin_user[0]})")
            
            # إنشاء كلمة مرور جديدة
            new_password = "admin123"
            salt = secrets.token_hex(32)
            password_hash = hashlib.pbkdf2_hmac('sha256', new_password.encode('utf-8'), salt.encode('utf-8'), 100000).hex()
            
            # تحديث كلمة المرور
            cursor.execute("""
                UPDATE users 
                SET password_hash = ?, salt = ?, failed_login_attempts = 0, locked_until = NULL
                WHERE username = 'admin'
            """, (password_hash, salt))
            
            # إلغاء جميع الجلسات النشطة
            cursor.execute("UPDATE user_sessions SET is_active = 0 WHERE user_id = ?", (admin_user[0],))
            
            conn.commit()
            
            print("✅ تم إعادة تعيين كلمة مرور المدير بنجاح!")
            print("📋 بيانات الدخول الجديدة:")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
            
        else:
            print("❌ لم يتم العثور على المستخدم admin")
            print("🔧 سيتم إنشاء مستخدم admin جديد...")
            
            # إنشاء مستخدم admin جديد
            salt = secrets.token_hex(32)
            password_hash = hashlib.pbkdf2_hmac('sha256', "admin123".encode('utf-8'), salt.encode('utf-8'), 100000).hex()
            
            cursor.execute("""
                INSERT INTO users (username, password_hash, salt, full_name, role, email, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, ("admin", password_hash, salt, "مدير النظام", UserRole.ADMIN.value, "<EMAIL>", True))
            
            conn.commit()
            
            print("✅ تم إنشاء المستخدم admin بنجاح!")
            print("📋 بيانات الدخول:")
            print("   اسم المستخدم: admin")
            print("   كلمة المرور: admin123")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في إعادة تعيين كلمة المرور: {e}")

def show_all_users():
    """عرض جميع المستخدمين في النظام"""
    
    db_path = "rental_system.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, username, full_name, role, is_active, created_at, last_login
            FROM users
            ORDER BY id
        """)
        
        users = cursor.fetchall()
        
        if users:
            print("\n📋 قائمة المستخدمين في النظام:")
            print("-" * 80)
            print(f"{'ID':<5} {'اسم المستخدم':<15} {'الاسم الكامل':<20} {'الدور':<15} {'نشط':<8} {'آخر دخول':<20}")
            print("-" * 80)
            
            for user in users:
                user_id, username, full_name, role, is_active, created_at, last_login = user
                active_status = "نعم" if is_active else "لا"
                last_login_display = last_login if last_login else "لم يسجل دخول"
                
                print(f"{user_id:<5} {username:<15} {full_name:<20} {role:<15} {active_status:<8} {last_login_display:<20}")
        else:
            print("❌ لا يوجد مستخدمين في النظام")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في عرض المستخدمين: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إعادة تعيين كلمة مرور المدير")
    print("=" * 50)
    
    # عرض المستخدمين الحاليين
    show_all_users()
    
    print("\n" + "=" * 50)
    
    # إعادة تعيين كلمة مرور المدير
    reset_admin_password()
    
    print("\n" + "=" * 50)
    print("✅ انتهت العملية. يمكنك الآن تسجيل الدخول باستخدام:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")

if __name__ == "__main__":
    main()
