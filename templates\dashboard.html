{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة تأجير المعدات{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Cards */
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    /* Gradient Backgrounds */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
    }

    /* Text Colors */
    .text-white-75 {
        color: rgba(255, 255, 255, 0.75) !important;
    }

    .text-white-50 {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    /* Action Cards */
    .action-card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }

    .action-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
        border-left-color: #007bff;
    }

    /* Icon Circles */
    .icon-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    /* Remove default link styles */
    a.text-decoration-none:hover {
        text-decoration: none !important;
    }

    .action-card .card-title {
        color: #2c3e50;
        font-weight: 600;
    }

    .action-card:hover .card-title {
        color: #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-3">
    <!-- إحصائيات أساسية -->
    <div class="row g-3 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card dashboard-card bg-gradient-primary">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="text-white mb-0">{{ stats.total_equipment }}</h3>
                            <p class="text-white-75 mb-0">إجمالي المعدات</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-camera fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card dashboard-card bg-gradient-success">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="text-white mb-0">{{ stats.total_customers }}</h3>
                            <p class="text-white-75 mb-0">العملاء</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-users fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card dashboard-card bg-gradient-warning">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="text-white mb-0">{{ stats.active_rentals }}</h3>
                            <p class="text-white-75 mb-0">إيجارات نشطة</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-handshake fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card dashboard-card bg-gradient-danger">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h3 class="text-white mb-0">{{ stats.overdue_rentals }}</h3>
                            <p class="text-white-75 mb-0">متأخرة</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-exclamation-triangle fa-2x text-white-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الأقسام الرئيسية -->
    <div class="row g-3">
        <div class="col-lg-6 col-md-6">
            <a href="{{ url_for('equipment') }}" class="text-decoration-none">
                <div class="card action-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-camera text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-1">إدارة المعدات</h5>
                            <p class="card-text text-muted mb-0">عرض وإدارة جميع المعدات</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-primary rounded-pill">{{ stats.total_equipment }}</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <div class="col-lg-6 col-md-6">
            <a href="{{ url_for('rentals') }}" class="text-decoration-none">
                <div class="card action-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="icon-circle bg-warning">
                                <i class="fas fa-handshake text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="card-title mb-1">إدارة الإيجارات</h5>
                            <p class="card-text text-muted mb-0">متابعة الإيجارات النشطة والمتأخرة</p>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-warning rounded-pill">{{ stats.active_rentals }}</span>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Modals will be added here -->
<div id="modalsContainer"></div>
{% endblock %}
