#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام البحث المتقدم
Advanced Search System
"""

import sqlite3
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, date
from enum import Enum

class SearchScope(Enum):
    """نطاق البحث"""
    ALL = "all"                    # البحث في جميع الجداول
    EQUIPMENT = "equipment"        # البحث في المعدات فقط
    CUSTOMERS = "customers"        # البحث في العملاء فقط
    RENTALS = "rentals"           # البحث في الإيجارات فقط

class SortOrder(Enum):
    """ترتيب النتائج"""
    ASC = "ASC"                   # تصاعدي
    DESC = "DESC"                 # تنازلي

class SearchFilter:
    """فلتر البحث"""
    
    def __init__(self, field: str, operator: str, value: Any, table: str = None):
        """
        إنشاء فلتر بحث
        
        Args:
            field: اسم الحقل
            operator: المشغل (=, !=, LIKE, >, <, >=, <=, IN, BETWEEN)
            value: القيمة
            table: الجدول (اختياري)
        """
        self.field = field
        self.operator = operator
        self.value = value
        self.table = table

class AdvancedSearchEngine:
    """محرك البحث المتقدم"""
    
    def __init__(self, db_path: str = "rental_system.db"):
        """تهيئة محرك البحث"""
        self.db_path = db_path
        
        # تعريف الجداول والحقول القابلة للبحث
        self.searchable_fields = {
            'equipment': {
                'id': {'type': 'int', 'display': 'المعرف'},
                'name': {'type': 'text', 'display': 'اسم المعدة'},
                'model': {'type': 'text', 'display': 'الموديل'},
                'category': {'type': 'text', 'display': 'النوع'},
                'serial_number': {'type': 'text', 'display': 'الرقم التسلسلي'},
                'status': {'type': 'text', 'display': 'الحالة'},
                'created_date': {'type': 'date', 'display': 'تاريخ الإضافة'}
            },
            'customers': {
                'id': {'type': 'int', 'display': 'المعرف'},
                'name': {'type': 'text', 'display': 'اسم العميل'},
                'phone': {'type': 'text', 'display': 'رقم الهاتف'},
                'photo_path': {'type': 'text', 'display': 'مسار الصورة'},
                'created_date': {'type': 'date', 'display': 'تاريخ التسجيل'}
            },
            'rentals': {
                'id': {'type': 'int', 'display': 'رقم الإيجار'},
                'customer_id': {'type': 'int', 'display': 'معرف العميل'},
                'equipment_id': {'type': 'int', 'display': 'معرف المعدة'},
                'rental_date': {'type': 'date', 'display': 'تاريخ الإيجار'},
                'return_date': {'type': 'date', 'display': 'تاريخ الإرجاع المتوقع'},
                'actual_return_date': {'type': 'date', 'display': 'تاريخ الإرجاع الفعلي'},
                'price': {'type': 'float', 'display': 'السعر'},
                'status': {'type': 'text', 'display': 'الحالة'},
                'created_date': {'type': 'date', 'display': 'تاريخ الإنشاء'}
            }
        }
        
        # المشغلات المتاحة لكل نوع بيانات
        self.operators = {
            'text': [
                ('=', 'يساوي'),
                ('!=', 'لا يساوي'),
                ('LIKE', 'يحتوي على'),
                ('NOT LIKE', 'لا يحتوي على'),
                ('STARTS_WITH', 'يبدأ بـ'),
                ('ENDS_WITH', 'ينتهي بـ')
            ],
            'int': [
                ('=', 'يساوي'),
                ('!=', 'لا يساوي'),
                ('>', 'أكبر من'),
                ('<', 'أصغر من'),
                ('>=', 'أكبر من أو يساوي'),
                ('<=', 'أصغر من أو يساوي'),
                ('BETWEEN', 'بين')
            ],
            'float': [
                ('=', 'يساوي'),
                ('!=', 'لا يساوي'),
                ('>', 'أكبر من'),
                ('<', 'أصغر من'),
                ('>=', 'أكبر من أو يساوي'),
                ('<=', 'أصغر من أو يساوي'),
                ('BETWEEN', 'بين')
            ],
            'date': [
                ('=', 'في تاريخ'),
                ('!=', 'ليس في تاريخ'),
                ('>', 'بعد'),
                ('<', 'قبل'),
                ('>=', 'في أو بعد'),
                ('<=', 'في أو قبل'),
                ('BETWEEN', 'بين تاريخين')
            ]
        }
    
    def search(self, query: str = "", scope: SearchScope = SearchScope.ALL,
               filters: List[SearchFilter] = None, sort_field: str = None,
               sort_order: SortOrder = SortOrder.DESC, limit: int = 100) -> Dict[str, List[Dict]]:
        """
        تنفيذ البحث المتقدم
        
        Args:
            query: نص البحث العام
            scope: نطاق البحث
            filters: قائمة الفلاتر
            sort_field: حقل الترتيب
            sort_order: اتجاه الترتيب
            limit: عدد النتائج المحدود
            
        Returns:
            Dict[str, List[Dict]]: نتائج البحث مجمعة حسب النوع
        """
        results = {
            'equipment': [],
            'customers': [],
            'rentals': [],
            'total_count': 0
        }
        
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            
            # البحث في المعدات
            if scope in [SearchScope.ALL, SearchScope.EQUIPMENT]:
                equipment_results = self._search_equipment(conn, query, filters, sort_field, sort_order, limit)
                results['equipment'] = equipment_results
                results['total_count'] += len(equipment_results)
            
            # البحث في العملاء
            if scope in [SearchScope.ALL, SearchScope.CUSTOMERS]:
                customer_results = self._search_customers(conn, query, filters, sort_field, sort_order, limit)
                results['customers'] = customer_results
                results['total_count'] += len(customer_results)
            
            # البحث في الإيجارات
            if scope in [SearchScope.ALL, SearchScope.RENTALS]:
                rental_results = self._search_rentals(conn, query, filters, sort_field, sort_order, limit)
                results['rentals'] = rental_results
                results['total_count'] += len(rental_results)
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في البحث: {e}")
        
        return results
    
    def _search_equipment(self, conn: sqlite3.Connection, query: str, filters: List[SearchFilter],
                         sort_field: str, sort_order: SortOrder, limit: int) -> List[Dict]:
        """البحث في المعدات"""
        base_query = """
            SELECT id, name, model, category, serial_number, status, created_date
            FROM equipment
        """
        
        where_conditions = []
        params = []
        
        # إضافة البحث النصي العام
        if query:
            text_conditions = []
            for field in ['name', 'model', 'category', 'serial_number']:
                text_conditions.append(f"{field} LIKE ?")
                params.append(f"%{query}%")
            
            where_conditions.append(f"({' OR '.join(text_conditions)})")
        
        # إضافة الفلاتر
        if filters:
            for filter_obj in filters:
                if filter_obj.table in [None, 'equipment']:
                    condition, filter_params = self._build_filter_condition(filter_obj)
                    if condition:
                        where_conditions.append(condition)
                        params.extend(filter_params)
        
        # بناء الاستعلام النهائي
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        # إضافة الترتيب
        if sort_field and sort_field in self.searchable_fields['equipment']:
            base_query += f" ORDER BY {sort_field} {sort_order.value}"
        else:
            base_query += " ORDER BY created_date DESC"
        
        # إضافة الحد الأقصى
        base_query += f" LIMIT {limit}"
        
        cursor = conn.execute(base_query, params)
        return [dict(row) for row in cursor.fetchall()]
    
    def _search_customers(self, conn: sqlite3.Connection, query: str, filters: List[SearchFilter],
                         sort_field: str, sort_order: SortOrder, limit: int) -> List[Dict]:
        """البحث في العملاء"""
        base_query = """
            SELECT id, name, phone, photo_path, created_date
            FROM customers
        """
        
        where_conditions = []
        params = []
        
        # إضافة البحث النصي العام
        if query:
            text_conditions = []
            for field in ['name', 'phone']:
                text_conditions.append(f"{field} LIKE ?")
                params.append(f"%{query}%")

            where_conditions.append(f"({' OR '.join(text_conditions)})")
        
        # إضافة الفلاتر
        if filters:
            for filter_obj in filters:
                if filter_obj.table in [None, 'customers']:
                    condition, filter_params = self._build_filter_condition(filter_obj)
                    if condition:
                        where_conditions.append(condition)
                        params.extend(filter_params)
        
        # بناء الاستعلام النهائي
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        # إضافة الترتيب
        if sort_field and sort_field in self.searchable_fields['customers']:
            base_query += f" ORDER BY {sort_field} {sort_order.value}"
        else:
            base_query += " ORDER BY created_date DESC"
        
        # إضافة الحد الأقصى
        base_query += f" LIMIT {limit}"
        
        cursor = conn.execute(base_query, params)
        return [dict(row) for row in cursor.fetchall()]
    
    def _search_rentals(self, conn: sqlite3.Connection, query: str, filters: List[SearchFilter],
                       sort_field: str, sort_order: SortOrder, limit: int) -> List[Dict]:
        """البحث في الإيجارات"""
        base_query = """
            SELECT r.id, r.customer_id, r.equipment_id, r.rental_date, r.return_date,
                   r.actual_return_date, r.price, r.status,
                   c.name as customer_name, c.phone as customer_phone,
                   e.name as equipment_name, e.model as equipment_model
            FROM rentals r
            LEFT JOIN customers c ON r.customer_id = c.id
            LEFT JOIN equipment e ON r.equipment_id = e.id
        """
        
        where_conditions = []
        params = []
        
        # إضافة البحث النصي العام
        if query:
            text_conditions = [
                "c.name LIKE ?",
                "c.phone LIKE ?",
                "e.name LIKE ?",
                "e.model LIKE ?",
                "r.status LIKE ?"
            ]
            for _ in text_conditions:
                params.append(f"%{query}%")
            
            where_conditions.append(f"({' OR '.join(text_conditions)})")
        
        # إضافة الفلاتر
        if filters:
            for filter_obj in filters:
                if filter_obj.table in [None, 'rentals']:
                    condition, filter_params = self._build_filter_condition(filter_obj, 'r')
                    if condition:
                        where_conditions.append(condition)
                        params.extend(filter_params)
        
        # بناء الاستعلام النهائي
        if where_conditions:
            base_query += " WHERE " + " AND ".join(where_conditions)
        
        # إضافة الترتيب
        if sort_field and sort_field in self.searchable_fields['rentals']:
            base_query += f" ORDER BY r.{sort_field} {sort_order.value}"
        else:
            base_query += " ORDER BY r.rental_date DESC"
        
        # إضافة الحد الأقصى
        base_query += f" LIMIT {limit}"
        
        cursor = conn.execute(base_query, params)
        return [dict(row) for row in cursor.fetchall()]

    def _build_filter_condition(self, filter_obj: SearchFilter, table_alias: str = "") -> Tuple[str, List]:
        """بناء شرط الفلتر"""
        field = filter_obj.field
        operator = filter_obj.operator
        value = filter_obj.value

        if table_alias:
            field = f"{table_alias}.{field}"

        params = []

        if operator == "=":
            condition = f"{field} = ?"
            params.append(value)

        elif operator == "!=":
            condition = f"{field} != ?"
            params.append(value)

        elif operator == "LIKE":
            condition = f"{field} LIKE ?"
            params.append(f"%{value}%")

        elif operator == "NOT LIKE":
            condition = f"{field} NOT LIKE ?"
            params.append(f"%{value}%")

        elif operator == "STARTS_WITH":
            condition = f"{field} LIKE ?"
            params.append(f"{value}%")

        elif operator == "ENDS_WITH":
            condition = f"{field} LIKE ?"
            params.append(f"%{value}")

        elif operator in [">", "<", ">=", "<="]:
            condition = f"{field} {operator} ?"
            params.append(value)

        elif operator == "BETWEEN":
            if isinstance(value, (list, tuple)) and len(value) == 2:
                condition = f"{field} BETWEEN ? AND ?"
                params.extend(value)
            else:
                return "", []

        elif operator == "IN":
            if isinstance(value, (list, tuple)):
                placeholders = ",".join(["?" for _ in value])
                condition = f"{field} IN ({placeholders})"
                params.extend(value)
            else:
                return "", []

        else:
            return "", []

        return condition, params

    def get_searchable_fields(self, table: str = None) -> Dict[str, Dict]:
        """الحصول على الحقول القابلة للبحث"""
        if table:
            return self.searchable_fields.get(table, {})
        return self.searchable_fields

    def get_operators_for_field_type(self, field_type: str) -> List[Tuple[str, str]]:
        """الحصول على المشغلات المتاحة لنوع الحقل"""
        return self.operators.get(field_type, [])

    def quick_search(self, query: str, limit: int = 50) -> Dict[str, List[Dict]]:
        """بحث سريع في جميع الجداول"""
        return self.search(query=query, scope=SearchScope.ALL, limit=limit)

    def search_equipment_by_status(self, status: str) -> List[Dict]:
        """البحث في المعدات حسب الحالة"""
        filters = [SearchFilter('status', '=', status, 'equipment')]
        results = self.search(scope=SearchScope.EQUIPMENT, filters=filters)
        return results['equipment']

    def search_overdue_rentals(self) -> List[Dict]:
        """البحث في الإيجارات المتأخرة"""
        today = date.today().isoformat()
        filters = [
            SearchFilter('status', '=', 'Active', 'rentals'),
            SearchFilter('return_date', '<', today, 'rentals')
        ]
        results = self.search(scope=SearchScope.RENTALS, filters=filters)
        return results['rentals']

    def get_search_statistics(self) -> Dict[str, int]:
        """الحصول على إحصائيات البحث"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # إحصائيات المعدات
            cursor.execute("SELECT COUNT(*) FROM equipment")
            stats['total_equipment'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM equipment WHERE status = 'Available'")
            stats['available_equipment'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM equipment WHERE status = 'Rented'")
            stats['rented_equipment'] = cursor.fetchone()[0]

            # إحصائيات العملاء
            cursor.execute("SELECT COUNT(*) FROM customers")
            stats['total_customers'] = cursor.fetchone()[0]

            # إحصائيات الإيجارات
            cursor.execute("SELECT COUNT(*) FROM rentals")
            stats['total_rentals'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM rentals WHERE status = 'Active'")
            stats['active_rentals'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM rentals WHERE status = 'Returned'")
            stats['returned_rentals'] = cursor.fetchone()[0]

            # الإيجارات المتأخرة
            today = date.today().isoformat()
            cursor.execute("SELECT COUNT(*) FROM rentals WHERE status = 'Active' AND return_date < ?", (today,))
            stats['overdue_rentals'] = cursor.fetchone()[0]

            conn.close()
            return stats

        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات البحث: {e}")
            return {}

    def export_search_results(self, results: Dict[str, List[Dict]], format: str = "csv") -> str:
        """تصدير نتائج البحث"""
        if format.lower() == "csv":
            return self._export_to_csv(results)
        elif format.lower() == "json":
            return self._export_to_json(results)
        else:
            raise ValueError("تنسيق التصدير غير مدعوم")

    def _export_to_csv(self, results: Dict[str, List[Dict]]) -> str:
        """تصدير النتائج إلى CSV"""
        import csv
        import io

        output = io.StringIO()

        # تصدير المعدات
        if results.get('equipment'):
            output.write("=== المعدات ===\n")
            writer = csv.DictWriter(output, fieldnames=results['equipment'][0].keys())
            writer.writeheader()
            writer.writerows(results['equipment'])
            output.write("\n")

        # تصدير العملاء
        if results.get('customers'):
            output.write("=== العملاء ===\n")
            writer = csv.DictWriter(output, fieldnames=results['customers'][0].keys())
            writer.writeheader()
            writer.writerows(results['customers'])
            output.write("\n")

        # تصدير الإيجارات
        if results.get('rentals'):
            output.write("=== الإيجارات ===\n")
            writer = csv.DictWriter(output, fieldnames=results['rentals'][0].keys())
            writer.writeheader()
            writer.writerows(results['rentals'])
            output.write("\n")

        return output.getvalue()

    def _export_to_json(self, results: Dict[str, List[Dict]]) -> str:
        """تصدير النتائج إلى JSON"""
        import json
        return json.dumps(results, ensure_ascii=False, indent=2)
