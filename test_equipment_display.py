#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def test_equipment_queries():
    """اختبار استعلامات المعدات"""
    config = get_config()
    conn = sqlite3.connect(config.DATABASE_PATH)
    cursor = conn.cursor()

    print('=== اختبار استعلام صفحة المعدات ===')
    cursor.execute('''
        SELECT id, name, model, serial_number, category, status,
               price, daily_rental_price, description, created_at
        FROM equipment
        ORDER BY created_at DESC
    ''')

    equipment_list = []
    for row in cursor.fetchall():
        equipment_list.append({
            'id': row[0],
            'name': row[1],
            'model': row[2] or '',
            'serial_number': row[3] or '',
            'category': row[4] or 'غير محدد',
            'status': row[5],
            'price': float(row[6] or 0),
            'daily_rental_price': float(row[7] or 0),
            'description': row[8] or '',
            'created_at': row[9]
        })

    print(f'تم جلب {len(equipment_list)} معدة')
    for i, eq in enumerate(equipment_list[:5]):
        print(f'{i+1}. {eq["name"]} - {eq["status"]} - {eq["category"]}')

    print('\n=== اختبار استعلام المعدات المتاحة ===')
    cursor.execute('''
        SELECT id, name, model, category, daily_rental_price, status, description
        FROM equipment
        WHERE status = 'Available'
        ORDER BY category, name
    ''')

    available_equipment = []
    for row in cursor.fetchall():
        available_equipment.append({
            'id': row[0],
            'name': row[1],
            'model': row[2] or '',
            'category': row[3] or 'غير محدد',
            'daily_price': float(row[4] or 0),
            'status': row[5],
            'description': row[6] or ''
        })

    print(f'تم جلب {len(available_equipment)} معدة متاحة')
    for i, eq in enumerate(available_equipment[:5]):
        print(f'{i+1}. {eq["name"]} - {eq["daily_price"]} ج.م/يوم')

    print('\n=== اختبار استعلام المعدات الخارجية ===')
    cursor.execute('''
        SELECT id, equipment_name, equipment_model, equipment_category,
               supplier_name, daily_rental_cost, customer_daily_price,
               profit_margin, rental_start_date, rental_end_date, status
        FROM external_equipment
        WHERE status = 'Active'
        ORDER BY equipment_category, equipment_name
    ''')

    external_equipment = []
    for row in cursor.fetchall():
        external_equipment.append({
            'id': row[0],
            'equipment_name': row[1],
            'equipment_model': row[2] or '',
            'equipment_category': row[3],
            'supplier_name': row[4],
            'daily_rental_cost': float(row[5]),
            'customer_daily_price': float(row[6]),
            'profit_margin': float(row[7]),
            'rental_start_date': row[8],
            'rental_end_date': row[9],
            'status': row[10]
        })

    print(f'تم جلب {len(external_equipment)} معدة خارجية')
    for i, eq in enumerate(external_equipment):
        print(f'{i+1}. {eq["equipment_name"]} - {eq["supplier_name"]} - {eq["customer_daily_price"]} ج.م/يوم')

    print('\n=== اختبار ربط المعدات الخارجية بالمكاتب ===')
    cursor.execute('''
        SELECT ee.id, ee.equipment_name, ee.equipment_category, 
               ee.customer_daily_price, o.name as office_name
        FROM external_equipment ee
        LEFT JOIN offices o ON ee.office_id = o.id
        WHERE ee.status = 'Active'
    ''')

    linked_equipment = cursor.fetchall()
    print(f'تم جلب {len(linked_equipment)} معدة خارجية مع ربط المكاتب')
    for eq in linked_equipment:
        office_name = eq[4] if eq[4] else 'غير مربوط بمكتب'
        print(f'- {eq[1]} ({eq[2]}) - {office_name} - {eq[3]} ج.م/يوم')

    conn.close()

if __name__ == '__main__':
    test_equipment_queries()
