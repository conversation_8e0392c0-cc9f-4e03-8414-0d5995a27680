{% extends "base.html" %}

{% block title %}عناصر المخزون{% endblock %}

{% block extra_css %}
<style>
    .filter-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .inventory-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .stock-badge {
        font-size: 0.9rem;
        padding: 5px 10px;
    }
    
    .low-stock {
        background-color: #dc3545;
        color: white;
    }
    
    .out-of-stock {
        background-color: #6c757d;
        color: white;
    }
    
    .good-stock {
        background-color: #28a745;
        color: white;
    }
    
    .category-badge {
        font-size: 0.8rem;
        padding: 3px 8px;
    }
    
    .action-buttons .btn {
        margin: 2px;
        padding: 5px 10px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-list me-2"></i>عناصر المخزون</h2>
                    <p class="text-muted">إدارة جميع عناصر المخزون والإكسسوارات</p>
                </div>
                <div>
                    <a href="{{ url_for('inventory') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-2"></i>لوحة التحكم
                    </a>
                    <a href="{{ url_for('add_inventory_item') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة عنصر جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card filter-card">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('inventory_items') }}">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search" 
                                       value="{{ search_term }}" placeholder="اسم العنصر، الماركة، الموديل...">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category_id">
                                    <option value="">جميع الفئات</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" 
                                            {% if current_category == category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="low_stock_only" 
                                           value="true" {% if low_stock_only %}checked{% endif %}>
                                    <label class="form-check-label">
                                        المخزون المنخفض فقط
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-light me-2">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="{{ url_for('inventory_items') }}" class="btn btn-outline-light">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="text-muted">
                        عدد العناصر: <strong>{{ items|length }}</strong>
                        {% if search_term %}
                        | البحث عن: <strong>"{{ search_term }}"</strong>
                        {% endif %}
                        {% if current_category %}
                        | الفئة: <strong>{{ categories|selectattr('id', 'equalto', current_category)|first|attr('name') }}</strong>
                        {% endif %}
                        {% if low_stock_only %}
                        | <span class="text-danger"><strong>المخزون المنخفض فقط</strong></span>
                        {% endif %}
                    </span>
                </div>
                <div>
                    <button class="btn btn-outline-secondary btn-sm" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-1"></i>تصدير Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <div class="row">
        <div class="col-12">
            <div class="inventory-table">
                {% if items %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>العنصر</th>
                                <th>الفئة</th>
                                <th>الماركة/الموديل</th>
                                <th>المخزون الحالي</th>
                                <th>الحد الأدنى</th>
                                <th>سعر الوحدة</th>
                                <th>سعر التأجير</th>
                                <th>المورد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in items %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ item.name }}</strong>
                                        {% if item.part_number %}
                                        <br><small class="text-muted">رقم القطعة: {{ item.part_number }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge category-badge bg-primary">
                                        {{ item.category_name }}
                                    </span>
                                </td>
                                <td>
                                    {% if item.brand %}{{ item.brand }}{% endif %}
                                    {% if item.model %}<br><small class="text-muted">{{ item.model }}</small>{% endif %}
                                </td>
                                <td>
                                    {% set stock_class = 'good-stock' %}
                                    {% if item.current_stock == 0 %}
                                        {% set stock_class = 'out-of-stock' %}
                                    {% elif item.current_stock <= item.min_stock_level %}
                                        {% set stock_class = 'low-stock' %}
                                    {% endif %}
                                    <span class="badge stock-badge {{ stock_class }}">
                                        {{ item.current_stock }}
                                    </span>
                                </td>
                                <td>{{ item.min_stock_level }}</td>
                                <td>{{ "%.2f"|format(item.unit_price) }} ر.س</td>
                                <td>{{ "%.2f"|format(item.rental_price) }} ر.س</td>
                                <td>
                                    {% if item.supplier_name %}
                                        <small>{{ item.supplier_name }}</small>
                                    {% else %}
                                        <small class="text-muted">غير محدد</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-outline-info btn-sm" 
                                                onclick="viewItemDetails({{ item.id }})" 
                                                title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('edit_inventory_item', item_id=item.id) }}" 
                                           class="btn btn-outline-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="addStock({{ item.id }})" 
                                                title="إضافة مخزون">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteItem({{ item.id }})" 
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عناصر في المخزون</h5>
                    <p class="text-muted">
                        {% if search_term or current_category or low_stock_only %}
                        لا توجد عناصر تطابق معايير البحث المحددة
                        {% else %}
                        ابدأ بإضافة عناصر جديدة للمخزون
                        {% endif %}
                    </p>
                    <a href="{{ url_for('add_inventory_item') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة عنصر جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Item Details Modal -->
<div class="modal fade" id="itemDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العنصر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="itemDetailsContent">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<!-- Add Stock Modal -->
<div class="modal fade" id="addStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addStockForm">
                    <input type="hidden" id="stockItemId">
                    <div class="mb-3">
                        <label class="form-label">الكمية المضافة</label>
                        <input type="number" class="form-control" id="stockQuantity" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">سعر الوحدة (اختياري)</label>
                        <input type="number" class="form-control" id="stockUnitCost" step="0.01" min="0">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="stockNotes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="submitAddStock()">إضافة</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewItemDetails(itemId) {
    // TODO: تحميل تفاصيل العنصر
    $('#itemDetailsModal').modal('show');
}

function addStock(itemId) {
    $('#stockItemId').val(itemId);
    $('#addStockForm')[0].reset();
    $('#addStockModal').modal('show');
}

function submitAddStock() {
    // TODO: إرسال طلب إضافة المخزون
    showAlert('success', 'تم إضافة المخزون بنجاح');
    $('#addStockModal').modal('hide');
}

function deleteItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        // إنشاء نموذج مخفي لإرسال POST request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/inventory/items/${itemId}/delete`;
        document.body.appendChild(form);
        form.submit();
    }
}

function exportToExcel() {
    // TODO: تصدير البيانات إلى Excel
    showAlert('info', 'سيتم تنفيذ هذه الميزة قريباً');
}
</script>
{% endblock %}
