#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطي لنظام إدارة تأجير الكاميرات والعدسات
Backup Manager for Camera and Lens Rental Management System
"""

import os
import shutil
import sqlite3
import zipfile
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, Tuple
import threading
import time

class BackupManager:
    """مدير النسخ الاحتياطي مع دعم النسخ التلقائي والاستعادة"""
    
    def __init__(self, db_path: str = "rental_system.db", backup_dir: str = "backups"):
        """
        تهيئة مدير النسخ الاحتياطي
        
        Args:
            db_path: مسار قاعدة البيانات الرئيسية
            backup_dir: مجلد النسخ الاحتياطية
        """
        self.db_path = db_path
        self.backup_dir = backup_dir
        self.photos_dir = "customer_photos"
        self.config_file = os.path.join(backup_dir, "backup_config.json")
        
        # إعدادات النسخ الاحتياطي الافتراضية
        self.default_config = {
            "auto_backup_enabled": True,
            "backup_interval_hours": 24,  # كل 24 ساعة
            "max_backups": 30,  # الاحتفاظ بـ 30 نسخة احتياطية
            "include_photos": True,
            "compress_backups": True,
            "last_backup": None
        }
        
        # إنشاء مجلد النسخ الاحتياطية
        self.ensure_backup_directory()
        
        # تحميل الإعدادات
        self.config = self.load_config()
        
        # متغيرات التحكم في النسخ التلقائي
        self.auto_backup_thread = None
        self.stop_auto_backup = False
    
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
    
    def load_config(self) -> Dict:
        """تحميل إعدادات النسخ الاحتياطي"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # دمج الإعدادات الافتراضية مع المحملة
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            except Exception as e:
                print(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
        
        return self.default_config.copy()
    
    def save_config(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
    
    def create_backup(self, backup_name: Optional[str] = None) -> Tuple[bool, str]:
        """
        إنشاء نسخة احتياطية
        
        Args:
            backup_name: اسم النسخة الاحتياطية (اختياري)
            
        Returns:
            Tuple[bool, str]: (نجح/فشل, رسالة)
        """
        try:
            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            # مسار النسخة الاحتياطية
            if self.config["compress_backups"]:
                backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
                return self._create_compressed_backup(backup_path, backup_name)
            else:
                backup_path = os.path.join(self.backup_dir, backup_name)
                return self._create_folder_backup(backup_path, backup_name)
                
        except Exception as e:
            error_msg = f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
            print(error_msg)
            return False, error_msg
    
    def _create_compressed_backup(self, backup_path: str, backup_name: str) -> Tuple[bool, str]:
        """إنشاء نسخة احتياطية مضغوطة"""
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "rental_system.db")
                
                # إضافة صور العملاء إذا كانت مطلوبة
                if self.config["include_photos"] and os.path.exists(self.photos_dir):
                    for root, dirs, files in os.walk(self.photos_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, ".")
                            zipf.write(file_path, arcname)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    "backup_name": backup_name,
                    "created_at": datetime.now().isoformat(),
                    "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                    "includes_photos": self.config["include_photos"],
                    "version": "2.0"
                }
                
                zipf.writestr("backup_info.json", json.dumps(backup_info, ensure_ascii=False, indent=2))
            
            # تحديث وقت آخر نسخة احتياطية
            self.config["last_backup"] = datetime.now().isoformat()
            self.save_config()
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups()
            
            return True, f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_name}"
            
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة المضغوطة: {str(e)}"
    
    def _create_folder_backup(self, backup_path: str, backup_name: str) -> Tuple[bool, str]:
        """إنشاء نسخة احتياطية في مجلد"""
        try:
            # إنشاء مجلد النسخة الاحتياطية
            os.makedirs(backup_path, exist_ok=True)
            
            # نسخ قاعدة البيانات
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, os.path.join(backup_path, "rental_system.db"))
            
            # نسخ صور العملاء إذا كانت مطلوبة
            if self.config["include_photos"] and os.path.exists(self.photos_dir):
                photos_backup_path = os.path.join(backup_path, "customer_photos")
                shutil.copytree(self.photos_dir, photos_backup_path, dirs_exist_ok=True)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                "backup_name": backup_name,
                "created_at": datetime.now().isoformat(),
                "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                "includes_photos": self.config["include_photos"],
                "version": "2.0"
            }
            
            info_path = os.path.join(backup_path, "backup_info.json")
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            # تحديث وقت آخر نسخة احتياطية
            self.config["last_backup"] = datetime.now().isoformat()
            self.save_config()
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups()
            
            return True, f"تم إنشاء النسخة الاحتياطية بنجاح: {backup_name}"
            
        except Exception as e:
            return False, f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            if len(backups) > self.config["max_backups"]:
                # ترتيب النسخ حسب التاريخ (الأقدم أولاً)
                backups.sort(key=lambda x: x["created_at"])
                
                # حذف النسخ الزائدة
                backups_to_delete = backups[:-self.config["max_backups"]]
                for backup in backups_to_delete:
                    self.delete_backup(backup["name"])
                    
        except Exception as e:
            print(f"خطأ في تنظيف النسخ القديمة: {e}")
    
    def list_backups(self) -> List[Dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        backups = []
        
        try:
            if not os.path.exists(self.backup_dir):
                return backups
            
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                # تخطي ملف الإعدادات
                if item == "backup_config.json":
                    continue
                
                backup_info = None
                
                # التحقق من النسخ المضغوطة
                if item.endswith('.zip') and zipfile.is_zipfile(item_path):
                    try:
                        with zipfile.ZipFile(item_path, 'r') as zipf:
                            if 'backup_info.json' in zipf.namelist():
                                info_data = zipf.read('backup_info.json')
                                backup_info = json.loads(info_data.decode('utf-8'))
                    except:
                        pass
                
                # التحقق من النسخ في مجلدات
                elif os.path.isdir(item_path):
                    info_file = os.path.join(item_path, 'backup_info.json')
                    if os.path.exists(info_file):
                        try:
                            with open(info_file, 'r', encoding='utf-8') as f:
                                backup_info = json.load(f)
                        except:
                            pass
                
                # إضافة معلومات النسخة الاحتياطية
                if backup_info:
                    backup_info["name"] = item
                    backup_info["path"] = item_path
                    backup_info["size"] = self._get_backup_size(item_path)
                    backups.append(backup_info)
                else:
                    # نسخة احتياطية بدون معلومات (قديمة)
                    stat = os.stat(item_path)
                    backups.append({
                        "name": item,
                        "path": item_path,
                        "created_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        "size": self._get_backup_size(item_path),
                        "version": "unknown"
                    })
        
        except Exception as e:
            print(f"خطأ في قراءة قائمة النسخ الاحتياطية: {e}")
        
        return backups
    
    def _get_backup_size(self, path: str) -> int:
        """حساب حجم النسخة الاحتياطية"""
        try:
            if os.path.isfile(path):
                return os.path.getsize(path)
            elif os.path.isdir(path):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                return total_size
        except:
            pass
        return 0

    def restore_backup(self, backup_name: str) -> Tuple[bool, str]:
        """
        استعادة نسخة احتياطية

        Args:
            backup_name: اسم النسخة الاحتياطية

        Returns:
            Tuple[bool, str]: (نجح/فشل, رسالة)
        """
        try:
            backup_path = os.path.join(self.backup_dir, backup_name)

            # التحقق من وجود النسخة الاحتياطية
            if not os.path.exists(backup_path):
                return False, "النسخة الاحتياطية غير موجودة"

            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup_name = f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.create_backup(current_backup_name)

            # استعادة النسخة الاحتياطية
            if backup_name.endswith('.zip'):
                return self._restore_compressed_backup(backup_path)
            else:
                return self._restore_folder_backup(backup_path)

        except Exception as e:
            error_msg = f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"
            print(error_msg)
            return False, error_msg

    def _restore_compressed_backup(self, backup_path: str) -> Tuple[bool, str]:
        """استعادة نسخة احتياطية مضغوطة"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # استعادة قاعدة البيانات
                if 'rental_system.db' in zipf.namelist():
                    zipf.extract('rental_system.db', '.')

                # استعادة صور العملاء
                for file_info in zipf.infolist():
                    if file_info.filename.startswith('customer_photos/'):
                        zipf.extract(file_info, '.')

            return True, "تم استعادة النسخة الاحتياطية بنجاح"

        except Exception as e:
            return False, f"خطأ في استعادة النسخة المضغوطة: {str(e)}"

    def _restore_folder_backup(self, backup_path: str) -> Tuple[bool, str]:
        """استعادة نسخة احتياطية من مجلد"""
        try:
            # استعادة قاعدة البيانات
            db_backup_path = os.path.join(backup_path, "rental_system.db")
            if os.path.exists(db_backup_path):
                shutil.copy2(db_backup_path, self.db_path)

            # استعادة صور العملاء
            photos_backup_path = os.path.join(backup_path, "customer_photos")
            if os.path.exists(photos_backup_path):
                if os.path.exists(self.photos_dir):
                    shutil.rmtree(self.photos_dir)
                shutil.copytree(photos_backup_path, self.photos_dir)

            return True, "تم استعادة النسخة الاحتياطية بنجاح"

        except Exception as e:
            return False, f"خطأ في استعادة النسخة الاحتياطية: {str(e)}"

    def delete_backup(self, backup_name: str) -> Tuple[bool, str]:
        """
        حذف نسخة احتياطية

        Args:
            backup_name: اسم النسخة الاحتياطية

        Returns:
            Tuple[bool, str]: (نجح/فشل, رسالة)
        """
        try:
            backup_path = os.path.join(self.backup_dir, backup_name)

            if not os.path.exists(backup_path):
                return False, "النسخة الاحتياطية غير موجودة"

            if os.path.isfile(backup_path):
                os.remove(backup_path)
            elif os.path.isdir(backup_path):
                shutil.rmtree(backup_path)

            return True, f"تم حذف النسخة الاحتياطية: {backup_name}"

        except Exception as e:
            error_msg = f"خطأ في حذف النسخة الاحتياطية: {str(e)}"
            return False, error_msg

    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if not self.config["auto_backup_enabled"]:
            return

        if self.auto_backup_thread and self.auto_backup_thread.is_alive():
            return  # النسخ التلقائي يعمل بالفعل

        self.stop_auto_backup = False
        self.auto_backup_thread = threading.Thread(target=self._auto_backup_worker, daemon=True)
        self.auto_backup_thread.start()
        print("تم بدء النسخ الاحتياطي التلقائي")

    def stop_auto_backup_service(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.stop_auto_backup = True
        if self.auto_backup_thread:
            self.auto_backup_thread.join(timeout=5)
        print("تم إيقاف النسخ الاحتياطي التلقائي")

    def _auto_backup_worker(self):
        """عامل النسخ الاحتياطي التلقائي"""
        while not self.stop_auto_backup:
            try:
                # التحقق من الحاجة لإنشاء نسخة احتياطية
                if self._should_create_backup():
                    success, message = self.create_backup()
                    if success:
                        print(f"تم إنشاء نسخة احتياطية تلقائية: {message}")
                    else:
                        print(f"فشل في إنشاء نسخة احتياطية تلقائية: {message}")

                # انتظار لمدة ساعة قبل التحقق مرة أخرى
                for _ in range(3600):  # 3600 ثانية = ساعة واحدة
                    if self.stop_auto_backup:
                        break
                    time.sleep(1)

            except Exception as e:
                print(f"خطأ في النسخ الاحتياطي التلقائي: {e}")
                time.sleep(300)  # انتظار 5 دقائق في حالة الخطأ

    def _should_create_backup(self) -> bool:
        """التحقق من الحاجة لإنشاء نسخة احتياطية"""
        if not self.config["last_backup"]:
            return True  # لم يتم إنشاء نسخة احتياطية من قبل

        try:
            last_backup_time = datetime.fromisoformat(self.config["last_backup"])
            time_since_backup = datetime.now() - last_backup_time
            backup_interval = timedelta(hours=self.config["backup_interval_hours"])

            return time_since_backup >= backup_interval

        except Exception as e:
            print(f"خطأ في التحقق من وقت النسخة الاحتياطية: {e}")
            return True  # في حالة الخطأ، قم بإنشاء نسخة احتياطية

    def update_config(self, new_config: Dict):
        """تحديث إعدادات النسخ الاحتياطي"""
        self.config.update(new_config)
        self.save_config()

        # إعادة تشغيل النسخ التلقائي إذا تم تغيير الإعدادات
        if self.config["auto_backup_enabled"]:
            self.stop_auto_backup_service()
            self.start_auto_backup()
        else:
            self.stop_auto_backup_service()

    def get_backup_status(self) -> Dict:
        """الحصول على حالة النسخ الاحتياطي"""
        backups = self.list_backups()
        total_size = sum(backup["size"] for backup in backups)

        status = {
            "auto_backup_enabled": self.config["auto_backup_enabled"],
            "auto_backup_running": self.auto_backup_thread and self.auto_backup_thread.is_alive(),
            "last_backup": self.config["last_backup"],
            "total_backups": len(backups),
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "next_backup_due": None
        }

        # حساب موعد النسخة الاحتياطية التالية
        if self.config["last_backup"] and self.config["auto_backup_enabled"]:
            try:
                last_backup_time = datetime.fromisoformat(self.config["last_backup"])
                next_backup_time = last_backup_time + timedelta(hours=self.config["backup_interval_hours"])
                status["next_backup_due"] = next_backup_time.isoformat()
            except:
                pass

        return status

    def export_backup_info(self) -> str:
        """تصدير معلومات النسخ الاحتياطية إلى JSON"""
        backups = self.list_backups()
        status = self.get_backup_status()

        export_data = {
            "export_date": datetime.now().isoformat(),
            "backup_status": status,
            "backup_list": backups,
            "config": self.config
        }

        return json.dumps(export_data, ensure_ascii=False, indent=2)
