{% extends "base.html" %}

{% block title %}تفاصيل المكتب - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    {% if office %}
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-building me-2 text-primary"></i>
                        {{ office.name }}
                    </h1>
                    <p class="text-muted mb-0">تفاصيل المكتب ومعداته</p>
                    {% else %}
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                        مكتب غير موجود
                    </h1>
                    {% endif %}
                </div>
                <div>
                    <button class="btn btn-outline-secondary" onclick="window.location.href='{{ url_for('offices') }}'">
                        <i class="fas fa-arrow-left me-2"></i>العودة للمكاتب
                    </button>
                </div>
            </div>
        </div>
    </div>

    {% if error %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ error }}
            </div>
        </div>
    </div>
    {% endif %}

    {% if office %}
    <!-- Office Info -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات المكتب
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td>{{ office.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>{{ office.address or 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td>{{ office.phone or 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td><strong>الشخص المسؤول:</strong></td>
                            <td>{{ office.contact_person or 'غير محدد' }}</td>
                        </tr>
                        <tr>
                            <td><strong>النوع:</strong></td>
                            <td>
                                {% if office.office_type == 'supplier' %}
                                <span class="badge bg-info">مورد</span>
                                {% elif office.office_type == 'partner' %}
                                <span class="badge bg-warning">شريك</span>
                                {% elif office.office_type == 'branch' %}
                                <span class="badge bg-secondary">فرع</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                {% if office.status == 'Active' %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ office.created_date }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات المعدات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h3 class="text-primary mb-1">{{ equipment|length }}</h3>
                                <p class="text-muted mb-0">إجمالي المعدات</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success mb-1">{{ equipment|selectattr('status', 'equalto', 'Active')|list|length }}</h3>
                            <p class="text-muted mb-0">المعدات النشطة</p>
                        </div>
                    </div>
                    
                    {% if equipment %}
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h5 class="text-info mb-1">
                                {{ "%.2f"|format(equipment|sum(attribute='customer_daily_price')) }} ج.م
                            </h5>
                            <p class="text-muted mb-0">إجمالي الإيرادات اليومية المتوقعة</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Equipment List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-camera me-2"></i>معدات المكتب
                        <span class="badge bg-primary ms-2">{{ equipment|length }}</span>
                        <!-- Debug: Equipment count = {{ equipment|length }} -->
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Debug: Equipment variable exists = {{ equipment is defined }} -->
                    <!-- Debug: Equipment type = {{ equipment.__class__.__name__ if equipment is defined else 'undefined' }} -->
                    {% if equipment %}
                    <!-- Debug: Equipment has items = True -->
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم المعدة</th>
                                    <th>الموديل</th>
                                    <th>الفئة</th>
                                    <th>المورد</th>
                                    <th>التكلفة اليومية</th>
                                    <th>السعر للعميل</th>
                                    <th>هامش الربح</th>
                                    <th>فترة الإيجار</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for eq in equipment %}
                                <tr>
                                    <td>{{ eq.id }}</td>
                                    <td>
                                        <strong>{{ eq.equipment_name }}</strong>
                                        {% if eq.notes %}
                                        <br><small class="text-muted">{{ eq.notes }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ eq.equipment_model }}</td>
                                    <td><span class="badge bg-info">{{ eq.equipment_category }}</span></td>
                                    <td>{{ eq.supplier_name }}</td>
                                    <td>{{ eq.daily_rental_cost }} ج.م</td>
                                    <td><strong>{{ eq.customer_daily_price }} ج.م</strong></td>
                                    <td>
                                        <span class="badge bg-success">{{ eq.profit_margin }} ج.م</span>
                                    </td>
                                    <td>
                                        <small>
                                            من: {{ eq.rental_start_date }}<br>
                                            إلى: {{ eq.rental_end_date }}
                                        </small>
                                    </td>
                                    <td>
                                        {% if eq.status == 'Active' %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد معدات لهذا المكتب</h5>
                        <p class="text-muted">يمكنك إضافة معدات جديدة من صفحة المخزون</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Add any JavaScript functionality here if needed
console.log('تم تحميل صفحة تفاصيل المكتب');
</script>
{% endblock %}
