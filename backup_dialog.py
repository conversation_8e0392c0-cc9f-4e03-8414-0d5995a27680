#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة النسخ الاحتياطي
Backup Management Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import font
from datetime import datetime
from typing import Dict, List, Optional
import threading
import os

from backup_manager import BackupManager

class BackupDialog:
    """نافذة إدارة النسخ الاحتياطي"""
    
    def __init__(self, parent):
        """تهيئة نافذة النسخ الاحتياطي"""
        self.parent = parent
        self.backup_manager = BackupManager()
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إدارة النسخ الاحتياطي")
        self.dialog.geometry("800x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{parent.winfo_rootx() + 50}+{parent.winfo_rooty() + 50}")
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحديث البيانات
        self.refresh_data()
        
        # بدء النسخ التلقائي إذا كان مفعلاً
        if self.backup_manager.config["auto_backup_enabled"]:
            self.backup_manager.start_auto_backup()
    
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # إنشاء دفتر التبويبات
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab()
        
        # تبويب الاستعادة
        self.create_restore_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        # أزرار التحكم
        self.create_control_buttons()
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        backup_frame = ttk.Frame(self.notebook)
        self.notebook.add(backup_frame, text="النسخ الاحتياطي")
        
        # معلومات الحالة
        status_frame = ttk.LabelFrame(backup_frame, text="حالة النسخ الاحتياطي")
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # تسميات الحالة
        self.status_labels = {}
        status_info = [
            ("auto_backup", "النسخ التلقائي"),
            ("last_backup", "آخر نسخة احتياطية"),
            ("total_backups", "عدد النسخ"),
            ("total_size", "الحجم الإجمالي")
        ]
        
        for i, (key, label) in enumerate(status_info):
            row = i // 2
            col = i % 2
            
            ttk.Label(status_frame, text=f"{label}:", 
                     font=self.arabic_font).grid(row=row, column=col*2, 
                                                sticky=tk.W, padx=10, pady=5)
            
            self.status_labels[key] = ttk.Label(status_frame, text="جاري التحميل...", 
                                               font=self.arabic_font_bold)
            self.status_labels[key].grid(row=row, column=col*2+1, 
                                        sticky=tk.W, padx=10, pady=5)
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = ttk.Frame(backup_frame)
        backup_buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(backup_buttons_frame, text="إنشاء نسخة احتياطية الآن", 
                  command=self.create_backup_now).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(backup_buttons_frame, text="تحديث", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # قائمة النسخ الاحتياطية
        backups_frame = ttk.LabelFrame(backup_frame, text="النسخ الاحتياطية المتاحة")
        backups_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إنشاء Treeview للنسخ الاحتياطية
        columns = ("name", "date", "size", "version")
        self.backups_tree = ttk.Treeview(backups_frame, columns=columns, show="headings")
        
        # تكوين الأعمدة
        self.backups_tree.heading("name", text="اسم النسخة")
        self.backups_tree.heading("date", text="تاريخ الإنشاء")
        self.backups_tree.heading("size", text="الحجم (MB)")
        self.backups_tree.heading("version", text="الإصدار")
        
        self.backups_tree.column("name", width=200)
        self.backups_tree.column("date", width=150)
        self.backups_tree.column("size", width=100)
        self.backups_tree.column("version", width=80)
        
        # شريط التمرير
        backup_scrollbar = ttk.Scrollbar(backups_frame, orient=tk.VERTICAL, 
                                        command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=backup_scrollbar.set)
        
        self.backups_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        backup_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار إدارة النسخ
        backup_manage_frame = ttk.Frame(backup_frame)
        backup_manage_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(backup_manage_frame, text="حذف النسخة المحددة", 
                  command=self.delete_selected_backup).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(backup_manage_frame, text="تصدير معلومات النسخ", 
                  command=self.export_backup_info).pack(side=tk.LEFT)
    
    def create_restore_tab(self):
        """إنشاء تبويب الاستعادة"""
        restore_frame = ttk.Frame(self.notebook)
        self.notebook.add(restore_frame, text="الاستعادة")
        
        # تحذير
        warning_frame = ttk.Frame(restore_frame)
        warning_frame.pack(fill=tk.X, padx=5, pady=5)
        
        warning_text = """⚠️ تحذير: استعادة النسخة الاحتياطية ستستبدل البيانات الحالية بالكامل.
سيتم إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة."""
        
        ttk.Label(warning_frame, text=warning_text, font=self.arabic_font,
                 foreground="red", wraplength=700).pack(pady=10)
        
        # اختيار النسخة للاستعادة
        select_frame = ttk.LabelFrame(restore_frame, text="اختيار النسخة للاستعادة")
        select_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(select_frame, text="النسخة الاحتياطية:", 
                 font=self.arabic_font).pack(side=tk.LEFT, padx=5)
        
        self.restore_var = tk.StringVar()
        self.restore_combo = ttk.Combobox(select_frame, textvariable=self.restore_var,
                                         state="readonly", width=40)
        self.restore_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        ttk.Button(select_frame, text="استعادة", 
                  command=self.restore_backup).pack(side=tk.RIGHT, padx=5)
        
        # معلومات النسخة المحددة
        info_frame = ttk.LabelFrame(restore_frame, text="معلومات النسخة المحددة")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.restore_info_text = tk.Text(info_frame, height=15, wrap=tk.WORD,
                                        font=self.arabic_font, state=tk.DISABLED)
        
        restore_info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL,
                                              command=self.restore_info_text.yview)
        self.restore_info_text.configure(yscrollcommand=restore_info_scrollbar.set)
        
        self.restore_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        restore_info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط تغيير الاختيار
        self.restore_combo.bind('<<ComboboxSelected>>', self.on_restore_selection_change)
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="الإعدادات")
        
        # إعدادات النسخ التلقائي
        auto_frame = ttk.LabelFrame(settings_frame, text="النسخ التلقائي")
        auto_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # تفعيل النسخ التلقائي
        self.auto_backup_var = tk.BooleanVar()
        ttk.Checkbutton(auto_frame, text="تفعيل النسخ الاحتياطي التلقائي",
                       variable=self.auto_backup_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # فترة النسخ
        interval_frame = ttk.Frame(auto_frame)
        interval_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(interval_frame, text="فترة النسخ (ساعات):", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.interval_var = tk.StringVar()
        interval_spinbox = ttk.Spinbox(interval_frame, from_=1, to=168, 
                                      textvariable=self.interval_var, width=10)
        interval_spinbox.pack(side=tk.LEFT, padx=5)
        
        # عدد النسخ المحفوظة
        max_backups_frame = ttk.Frame(auto_frame)
        max_backups_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(max_backups_frame, text="عدد النسخ المحفوظة:", 
                 font=self.arabic_font).pack(side=tk.LEFT)
        
        self.max_backups_var = tk.StringVar()
        max_backups_spinbox = ttk.Spinbox(max_backups_frame, from_=5, to=100,
                                         textvariable=self.max_backups_var, width=10)
        max_backups_spinbox.pack(side=tk.LEFT, padx=5)
        
        # إعدادات إضافية
        options_frame = ttk.LabelFrame(settings_frame, text="خيارات إضافية")
        options_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.include_photos_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="تضمين صور العملاء",
                       variable=self.include_photos_var).pack(anchor=tk.W, padx=5, pady=5)
        
        self.compress_backups_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="ضغط النسخ الاحتياطية",
                       variable=self.compress_backups_var).pack(anchor=tk.W, padx=5, pady=5)
        
        # أزرار الإعدادات
        settings_buttons_frame = ttk.Frame(settings_frame)
        settings_buttons_frame.pack(fill=tk.X, padx=5, pady=10)
        
        ttk.Button(settings_buttons_frame, text="حفظ الإعدادات", 
                  command=self.save_settings).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(settings_buttons_frame, text="استعادة الافتراضي", 
                  command=self.reset_settings).pack(side=tk.LEFT)
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(button_frame, text="إغلاق",
                  command=self.close_dialog).pack(side=tk.RIGHT)

    def refresh_data(self):
        """تحديث البيانات"""
        # تحديث معلومات الحالة
        self.update_status_info()

        # تحديث قائمة النسخ الاحتياطية
        self.update_backups_list()

        # تحديث قائمة الاستعادة
        self.update_restore_combo()

        # تحديث الإعدادات
        self.load_settings()

    def update_status_info(self):
        """تحديث معلومات الحالة"""
        try:
            status = self.backup_manager.get_backup_status()

            # حالة النسخ التلقائي
            auto_status = "مفعل" if status["auto_backup_enabled"] else "معطل"
            if status["auto_backup_running"]:
                auto_status += " (يعمل)"
            self.status_labels["auto_backup"].config(text=auto_status)

            # آخر نسخة احتياطية
            if status["last_backup"]:
                try:
                    last_backup_time = datetime.fromisoformat(status["last_backup"])
                    last_backup_str = last_backup_time.strftime("%Y-%m-%d %H:%M")
                except:
                    last_backup_str = status["last_backup"]
            else:
                last_backup_str = "لم يتم إنشاء نسخة بعد"
            self.status_labels["last_backup"].config(text=last_backup_str)

            # عدد النسخ
            self.status_labels["total_backups"].config(text=str(status["total_backups"]))

            # الحجم الإجمالي
            self.status_labels["total_size"].config(text=f"{status['total_size_mb']} MB")

        except Exception as e:
            print(f"خطأ في تحديث معلومات الحالة: {e}")

    def update_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            # مسح القائمة الحالية
            for item in self.backups_tree.get_children():
                self.backups_tree.delete(item)

            # إضافة النسخ الاحتياطية
            backups = self.backup_manager.list_backups()
            backups.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            for backup in backups:
                try:
                    # تنسيق التاريخ
                    created_at = backup.get("created_at", "")
                    if created_at:
                        try:
                            date_obj = datetime.fromisoformat(created_at)
                            formatted_date = date_obj.strftime("%Y-%m-%d %H:%M")
                        except:
                            formatted_date = created_at
                    else:
                        formatted_date = "غير معروف"

                    # تنسيق الحجم
                    size_mb = round(backup.get("size", 0) / (1024 * 1024), 2)

                    # الإصدار
                    version = backup.get("version", "غير معروف")

                    self.backups_tree.insert("", tk.END, values=(
                        backup["name"],
                        formatted_date,
                        size_mb,
                        version
                    ))

                except Exception as e:
                    print(f"خطأ في إضافة النسخة الاحتياطية {backup.get('name', 'unknown')}: {e}")

        except Exception as e:
            print(f"خطأ في تحديث قائمة النسخ الاحتياطية: {e}")

    def update_restore_combo(self):
        """تحديث قائمة الاستعادة"""
        try:
            backups = self.backup_manager.list_backups()
            backup_names = [backup["name"] for backup in backups]
            backup_names.sort(reverse=True)

            self.restore_combo['values'] = backup_names

            if backup_names:
                self.restore_combo.set(backup_names[0])
                self.on_restore_selection_change()
            else:
                self.restore_combo.set("")
                self.restore_info_text.config(state=tk.NORMAL)
                self.restore_info_text.delete(1.0, tk.END)
                self.restore_info_text.insert(tk.END, "لا توجد نسخ احتياطية متاحة")
                self.restore_info_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"خطأ في تحديث قائمة الاستعادة: {e}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            config = self.backup_manager.config

            self.auto_backup_var.set(config.get("auto_backup_enabled", True))
            self.interval_var.set(str(config.get("backup_interval_hours", 24)))
            self.max_backups_var.set(str(config.get("max_backups", 30)))
            self.include_photos_var.set(config.get("include_photos", True))
            self.compress_backups_var.set(config.get("compress_backups", True))

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def create_backup_now(self):
        """إنشاء نسخة احتياطية فورية"""
        def backup_thread():
            try:
                # تعطيل الزر أثناء النسخ
                self.dialog.after(0, lambda: self.set_backup_button_state(False))

                success, message = self.backup_manager.create_backup()

                # تحديث الواجهة
                self.dialog.after(0, lambda: self.on_backup_complete(success, message))

            except Exception as e:
                error_msg = f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
                self.dialog.after(0, lambda: self.on_backup_complete(False, error_msg))

        # تشغيل النسخ في خيط منفصل
        threading.Thread(target=backup_thread, daemon=True).start()

    def set_backup_button_state(self, enabled: bool):
        """تعيين حالة زر النسخ الاحتياطي"""
        # البحث عن الزر وتعيين حالته
        # هذا مبسط - في التطبيق الحقيقي نحتاج مرجع للزر
        pass

    def on_backup_complete(self, success: bool, message: str):
        """معالج اكتمال النسخ الاحتياطي"""
        if success:
            messagebox.showinfo("نجح", message)
        else:
            messagebox.showerror("خطأ", message)

        # تحديث البيانات
        self.refresh_data()

    def delete_selected_backup(self):
        """حذف النسخة الاحتياطية المحددة"""
        selection = self.backups_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return

        item = self.backups_tree.item(selection[0])
        backup_name = item['values'][0]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل أنت متأكد من حذف النسخة الاحتياطية '{backup_name}'؟"):
            success, message = self.backup_manager.delete_backup(backup_name)

            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_data()
            else:
                messagebox.showerror("خطأ", message)

    def export_backup_info(self):
        """تصدير معلومات النسخ الاحتياطية"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="حفظ معلومات النسخ الاحتياطية",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                backup_info = self.backup_manager.export_backup_info()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(backup_info)

                messagebox.showinfo("نجح", f"تم تصدير معلومات النسخ الاحتياطية إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير المعلومات: {str(e)}")

    def restore_backup(self):
        """استعادة النسخة الاحتياطية المحددة"""
        backup_name = self.restore_var.get()
        if not backup_name:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للاستعادة")
            return

        warning_msg = """تحذير: هذه العملية ستستبدل جميع البيانات الحالية!

سيتم:
1. إنشاء نسخة احتياطية من الحالة الحالية
2. استعادة النسخة المحددة
3. إعادة تشغيل التطبيق مطلوبة

هل أنت متأكد من المتابعة؟"""

        if messagebox.askyesno("تأكيد الاستعادة", warning_msg):
            def restore_thread():
                try:
                    success, message = self.backup_manager.restore_backup(backup_name)
                    self.dialog.after(0, lambda: self.on_restore_complete(success, message))
                except Exception as e:
                    error_msg = f"خطأ في الاستعادة: {str(e)}"
                    self.dialog.after(0, lambda: self.on_restore_complete(False, error_msg))

            threading.Thread(target=restore_thread, daemon=True).start()

    def on_restore_complete(self, success: bool, message: str):
        """معالج اكتمال الاستعادة"""
        if success:
            messagebox.showinfo("نجح", f"{message}\n\nيرجى إعادة تشغيل التطبيق لتطبيق التغييرات.")
        else:
            messagebox.showerror("خطأ", message)

    def on_restore_selection_change(self, event=None):
        """معالج تغيير اختيار الاستعادة"""
        backup_name = self.restore_var.get()
        if not backup_name:
            return

        try:
            # البحث عن معلومات النسخة الاحتياطية
            backups = self.backup_manager.list_backups()
            selected_backup = None

            for backup in backups:
                if backup["name"] == backup_name:
                    selected_backup = backup
                    break

            # عرض المعلومات
            self.restore_info_text.config(state=tk.NORMAL)
            self.restore_info_text.delete(1.0, tk.END)

            if selected_backup:
                info_text = f"""اسم النسخة: {selected_backup['name']}
تاريخ الإنشاء: {selected_backup.get('created_at', 'غير معروف')}
الحجم: {round(selected_backup.get('size', 0) / (1024 * 1024), 2)} MB
الإصدار: {selected_backup.get('version', 'غير معروف')}
يتضمن الصور: {selected_backup.get('includes_photos', 'غير معروف')}

المسار: {selected_backup.get('path', 'غير معروف')}"""
            else:
                info_text = "لم يتم العثور على معلومات النسخة الاحتياطية"

            self.restore_info_text.insert(tk.END, info_text)
            self.restore_info_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f"خطأ في عرض معلومات النسخة الاحتياطية: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            new_config = {
                "auto_backup_enabled": self.auto_backup_var.get(),
                "backup_interval_hours": int(self.interval_var.get()),
                "max_backups": int(self.max_backups_var.get()),
                "include_photos": self.include_photos_var.get(),
                "compress_backups": self.compress_backups_var.get()
            }

            self.backup_manager.update_config(new_config)
            messagebox.showinfo("نجح", "تم حفظ الإعدادات بنجاح")

            # تحديث معلومات الحالة
            self.update_status_info()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للأرقام")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        if messagebox.askyesno("تأكيد", "هل تريد استعادة الإعدادات الافتراضية؟"):
            self.backup_manager.config = self.backup_manager.default_config.copy()
            self.backup_manager.save_config()
            self.load_settings()
            messagebox.showinfo("نجح", "تم استعادة الإعدادات الافتراضية")

    def close_dialog(self):
        """إغلاق النافذة"""
        # إيقاف النسخ التلقائي إذا كان معطلاً
        if not self.backup_manager.config["auto_backup_enabled"]:
            self.backup_manager.stop_auto_backup_service()

        self.dialog.destroy()

    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
