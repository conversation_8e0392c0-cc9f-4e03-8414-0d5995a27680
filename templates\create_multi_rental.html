{% extends "base.html" %}

{% block title %}إنشاء إيجار متعدد{% endblock %}

{% block extra_css %}
<style>
    .equipment-card {
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 1rem;
    }
    
    .equipment-card:hover {
        border-color: #0d6efd;
        box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.15);
    }
    
    .equipment-card.selected {
        border-color: #198754;
        background-color: #d1edff;
    }
    
    .equipment-card.selected .card-header {
        background-color: #198754;
        color: white;
    }
    
    .category-section {
        margin-bottom: 2rem;
    }
    
    .category-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .selected-equipment {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .price-input {
        width: 120px;
    }
    
    .days-input {
        width: 80px;
    }
    
    .total-section {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }
    
    .step {
        display: flex;
        align-items: center;
        margin: 0 1rem;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #6c757d;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
    }
    
    .step.active .step-number {
        background-color: #0d6efd;
    }
    
    .step.completed .step-number {
        background-color: #198754;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-plus-circle me-2 text-primary"></i>إنشاء إيجار متعدد
                    </h2>
                    <p class="text-muted mb-0">اختر عدة معدات لعميل واحد في إيجار واحد</p>
                </div>
                <div>
                    <a href="{{ url_for('rentals') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للإيجارات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="step active" id="step1">
            <div class="step-number">1</div>
            <span>اختيار العميل</span>
        </div>
        <div class="step" id="step2">
            <div class="step-number">2</div>
            <span>اختيار المعدات</span>
        </div>
        <div class="step" id="step3">
            <div class="step-number">3</div>
            <span>تحديد التفاصيل</span>
        </div>
        <div class="step" id="step4">
            <div class="step-number">4</div>
            <span>المراجعة والحفظ</span>
        </div>
    </div>

    <!-- Step 1: Customer Selection -->
    <div class="step-content" id="step1-content">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>اختيار العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="customerSelect" class="form-label">العميل</label>
                            <select class="form-select" id="customerSelect" required>
                                <option value="">اختر العميل...</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}" data-name="{{ customer.name }}" data-phone="{{ customer.phone }}">
                                    {{ customer.name }} - {{ customer.phone }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="rentalDate" class="form-label">تاريخ الإيجار</label>
                            <input type="date" class="form-control" id="rentalDate" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="returnDate" class="form-label">تاريخ الإرجاع</label>
                            <input type="date" class="form-control" id="returnDate" required>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                                التالي <i class="fas fa-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 2: Equipment Selection -->
    <div class="step-content d-none" id="step2-content">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-camera me-2"></i>اختيار المعدات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="equipmentCategories">
                            <!-- سيتم تحميل المعدات هنا -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>المعدات المختارة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="selectedEquipmentList">
                            <p class="text-muted text-center">لم يتم اختيار معدات بعد</p>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-3">
                    <button type="button" class="btn btn-outline-secondary" onclick="previousStep(1)">
                        <i class="fas fa-arrow-right me-2"></i>السابق
                    </button>
                    <button type="button" class="btn btn-primary" onclick="nextStep(3)" id="nextToStep3" disabled>
                        التالي <i class="fas fa-arrow-left ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 3: Details -->
    <div class="step-content d-none" id="step3-content">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>تحديد تفاصيل الإيجار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="equipmentDetails">
                            <!-- سيتم عرض تفاصيل المعدات هنا -->
                        </div>
                        
                        <div class="mb-3">
                            <label for="rentalNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="rentalNotes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="previousStep(2)">
                                <i class="fas fa-arrow-right me-2"></i>السابق
                            </button>
                            <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                                التالي <i class="fas fa-arrow-left ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step 4: Review and Save -->
    <div class="step-content d-none" id="step4-content">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check me-2"></i>مراجعة الإيجار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="rentalSummary">
                            <!-- سيتم عرض ملخص الإيجار هنا -->
                        </div>
                        
                        <div class="total-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>إجمالي التكلفة</h4>
                                    <p class="mb-0">شامل جميع المعدات والأيام</p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <h2 id="totalCost">0.00 ج.م</h2>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-outline-secondary" onclick="previousStep(3)">
                                <i class="fas fa-arrow-right me-2"></i>السابق
                            </button>
                            <button type="button" class="btn btn-success btn-lg" onclick="saveRental()">
                                <i class="fas fa-save me-2"></i>حفظ الإيجار
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentStep = 1;
    let selectedEquipment = [];
    let availableEquipment = [];
    let selectedCustomer = null;
    let rentalDates = {};

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين التاريخ الافتراضي
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('rentalDate').value = today;
        
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        document.getElementById('returnDate').value = tomorrow.toISOString().split('T')[0];
    });

    // الانتقال للخطوة التالية
    function nextStep(step) {
        if (step === 2) {
            if (!validateStep1()) return;
            loadAvailableEquipment();
        } else if (step === 3) {
            if (selectedEquipment.length === 0) {
                showAlert('warning', 'يرجى اختيار معدة واحدة على الأقل');
                return;
            }
            showEquipmentDetails();
        } else if (step === 4) {
            showRentalSummary();
        }
        
        // إخفاء الخطوة الحالية
        document.getElementById(`step${currentStep}-content`).classList.add('d-none');
        document.getElementById(`step${currentStep}`).classList.remove('active');
        document.getElementById(`step${currentStep}`).classList.add('completed');
        
        // إظهار الخطوة الجديدة
        currentStep = step;
        document.getElementById(`step${currentStep}-content`).classList.remove('d-none');
        document.getElementById(`step${currentStep}`).classList.add('active');
    }

    // العودة للخطوة السابقة
    function previousStep(step) {
        // إخفاء الخطوة الحالية
        document.getElementById(`step${currentStep}-content`).classList.add('d-none');
        document.getElementById(`step${currentStep}`).classList.remove('active');
        
        // إظهار الخطوة السابقة
        currentStep = step;
        document.getElementById(`step${currentStep}-content`).classList.remove('d-none');
        document.getElementById(`step${currentStep}`).classList.add('active');
        document.getElementById(`step${currentStep}`).classList.remove('completed');
    }

    // التحقق من صحة الخطوة الأولى
    function validateStep1() {
        const customerSelect = document.getElementById('customerSelect');
        const rentalDate = document.getElementById('rentalDate');
        const returnDate = document.getElementById('returnDate');
        
        if (!customerSelect.value) {
            showAlert('warning', 'يرجى اختيار العميل');
            return false;
        }
        
        if (!rentalDate.value || !returnDate.value) {
            showAlert('warning', 'يرجى تحديد تاريخ الإيجار والإرجاع');
            return false;
        }
        
        if (new Date(returnDate.value) <= new Date(rentalDate.value)) {
            showAlert('warning', 'تاريخ الإرجاع يجب أن يكون بعد تاريخ الإيجار');
            return false;
        }
        
        // حفظ البيانات
        const selectedOption = customerSelect.options[customerSelect.selectedIndex];
        selectedCustomer = {
            id: customerSelect.value,
            name: selectedOption.dataset.name,
            phone: selectedOption.dataset.phone
        };
        
        rentalDates = {
            rental_date: rentalDate.value,
            return_date: returnDate.value
        };
        
        return true;
    }

    // تحميل المعدات المتاحة
    async function loadAvailableEquipment() {
        try {
            const response = await fetch('/api/equipment/available', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(rentalDates)
            });
            
            const result = await response.json();
            
            if (result.success) {
                availableEquipment = result.equipment;
                displayEquipmentByCategory(result.equipment_by_category);
            } else {
                showAlert('danger', result.error);
            }
        } catch (error) {
            console.error('Error loading equipment:', error);
            showAlert('danger', 'حدث خطأ في تحميل المعدات');
        }
    }

    // عرض المعدات مجمعة حسب الفئة
    function displayEquipmentByCategory(equipmentByCategory) {
        const container = document.getElementById('equipmentCategories');
        container.innerHTML = '';
        
        Object.keys(equipmentByCategory).forEach(category => {
            const equipment = equipmentByCategory[category];
            
            const categoryHtml = `
                <div class="category-section">
                    <div class="category-header">
                        <h5 class="mb-0">
                            <i class="fas fa-layer-group me-2"></i>${category}
                            <span class="badge bg-light text-dark ms-2">${equipment.length}</span>
                        </h5>
                    </div>
                    <div class="row">
                        ${equipment.map(eq => `
                            <div class="col-md-6 col-lg-4">
                                <div class="equipment-card" onclick="toggleEquipment(${eq.id})">
                                    <div class="card-header">
                                        <h6 class="mb-0">${eq.name}</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-1"><strong>الموديل:</strong> ${eq.model}</p>
                                        <p class="mb-1"><strong>الرقم التسلسلي:</strong> ${eq.serial_number}</p>
                                        <p class="mb-0"><span class="badge bg-success">متاح</span></p>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            container.innerHTML += categoryHtml;
        });
    }

    // تبديل اختيار المعدة
    function toggleEquipment(equipmentId) {
        const equipment = availableEquipment.find(eq => eq.id === equipmentId);
        const card = document.querySelector(`[onclick="toggleEquipment(${equipmentId})"]`);
        
        const existingIndex = selectedEquipment.findIndex(eq => eq.id === equipmentId);
        
        if (existingIndex > -1) {
            // إلغاء الاختيار
            selectedEquipment.splice(existingIndex, 1);
            card.classList.remove('selected');
        } else {
            // إضافة للاختيار
            selectedEquipment.push({
                ...equipment,
                rental_price: 0,
                days_count: 1
            });
            card.classList.add('selected');
        }
        
        updateSelectedEquipmentList();
        updateNextButton();
    }

    // تحديث قائمة المعدات المختارة
    function updateSelectedEquipmentList() {
        const container = document.getElementById('selectedEquipmentList');
        
        if (selectedEquipment.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">لم يتم اختيار معدات بعد</p>';
            return;
        }
        
        container.innerHTML = selectedEquipment.map(eq => `
            <div class="selected-equipment">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${eq.name}</h6>
                        <small class="text-muted">${eq.model}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="removeEquipment(${eq.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    // إزالة معدة من الاختيار
    function removeEquipment(equipmentId) {
        toggleEquipment(equipmentId);
    }

    // تحديث زر التالي
    function updateNextButton() {
        const nextBtn = document.getElementById('nextToStep3');
        nextBtn.disabled = selectedEquipment.length === 0;
    }

    // عرض تفاصيل المعدات
    function showEquipmentDetails() {
        const container = document.getElementById('equipmentDetails');
        
        container.innerHTML = selectedEquipment.map((eq, index) => `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">${eq.name} - ${eq.model}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">سعر الإيجار اليومي (ج.م)</label>
                            <input type="number" class="form-control price-input" 
                                   value="${eq.rental_price}" 
                                   onchange="updateEquipmentPrice(${index}, this.value)"
                                   min="0" step="0.01">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">عدد الأيام</label>
                            <input type="number" class="form-control days-input" 
                                   value="${eq.days_count}" 
                                   onchange="updateEquipmentDays(${index}, this.value)"
                                   min="1">
                        </div>
                    </div>
                    <div class="mt-2">
                        <strong>المجموع الفرعي: <span id="subtotal-${index}">${(eq.rental_price * eq.days_count).toFixed(2)}</span> ج.م</strong>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // تحديث سعر المعدة
    function updateEquipmentPrice(index, price) {
        selectedEquipment[index].rental_price = parseFloat(price) || 0;
        updateSubtotal(index);
    }

    // تحديث عدد الأيام
    function updateEquipmentDays(index, days) {
        selectedEquipment[index].days_count = parseInt(days) || 1;
        updateSubtotal(index);
    }

    // تحديث المجموع الفرعي
    function updateSubtotal(index) {
        const eq = selectedEquipment[index];
        const subtotal = eq.rental_price * eq.days_count;
        document.getElementById(`subtotal-${index}`).textContent = subtotal.toFixed(2);
    }

    // عرض ملخص الإيجار
    function showRentalSummary() {
        const container = document.getElementById('rentalSummary');
        
        let totalCost = 0;
        selectedEquipment.forEach(eq => {
            totalCost += eq.rental_price * eq.days_count;
        });
        
        container.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>معلومات العميل</h5>
                    <p><strong>الاسم:</strong> ${selectedCustomer.name}</p>
                    <p><strong>الهاتف:</strong> ${selectedCustomer.phone}</p>
                </div>
                <div class="col-md-6">
                    <h5>تواريخ الإيجار</h5>
                    <p><strong>تاريخ الإيجار:</strong> ${rentalDates.rental_date}</p>
                    <p><strong>تاريخ الإرجاع:</strong> ${rentalDates.return_date}</p>
                </div>
            </div>
            
            <h5>المعدات المختارة</h5>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>المعدة</th>
                            <th>السعر اليومي</th>
                            <th>عدد الأيام</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${selectedEquipment.map(eq => `
                            <tr>
                                <td>${eq.name} - ${eq.model}</td>
                                <td>${eq.rental_price.toFixed(2)} ج.م</td>
                                <td>${eq.days_count}</td>
                                <td>${(eq.rental_price * eq.days_count).toFixed(2)} ج.م</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        document.getElementById('totalCost').textContent = `${totalCost.toFixed(2)} ج.م`;
    }

    // حفظ الإيجار
    async function saveRental() {
        try {
            const rentalData = {
                customer_id: parseInt(selectedCustomer.id),
                rental_date: rentalDates.rental_date,
                return_date: rentalDates.return_date,
                equipment_list: selectedEquipment.map(eq => ({
                    equipment_id: eq.id,
                    rental_price: eq.rental_price,
                    days_count: eq.days_count
                })),
                notes: document.getElementById('rentalNotes').value
            };
            
            const response = await fetch('/api/multi-rentals', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(rentalData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
                setTimeout(() => {
                    window.location.href = '/rentals';
                }, 2000);
            } else {
                showAlert('danger', result.error);
            }
        } catch (error) {
            console.error('Error saving rental:', error);
            showAlert('danger', 'حدث خطأ في حفظ الإيجار');
        }
    }
</script>
{% endblock %}
