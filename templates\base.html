<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة تأجير المعدات{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 600;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 10px;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .stats-card .card-body {
            padding: 2rem;
        }
        
        .stats-card h3 {
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .online-indicator {
            width: 10px;
            height: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
            margin-left: 5px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.user_id %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-camera me-2"></i>
                نظام إدارة تأجير المعدات
                <span class="online-indicator"></span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- القوائم الرئيسية -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('backup_page') }}">
                            <i class="fas fa-shield-alt"></i>
                            <span>النسخ الاحتياطي</span>
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="equipmentDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-camera me-1"></i>
                            المعدات والمخزون
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="equipmentDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('equipment') }}">
                                <i class="fas fa-camera me-2"></i>المعدات الرئيسية
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory') }}">
                                <i class="fas fa-boxes me-2"></i>المخزون والإكسسوارات
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('inventory_items') }}">
                                <i class="fas fa-list me-2"></i>عرض جميع عناصر المخزون
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_inventory_item') }}">
                                <i class="fas fa-plus me-2"></i>إضافة عنصر جديد
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('customers') }}">
                            <i class="fas fa-users me-1"></i>
                            العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('rentals') }}">
                            <i class="fas fa-clipboard-list me-1"></i>
                            الإيجارات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('create_multi_rental') }}">
                            <i class="fas fa-layer-group me-1"></i>
                            إيجار متعدد
                        </a>
                    </li>
                    {% if session.role == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('offices') }}">
                            <i class="fas fa-building me-1"></i>
                            المكاتب والموردين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_users') }}">
                            <i class="fas fa-users-cog me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('company_settings_page') }}">
                            <i class="fas fa-building me-1"></i>
                            إعدادات الشركة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('permissions_management') }}">
                            <i class="fas fa-user-shield me-1"></i>
                            إدارة الصلاحيات
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ session.full_name }}
                            <span class="badge bg-secondary ms-1">{{ get_role_display_name(session.role) }}</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="printEquipmentList()">
                                <i class="fas fa-print me-2"></i>طباعة قائمة المعدات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <script>
        // إعداد Socket.IO للتحديثات المباشرة
        {% if session.user_id %}
        const socket = io();
        
        socket.on('connect', function() {
            console.log('متصل بالخادم');
        });
        
        socket.on('equipment_updated', function(data) {
            // تحديث قائمة المعدات إذا كانت مفتوحة
            if (window.location.pathname === '/equipment') {
                location.reload();
            }
        });
        
        socket.on('customers_updated', function(data) {
            // تحديث قائمة العملاء إذا كانت مفتوحة
            if (window.location.pathname === '/customers') {
                location.reload();
            }
        });
        
        socket.on('rentals_updated', function(data) {
            // تحديث قائمة الإيجارات إذا كانت مفتوحة
            if (window.location.pathname === '/rentals' || window.location.pathname === '/dashboard') {
                location.reload();
            }
        });
        {% endif %}
        
        // دالة طباعة قائمة المعدات
        function printEquipmentList() {
            fetch('/api/print/equipment')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // فتح رابط التحميل
                        window.open(data.download_url, '_blank');
                        
                        // إظهار رسالة نجاح
                        showAlert('success', data.message);
                    } else {
                        showAlert('danger', data.error);
                    }
                })
                .catch(error => {
                    showAlert('danger', 'حدث خطأ في طباعة قائمة المعدات');
                });
        }
        
        // دالة إظهار التنبيهات
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // إدراج التنبيه في أعلى الصفحة
            const container = document.querySelector('.container-fluid') || document.body;
            container.insertBefore(alertDiv, container.firstChild);
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // تحديث الوقت كل دقيقة
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-EG');
            const timeElements = document.querySelectorAll('.current-time');
            timeElements.forEach(el => el.textContent = timeString);
        }
        
        setInterval(updateTime, 60000);
        updateTime();
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
