#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إطار قابل للطي والتوسيع لتحسين واجهة المستخدم
Collapsible Frame widget for improved user interface
"""

import tkinter as tk
from tkinter import ttk
from tkinter import font

class CollapsibleFrame:
    """إطار قابل للطي والتوسيع مع دعم اللغة العربية"""
    
    def __init__(self, parent, title: str, expanded: bool = False):
        """
        إنشاء إطار قابل للطي
        
        Args:
            parent: الإطار الأب
            title: عنوان القسم
            expanded: هل القسم مفتوح افتراضياً
        """
        self.parent = parent
        self.title = title
        self.expanded = expanded
        
        # إنشاء الإطار الرئيسي
        self.main_frame = ttk.Frame(parent)
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=11, weight="bold")
            self.content_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=11, weight="bold")
            self.content_font = font.Font(family="Arial", size=10)
        
        # إنشاء رأس القسم
        self.create_header()
        
        # إنشاء منطقة المحتوى
        self.create_content_area()
        
        # تطبيق الحالة الافتراضية
        self.update_display()
    
    def create_header(self):
        """إنشاء رأس القسم مع زر الطي/التوسيع"""
        self.header_frame = ttk.Frame(self.main_frame, style="Header.TFrame")
        self.header_frame.pack(fill=tk.X, padx=2, pady=2)
        
        # زر الطي/التوسيع
        self.toggle_button = ttk.Button(
            self.header_frame,
            text=self.get_toggle_text(),
            command=self.toggle,
            width=3
        )
        self.toggle_button.pack(side=tk.LEFT, padx=(5, 10))
        
        # عنوان القسم
        self.title_label = ttk.Label(
            self.header_frame,
            text=self.title,
            font=self.arabic_font
        )
        self.title_label.pack(side=tk.LEFT, padx=5)
        
        # خط فاصل
        separator = ttk.Separator(self.header_frame, orient=tk.HORIZONTAL)
        separator.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 5))
    
    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        self.content_frame = ttk.Frame(self.main_frame)
        # سيتم إضافة المحتوى لاحقاً
    
    def get_toggle_text(self) -> str:
        """الحصول على نص زر الطي/التوسيع"""
        return "▼" if self.expanded else "▶"
    
    def toggle(self):
        """تبديل حالة الطي/التوسيع"""
        self.expanded = not self.expanded
        self.update_display()
    
    def update_display(self):
        """تحديث عرض القسم حسب الحالة"""
        self.toggle_button.config(text=self.get_toggle_text())
        
        if self.expanded:
            self.content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        else:
            self.content_frame.pack_forget()
    
    def expand(self):
        """توسيع القسم"""
        if not self.expanded:
            self.toggle()
    
    def collapse(self):
        """طي القسم"""
        if self.expanded:
            self.toggle()
    
    def pack(self, **kwargs):
        """تطبيق pack على الإطار الرئيسي"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """تطبيق grid على الإطار الرئيسي"""
        self.main_frame.grid(**kwargs)
    
    def get_content_frame(self) -> ttk.Frame:
        """الحصول على إطار المحتوى لإضافة العناصر"""
        return self.content_frame

class CollapsibleNotebook:
    """دفتر ملاحظات مع أقسام قابلة للطي"""
    
    def __init__(self, parent):
        """إنشاء دفتر ملاحظات قابل للطي"""
        self.parent = parent
        self.sections = {}
        self.current_section = None
        
        # إنشاء الإطار الرئيسي
        self.main_frame = ttk.Frame(parent)
        
        # إنشاء منطقة التمرير
        self.create_scrollable_area()
    
    def create_scrollable_area(self):
        """إنشاء منطقة قابلة للتمرير"""
        # إنشاء Canvas للتمرير
        self.canvas = tk.Canvas(self.main_frame, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.main_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        # ربط التمرير
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # تخطيط العناصر
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط عجلة الماوس بالتمرير
        self.bind_mousewheel()
    
    def bind_mousewheel(self):
        """ربط عجلة الماوس بالتمرير"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        self.canvas.bind("<MouseWheel>", _on_mousewheel)
    
    def add_section(self, section_id: str, title: str, expanded: bool = False) -> CollapsibleFrame:
        """إضافة قسم جديد"""
        section = CollapsibleFrame(self.scrollable_frame, title, expanded)
        section.pack(fill=tk.X, padx=5, pady=2)
        
        # إضافة معالج للنقر على العنوان لإغلاق الأقسام الأخرى
        section.title_label.bind("<Button-1>", lambda e, sid=section_id: self.select_section(sid))
        
        self.sections[section_id] = section
        
        if expanded:
            self.current_section = section_id
        
        return section
    
    def select_section(self, section_id: str):
        """اختيار قسم وإغلاق الأقسام الأخرى"""
        if section_id not in self.sections:
            return
        
        # إغلاق جميع الأقسام الأخرى
        for sid, section in self.sections.items():
            if sid != section_id:
                section.collapse()
        
        # فتح القسم المحدد
        self.sections[section_id].expand()
        self.current_section = section_id
        
        # التمرير إلى القسم المحدد
        self.scroll_to_section(section_id)
    
    def scroll_to_section(self, section_id: str):
        """التمرير إلى قسم محدد"""
        if section_id in self.sections:
            section = self.sections[section_id]
            # تحديث منطقة التمرير
            self.canvas.update_idletasks()
            
            # حساب موقع القسم
            section_y = section.main_frame.winfo_y()
            canvas_height = self.canvas.winfo_height()
            scroll_region = self.canvas.bbox("all")
            
            if scroll_region:
                total_height = scroll_region[3]
                if total_height > canvas_height:
                    # حساب نسبة التمرير
                    scroll_fraction = section_y / total_height
                    self.canvas.yview_moveto(scroll_fraction)
    
    def get_section(self, section_id: str) -> CollapsibleFrame:
        """الحصول على قسم محدد"""
        return self.sections.get(section_id)
    
    def pack(self, **kwargs):
        """تطبيق pack على الإطار الرئيسي"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """تطبيق grid على الإطار الرئيسي"""
        self.main_frame.grid(**kwargs)
