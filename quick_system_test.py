#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from datetime import datetime

def quick_system_test():
    """اختبار سريع لحالة النظام"""
    base_url = "http://127.0.0.1:5000"
    
    print("🚀 اختبار سريع لحالة النظام")
    print("=" * 50)
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    try:
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تسجيل الدخول نجح")
        else:
            print(f"❌ فشل تسجيل الدخول")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return
    
    # اختبار الصفحات الأساسية
    pages = [
        ("/equipment", "المعدات"),
        ("/customers", "العملاء"),
        ("/offices", "المكاتب"),
        ("/new_rental", "إنشاء إيجار"),
    ]
    
    results = {}
    
    for url, name in pages:
        try:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                content = response.text
                
                # عد الصفوف
                row_count = 0
                if 'equipment' in url:
                    row_count = content.count('<tr data-category=')
                elif 'customers' in url:
                    row_count = content.count('<tr data-customer=')
                elif 'offices' in url:
                    row_count = content.count('<tr data-office=')
                
                has_data = any(keyword in content for keyword in ['كاميرا', 'أحمد', 'محمد', 'استوديو'])
                
                results[name] = {
                    'status': 'success',
                    'size': len(content),
                    'rows': row_count,
                    'has_data': has_data
                }
                
                status_icon = "✅" if row_count > 0 or has_data else "⚠️"
                print(f"{status_icon} {name}: {len(content)} حرف - {row_count} صف")
            else:
                results[name] = {'status': 'error', 'code': response.status_code}
                print(f"❌ {name}: خطأ {response.status_code}")
        except Exception as e:
            results[name] = {'status': 'exception'}
            print(f"❌ {name}: خطأ - {e}")
    
    # اختبار APIs
    apis = [
        ("/api/equipment/all", "API المعدات"),
        ("/api/customers", "API العملاء"),
        ("/api/external-equipment/available", "API المعدات الخارجية"),
    ]
    
    for url, name in apis:
        try:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                data = response.json()
                count = 0
                
                if isinstance(data, dict):
                    if 'equipment' in data:
                        count = len(data['equipment'])
                    elif 'customers' in data:
                        count = len(data['customers'])
                    elif 'count' in data:
                        count = data['count']
                elif isinstance(data, list):
                    count = len(data)
                
                results[name] = {'status': 'success', 'count': count}
                print(f"✅ {name}: {count} عنصر")
            else:
                results[name] = {'status': 'error'}
                print(f"❌ {name}: خطأ {response.status_code}")
        except Exception as e:
            results[name] = {'status': 'exception'}
            print(f"❌ {name}: خطأ - {e}")
    
    # ملخص النتائج
    total = len(results)
    successful = sum(1 for r in results.values() if r.get('status') == 'success')
    success_rate = (successful / total) * 100 if total > 0 else 0
    
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج")
    print("=" * 50)
    print(f"📈 إجمالي الاختبارات: {total}")
    print(f"✅ الناجحة: {successful}")
    print(f"❌ الفاشلة: {total - successful}")
    print(f"🎯 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        status = "🌟 ممتاز"
    elif success_rate >= 80:
        status = "✅ جيد جداً"
    elif success_rate >= 70:
        status = "⚠️ جيد"
    else:
        status = "❌ يحتاج تحسين"
    
    print(f"🏆 التقييم: {status}")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results

if __name__ == "__main__":
    quick_system_test()
