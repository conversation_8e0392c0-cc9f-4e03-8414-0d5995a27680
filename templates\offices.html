{% extends "base.html" %}

{% block title %}المكاتب والموردين - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-building me-2 text-primary"></i>
                        المكاتب والموردين
                    </h1>
                    <p class="text-muted mb-0">إدارة المكاتب والموردين والمعدات الخارجية</p>
                </div>
                <div>
                    <button class="btn btn-success" onclick="addOffice()">
                        <i class="fas fa-plus me-2"></i>إضافة مكتب جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">إجمالي المكاتب</h6>
                            <h3 class="mb-0">{{ offices|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-building fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">المكاتب النشطة</h6>
                            <h3 class="mb-0">{{ offices|selectattr('status', 'equalto', 'Active')|list|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الموردين</h6>
                            <h3 class="mb-0">{{ offices|selectattr('office_type', 'equalto', 'supplier')|list|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الشركاء</h6>
                            <h3 class="mb-0">{{ offices|selectattr('office_type', 'equalto', 'partner')|list|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-handshake fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Offices Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>قائمة المكاتب والموردين
                    </h5>
                </div>
                <div class="card-body">
                    {% if error %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ error }}
                    </div>
                    {% endif %}

                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم المكتب</th>
                                    <th>العنوان</th>
                                    <th>الهاتف</th>
                                    <th>الشخص المسؤول</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>المعدات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if offices %}
                                    {% for office in offices %}
                                    <tr>
                                        <td>{{ office.id }}</td>
                                        <td>
                                            <strong>{{ office.name }}</strong>
                                            {% if office.email %}
                                            <br><small class="text-muted">{{ office.email }}</small>
                                            {% endif %}
                                        </td>
                                        <td>{{ office.address }}</td>
                                        <td>{{ office.phone }}</td>
                                        <td>{{ office.contact_person }}</td>
                                        <td>
                                            {% if office.office_type == 'supplier' %}
                                            <span class="badge bg-info">مورد</span>
                                            {% elif office.office_type == 'partner' %}
                                            <span class="badge bg-warning">شريك</span>
                                            {% elif office.office_type == 'branch' %}
                                            <span class="badge bg-secondary">فرع</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if office.status == 'Active' %}
                                            <span class="badge bg-success">نشط</span>
                                            {% else %}
                                            <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ office.equipment_count }}</span>
                                            {% if office.active_equipment > 0 %}
                                            <span class="badge bg-success">{{ office.active_equipment }} نشط</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="viewOffice({{ office.id }})" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="editOffice({{ office.id }})" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-info" onclick="viewOfficeEquipment({{ office.id }})" title="المعدات">
                                                    <i class="fas fa-camera"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteOffice({{ office.id }})" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted py-4">
                                            <i class="fas fa-building fa-3x mb-3 d-block"></i>
                                            لا توجد مكاتب مسجلة
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Office Equipment Modal -->
<div class="modal fade" id="officeEquipmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-camera me-2"></i>معدات المكتب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="officeEquipmentContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">جاري تحميل المعدات...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// View office details
function viewOffice(officeId) {
    // Implementation for viewing office details
    alert('عرض تفاصيل المكتب: ' + officeId);
}

// Edit office
function editOffice(officeId) {
    // Implementation for editing office
    alert('تعديل المكتب: ' + officeId);
}

// Add new office
function addOffice() {
    // Implementation for adding new office
    alert('إضافة مكتب جديد');
}

// Delete office
function deleteOffice(officeId) {
    if (confirm('هل أنت متأكد من حذف هذا المكتب؟')) {
        // Implementation for deleting office
        alert('حذف المكتب: ' + officeId);
    }
}

// View office equipment
function viewOfficeEquipment(officeId) {
    const modal = new bootstrap.Modal(document.getElementById('officeEquipmentModal'));
    modal.show();
    
    // Load office equipment
    fetch(`/api/offices/${officeId}/equipment`)
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('officeEquipmentContent');
            
            if (data.equipment && data.equipment.length > 0) {
                let html = '<div class="table-responsive"><table class="table table-sm">';
                html += '<thead><tr><th>اسم المعدة</th><th>الفئة</th><th>الحالة</th><th>السعر اليومي</th></tr></thead><tbody>';
                
                data.equipment.forEach(eq => {
                    const statusBadge = eq.status === 'Active' ? 
                        '<span class="badge bg-success">نشط</span>' : 
                        '<span class="badge bg-secondary">غير نشط</span>';
                    
                    html += `
                        <tr>
                            <td><strong>${eq.equipment_name}</strong></td>
                            <td><span class="badge bg-info">${eq.equipment_category}</span></td>
                            <td>${statusBadge}</td>
                            <td>${eq.customer_daily_price} ج.م</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                content.innerHTML = html;
            } else {
                content.innerHTML = '<div class="text-center text-muted"><i class="fas fa-camera fa-3x mb-3 d-block"></i>لا توجد معدات لهذا المكتب</div>';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل معدات المكتب:', error);
            document.getElementById('officeEquipmentContent').innerHTML = 
                '<div class="alert alert-danger">فشل في تحميل معدات المكتب</div>';
        });
}

// Show alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
