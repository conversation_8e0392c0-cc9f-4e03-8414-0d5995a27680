#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re

def debug_office_template():
    """فحص قالب تفاصيل المكتب"""
    base_url = "http://127.0.0.1:5000"
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة تفاصيل المكتب 3
        office_id = 3
        print(f"🔍 فحص قالب تفاصيل المكتب {office_id}...")
        response = session.get(f"{base_url}/offices/{office_id}")
        
        if response.status_code == 200:
            content = response.text
            print("✅ صفحة تفاصيل المكتب تعمل")
            
            # فحص عدد المعدات في الإحصائيات
            equipment_count_match = re.search(r'<h3 class="text-primary mb-1">(\d+)</h3>', content)
            if equipment_count_match:
                equipment_count = equipment_count_match.group(1)
                print(f"📊 عدد المعدات في الإحصائيات: {equipment_count}")
            else:
                print("❌ لم يتم العثور على عدد المعدات في الإحصائيات")
            
            # فحص badge عدد المعدات
            badge_match = re.search(r'<span class="badge bg-primary ms-2">(\d+)</span>', content)
            if badge_match:
                badge_count = badge_match.group(1)
                print(f"📊 عدد المعدات في badge: {badge_count}")
            else:
                print("❌ لم يتم العثور على badge عدد المعدات")
            
            # فحص وجود جدول المعدات
            if '<table class="table table-hover table-striped">' in content:
                print("✅ يحتوي على جدول المعدات")
                
                # فحص tbody
                tbody_start = content.find('<tbody>')
                tbody_end = content.find('</tbody>')
                if tbody_start != -1 and tbody_end != -1:
                    tbody_content = content[tbody_start:tbody_end+8]
                    print("✅ يحتوي على tbody")
                    
                    # عد الصفوف
                    row_count = tbody_content.count('<tr>')
                    print(f"📊 عدد الصفوف في tbody: {row_count}")
                    
                    if row_count > 0:
                        print("✅ يحتوي على صفوف")
                        # عرض محتوى tbody
                        print(f"📋 محتوى tbody:")
                        print(tbody_content[:500] + "..." if len(tbody_content) > 500 else tbody_content)
                    else:
                        print("❌ لا يحتوي على صفوف")
                        print(f"📋 محتوى tbody فارغ:")
                        print(tbody_content)
                else:
                    print("❌ لا يحتوي على tbody")
            else:
                print("❌ لا يحتوي على جدول المعدات")
            
            # فحص رسالة "لا توجد معدات"
            if 'لا توجد معدات لهذا المكتب' in content:
                print("⚠️ يعرض رسالة 'لا توجد معدات'")
            else:
                print("✅ لا يعرض رسالة 'لا توجد معدات'")
            
            # فحص اسم المعدة في المحتوى
            if 'Sony A7R V' in content:
                print("✅ يحتوي على اسم المعدة في المحتوى")
            else:
                print("❌ لا يحتوي على اسم المعدة في المحتوى")
                
        else:
            print(f"❌ خطأ في صفحة تفاصيل المكتب: {response.status_code}")
            
    else:
        print("❌ فشل تسجيل الدخول")

if __name__ == "__main__":
    debug_office_template()
