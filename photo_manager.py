#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الصور - إدارة رفع وحفظ صور العملاء
Photo Manager - Handle customer photo uploads and storage
"""

import os
import shutil
from datetime import datetime
from typing import Optional, Tuple
from PIL import Image
import uuid

class PhotoManager:
    """مدير الصور لإدارة صور العملاء وبطاقات الهوية"""
    
    def __init__(self, base_path: str = None):
        """تهيئة مدير الصور"""
        if base_path is None:
            # تحديد مسار الصور في مجلد static/images
            current_dir = os.path.dirname(os.path.abspath(__file__))
            system_dir = os.path.dirname(current_dir)
            base_path = os.path.join(system_dir, "static", "images", "customer_photos")
        self.base_path = base_path
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        self.max_file_size = 5 * 1024 * 1024  # 5 MB
        self.max_image_size = (1920, 1080)  # أقصى حجم للصورة
        
        # إنشاء المجلدات المطلوبة
        self._create_directories()
    
    def _create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            self.base_path,
            os.path.join(self.base_path, "profile_photos"),
            os.path.join(self.base_path, "id_photos"),
            os.path.join(self.base_path, "thumbnails")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def validate_image(self, file_path: str) -> Tuple[bool, str]:
        """التحقق من صحة الصورة"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                return False, "الملف غير موجود"
            
            # التحقق من حجم الملف
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return False, f"حجم الملف كبير جداً (أقصى حجم: {self.max_file_size // (1024*1024)} MB)"
            
            # التحقق من امتداد الملف
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return False, f"صيغة الملف غير مدعومة. الصيغ المدعومة: {', '.join(self.supported_formats)}"
            
            # التحقق من صحة الصورة
            with Image.open(file_path) as img:
                img.verify()
            
            return True, "الصورة صحيحة"
            
        except Exception as e:
            return False, f"خطأ في التحقق من الصورة: {str(e)}"
    
    def resize_image(self, input_path: str, output_path: str, max_size: Tuple[int, int] = None) -> bool:
        """تغيير حجم الصورة"""
        try:
            if max_size is None:
                max_size = self.max_image_size
            
            with Image.open(input_path) as img:
                # تحويل إلى RGB إذا كانت الصورة RGBA
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # تغيير الحجم مع الحفاظ على النسبة
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # حفظ الصورة
                img.save(output_path, 'JPEG', quality=85, optimize=True)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تغيير حجم الصورة: {e}")
            return False
    
    def create_thumbnail(self, input_path: str, output_path: str, size: Tuple[int, int] = (150, 150)) -> bool:
        """إنشاء صورة مصغرة"""
        try:
            with Image.open(input_path) as img:
                # تحويل إلى RGB إذا كانت الصورة RGBA
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # إنشاء صورة مصغرة
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # حفظ الصورة المصغرة
                img.save(output_path, 'JPEG', quality=80, optimize=True)
            
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء الصورة المصغرة: {e}")
            return False
    
    def save_customer_photo(self, source_path: str, customer_id: int, photo_type: str = "profile") -> Tuple[bool, str, Optional[str]]:
        """
        حفظ صورة العميل
        photo_type: "profile" للصورة الشخصية أو "id" لصورة بطاقة الهوية
        """
        try:
            # التحقق من صحة الصورة
            is_valid, message = self.validate_image(source_path)
            if not is_valid:
                return False, message, None
            
            # إنشاء اسم ملف فريد
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            file_ext = ".jpg"  # نحفظ جميع الصور بصيغة JPG
            
            filename = f"customer_{customer_id}_{photo_type}_{timestamp}_{unique_id}{file_ext}"
            
            # تحديد المجلد المناسب
            if photo_type == "profile":
                subfolder = "profile_photos"
            elif photo_type == "id":
                subfolder = "id_photos"
            else:
                return False, "نوع الصورة غير صحيح", None
            
            # مسارات الحفظ
            output_dir = os.path.join(self.base_path, subfolder)
            output_path = os.path.join(output_dir, filename)
            thumbnail_path = os.path.join(self.base_path, "thumbnails", f"thumb_{filename}")
            
            # تغيير حجم الصورة وحفظها
            if not self.resize_image(source_path, output_path):
                return False, "فشل في معالجة الصورة", None
            
            # إنشاء صورة مصغرة
            self.create_thumbnail(output_path, thumbnail_path)
            
            # إرجاع المسار النسبي
            relative_path = os.path.join(subfolder, filename).replace("\\", "/")
            
            return True, "تم حفظ الصورة بنجاح", relative_path
            
        except Exception as e:
            return False, f"خطأ في حفظ الصورة: {str(e)}", None
    
    def delete_photo(self, photo_path: str) -> bool:
        """حذف صورة"""
        try:
            if not photo_path:
                return True  # لا توجد صورة للحذف
            
            # المسار الكامل للصورة
            full_path = os.path.join(self.base_path, photo_path)
            
            # حذف الصورة الأساسية
            if os.path.exists(full_path):
                os.remove(full_path)
            
            # حذف الصورة المصغرة
            filename = os.path.basename(photo_path)
            thumbnail_path = os.path.join(self.base_path, "thumbnails", f"thumb_{filename}")
            if os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حذف الصورة: {e}")
            return False
    
    def get_photo_path(self, photo_path: str, thumbnail: bool = False) -> str:
        """الحصول على المسار الكامل للصورة"""
        if not photo_path:
            return ""
        
        if thumbnail:
            filename = os.path.basename(photo_path)
            return os.path.join(self.base_path, "thumbnails", f"thumb_{filename}")
        else:
            return os.path.join(self.base_path, photo_path)
    
    def photo_exists(self, photo_path: str) -> bool:
        """التحقق من وجود الصورة"""
        if not photo_path:
            return False
        
        full_path = os.path.join(self.base_path, photo_path)
        return os.path.exists(full_path)
    
    def get_photo_info(self, photo_path: str) -> dict:
        """الحصول على معلومات الصورة"""
        info = {
            'exists': False,
            'size': 0,
            'dimensions': (0, 0),
            'format': '',
            'created': None
        }
        
        try:
            if not photo_path:
                return info
            
            full_path = os.path.join(self.base_path, photo_path)
            
            if os.path.exists(full_path):
                info['exists'] = True
                info['size'] = os.path.getsize(full_path)
                info['created'] = datetime.fromtimestamp(os.path.getctime(full_path))
                
                with Image.open(full_path) as img:
                    info['dimensions'] = img.size
                    info['format'] = img.format
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات الصورة: {e}")
        
        return info
    
    def cleanup_orphaned_photos(self, valid_photo_paths: list) -> int:
        """تنظيف الصور المهجورة (غير المرتبطة بعملاء)"""
        try:
            deleted_count = 0
            
            # البحث في جميع مجلدات الصور
            for subfolder in ["profile_photos", "id_photos"]:
                folder_path = os.path.join(self.base_path, subfolder)
                
                if os.path.exists(folder_path):
                    for filename in os.listdir(folder_path):
                        file_path = os.path.join(subfolder, filename).replace("\\", "/")
                        
                        # إذا لم تكن الصورة في قائمة الصور الصحيحة
                        if file_path not in valid_photo_paths:
                            full_path = os.path.join(folder_path, filename)
                            os.remove(full_path)
                            
                            # حذف الصورة المصغرة أيضاً
                            thumbnail_path = os.path.join(self.base_path, "thumbnails", f"thumb_{filename}")
                            if os.path.exists(thumbnail_path):
                                os.remove(thumbnail_path)
                            
                            deleted_count += 1
            
            return deleted_count
            
        except Exception as e:
            print(f"خطأ في تنظيف الصور المهجورة: {e}")
            return 0

# مثال على الاستخدام
if __name__ == "__main__":
    photo_manager = PhotoManager()
    print("مدير الصور جاهز للاستخدام")
    print(f"المجلدات المدعومة: {photo_manager.supported_formats}")
    print(f"أقصى حجم للملف: {photo_manager.max_file_size // (1024*1024)} MB")
