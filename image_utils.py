#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Image utilities for the Camera and Lens Rental Management System
Handles image validation, resizing, and file operations for customer photos
"""

import os
import shutil
from PIL import Image, ImageTk
import tkinter as tk
from tkinter import filedialog, messagebox
from typing import Optional, Tuple

class ImageHandler:
    """Handles image operations for customer photos"""
    
    def __init__(self, photos_dir: str = "customer_photos"):
        """Initialize image handler with photos directory"""
        self.photos_dir = photos_dir
        self.max_file_size = 5 * 1024 * 1024  # 5MB
        self.max_dimensions = (800, 600)  # Max width, height
        self.allowed_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        
        # Create photos directory if it doesn't exist
        if not os.path.exists(self.photos_dir):
            os.makedirs(self.photos_dir)
    
    def validate_image(self, file_path: str) -> Tuple[bool, str]:
        """Validate image file"""
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                return False, "الملف غير موجود"
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > self.max_file_size:
                return False, f"حجم الملف كبير جداً. الحد الأقصى {self.max_file_size // (1024*1024)} ميجابايت"
            
            # Check file extension
            _, ext = os.path.splitext(file_path.lower())
            if ext not in self.allowed_formats:
                return False, f"نوع الملف غير مدعوم. الأنواع المدعومة: {', '.join(self.allowed_formats)}"
            
            # Try to open and validate image
            with Image.open(file_path) as img:
                img.verify()  # Verify it's a valid image
            
            return True, "صورة صالحة"
            
        except Exception as e:
            return False, f"خطأ في التحقق من الصورة: {str(e)}"
    
    def resize_image(self, input_path: str, output_path: str) -> bool:
        """Resize image to fit maximum dimensions while maintaining aspect ratio"""
        try:
            with Image.open(input_path) as img:
                # Convert to RGB if necessary (for PNG with transparency)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Calculate new size maintaining aspect ratio
                img.thumbnail(self.max_dimensions, Image.Resampling.LANCZOS)
                
                # Save resized image
                img.save(output_path, 'JPEG', quality=85, optimize=True)
                
            return True
        except Exception as e:
            print(f"Error resizing image: {e}")
            return False
    
    def save_customer_photo(self, source_path: str, customer_id: int) -> Optional[str]:
        """Save customer photo with proper naming and resizing"""
        try:
            # Validate image first
            is_valid, message = self.validate_image(source_path)
            if not is_valid:
                messagebox.showerror("خطأ في الصورة", message)
                return None
            
            # Generate filename
            filename = f"customer_{customer_id}.jpg"
            destination_path = os.path.join(self.photos_dir, filename)
            
            # Resize and save image
            if self.resize_image(source_path, destination_path):
                return destination_path
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الصورة")
                return None
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الصورة: {str(e)}")
            return None
    
    def delete_customer_photo(self, photo_path: str) -> bool:
        """Delete customer photo file"""
        try:
            if photo_path and os.path.exists(photo_path):
                os.remove(photo_path)
                return True
            return True  # Consider it successful if file doesn't exist
        except Exception as e:
            print(f"Error deleting photo: {e}")
            return False
    
    def get_photo_for_display(self, photo_path: str, size: Tuple[int, int] = (150, 150)) -> Optional[ImageTk.PhotoImage]:
        """Get photo resized for display in GUI"""
        try:
            if not photo_path or not os.path.exists(photo_path):
                return None
            
            with Image.open(photo_path) as img:
                # Resize for display
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage for tkinter
                return ImageTk.PhotoImage(img)
                
        except Exception as e:
            print(f"Error loading photo for display: {e}")
            return None
    
    def select_image_file(self, parent_window) -> Optional[str]:
        """Open file dialog to select image file"""
        try:
            file_types = [
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
            
            filename = filedialog.askopenfilename(
                parent=parent_window,
                title="اختر صورة العميل",
                filetypes=file_types,
                initialdir=os.path.expanduser("~")
            )
            
            return filename if filename else None
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في اختيار الملف: {str(e)}")
            return None

def create_placeholder_image(size: Tuple[int, int] = (150, 150)) -> ImageTk.PhotoImage:
    """Create a placeholder image for customers without photos"""
    try:
        # Create a simple placeholder image
        img = Image.new('RGB', size, color='lightgray')
        
        # You could add text or a simple icon here
        # For now, just return the gray placeholder
        return ImageTk.PhotoImage(img)
        
    except Exception as e:
        print(f"Error creating placeholder image: {e}")
        # Return None if we can't create placeholder
        return None

# Global image handler instance
image_handler = ImageHandler()
