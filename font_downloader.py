#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مُحمل الخطوط العربية
Arabic Font Downloader
"""

import os
import urllib.request
import urllib.parse
import zipfile
import tempfile

# محاولة استيراد requests، وإذا لم تكن متوفرة استخدم urllib
try:
    import requests
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False

class ArabicFontDownloader:
    """مُحمل الخطوط العربية المجانية"""
    
    def __init__(self, fonts_dir="fonts"):
        self.fonts_dir = fonts_dir
        self.ensure_fonts_directory()
        
        # قائمة الخطوط العربية المجانية
        self.available_fonts = {
            'cairo': {
                'name': 'Cairo',
                'url': 'https://github.com/google/fonts/raw/main/ofl/cairo/Cairo%5Bslnt%2Cwght%5D.ttf',
                'filename': 'Cairo-Regular.ttf',
                'description': 'خط القاهرة - خط عربي حديث وأنيق'
            },
            'amiri': {
                'name': 'Amiri',
                'url': 'https://github.com/google/fonts/raw/main/ofl/amiri/Amiri-Regular.ttf',
                'filename': 'Amiri-Regular.ttf',
                'description': 'خط أميري - خط عربي تقليدي'
            },
            'scheherazade': {
                'name': 'Scheherazade New',
                'url': 'https://github.com/google/fonts/raw/main/ofl/scheherazadenew/ScheherazadeNew-Regular.ttf',
                'filename': 'ScheherazadeNew-Regular.ttf',
                'description': 'خط شهرزاد - خط عربي كلاسيكي'
            }
        }
    
    def ensure_fonts_directory(self):
        """التأكد من وجود مجلد الخطوط"""
        if not os.path.exists(self.fonts_dir):
            os.makedirs(self.fonts_dir)
            print(f"تم إنشاء مجلد الخطوط: {self.fonts_dir}")
    
    def download_font(self, font_key, force_download=False):
        """تحميل خط محدد"""
        if font_key not in self.available_fonts:
            print(f"الخط {font_key} غير متوفر")
            return False
        
        font_info = self.available_fonts[font_key]
        font_path = os.path.join(self.fonts_dir, font_info['filename'])
        
        # التحقق من وجود الخط
        if os.path.exists(font_path) and not force_download:
            print(f"الخط {font_info['name']} موجود بالفعل: {font_path}")
            return True
        
        try:
            print(f"جاري تحميل خط {font_info['name']}...")

            # تحميل الخط باستخدام urllib أو requests
            if HAS_REQUESTS:
                # استخدام requests إذا كانت متوفرة
                response = requests.get(font_info['url'], timeout=30)
                response.raise_for_status()
                font_data = response.content
            else:
                # استخدام urllib كبديل
                with urllib.request.urlopen(font_info['url']) as response:
                    font_data = response.read()

            # حفظ الخط
            with open(font_path, 'wb') as f:
                f.write(font_data)

            print(f"تم تحميل الخط بنجاح: {font_path}")
            return True

        except Exception as e:
            print(f"فشل في تحميل الخط {font_info['name']}: {e}")
            return False
    
    def download_all_fonts(self, force_download=False):
        """تحميل جميع الخطوط المتاحة"""
        success_count = 0
        total_count = len(self.available_fonts)
        
        print("بدء تحميل الخطوط العربية...")
        print("=" * 50)
        
        for font_key in self.available_fonts:
            if self.download_font(font_key, force_download):
                success_count += 1
        
        print("=" * 50)
        print(f"تم تحميل {success_count} من {total_count} خطوط")
        
        return success_count == total_count
    
    def get_available_fonts_info(self):
        """الحصول على معلومات الخطوط المتاحة"""
        return self.available_fonts
    
    def list_downloaded_fonts(self):
        """عرض قائمة الخطوط المحملة"""
        downloaded_fonts = []
        
        for font_key, font_info in self.available_fonts.items():
            font_path = os.path.join(self.fonts_dir, font_info['filename'])
            if os.path.exists(font_path):
                file_size = os.path.getsize(font_path)
                downloaded_fonts.append({
                    'key': font_key,
                    'name': font_info['name'],
                    'filename': font_info['filename'],
                    'path': font_path,
                    'size_mb': round(file_size / (1024 * 1024), 2),
                    'description': font_info['description']
                })
        
        return downloaded_fonts
    
    def create_fallback_font(self):
        """إنشاء خط احتياطي بسيط"""
        fallback_path = os.path.join(self.fonts_dir, "fallback-arabic.ttf")
        
        # نسخ خط النظام إذا كان متوفراً
        system_fonts = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]
        
        for system_font in system_fonts:
            if os.path.exists(system_font):
                try:
                    import shutil
                    shutil.copy2(system_font, fallback_path)
                    print(f"تم إنشاء خط احتياطي من: {system_font}")
                    return True
                except Exception as e:
                    print(f"فشل في نسخ الخط الاحتياطي: {e}")
                    continue
        
        print("لم يتم العثور على خط احتياطي مناسب")
        return False
    
    def setup_fonts_for_system(self):
        """إعداد الخطوط للنظام"""
        print("إعداد الخطوط العربية للنظام...")
        
        # تحميل الخطوط الأساسية
        essential_fonts = ['cairo', 'amiri']
        
        for font_key in essential_fonts:
            self.download_font(font_key)
        
        # إنشاء خط احتياطي إذا لم تنجح التحميلات
        downloaded = self.list_downloaded_fonts()
        if not downloaded:
            print("لم يتم تحميل أي خطوط، جاري إنشاء خط احتياطي...")
            self.create_fallback_font()
        
        # عرض النتائج
        final_fonts = self.list_downloaded_fonts()
        if final_fonts:
            print("\nالخطوط المتاحة:")
            for font in final_fonts:
                print(f"  ✓ {font['name']} ({font['size_mb']} MB)")
        else:
            print("⚠️  لم يتم العثور على خطوط عربية")
        
        return len(final_fonts) > 0

def main():
    """الدالة الرئيسية للاختبار"""
    downloader = ArabicFontDownloader()
    
    print("مُحمل الخطوط العربية")
    print("=" * 30)
    
    # عرض الخطوط المتاحة
    fonts_info = downloader.get_available_fonts_info()
    print("\nالخطوط المتاحة للتحميل:")
    for key, info in fonts_info.items():
        print(f"  {key}: {info['name']} - {info['description']}")
    
    # إعداد الخطوط
    success = downloader.setup_fonts_for_system()
    
    if success:
        print("\n✅ تم إعداد الخطوط بنجاح!")
    else:
        print("\n❌ فشل في إعداد الخطوط")
    
    return success

if __name__ == "__main__":
    main()
