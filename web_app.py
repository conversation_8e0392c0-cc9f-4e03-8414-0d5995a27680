#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة تأجير المعدات - الملف الرئيسي المحسن
Rental Management System - Enhanced Main File
"""

from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash
import sqlite3
import os
import sys
from datetime import datetime, timedelta
import json
import hashlib
import secrets
import threading
import time
import socket

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الإعدادات والأدوات
from config.settings import get_config
from utils.decorators import login_required, admin_required, manager_required, permission_required, Permission
from utils.helpers import get_role_display_name, get_local_ip, format_datetime, format_currency
from utils.database_helper import DatabaseColumns, SAFE_QUERIES

# إنشاء تطبيق Flask
app = Flask(__name__, 
           template_folder='../templates',
           static_folder='../static')

# تحميل الإعدادات
config = get_config()
app.config.from_object(config)

# إعداد قاعدة البيانات
class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        if not os.path.exists(os.path.dirname(self.db_path)):
            os.makedirs(os.path.dirname(self.db_path))

# إنشاء مدير قاعدة البيانات
db = DatabaseManager(config.DATABASE_PATH)

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('login.html')
        
        try:
            conn = db.get_connection()
            cursor = conn.cursor()
            
            # البحث عن المستخدم
            cursor.execute('''
                SELECT id, username, password, role, full_name, is_active
                FROM users
                WHERE username = ? AND is_active = 1
            ''', (username,))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                # التحقق من كلمة المرور
                stored_password = user['password']
                
                # فحص كلمة المرور المشفرة أو العادية
                if (stored_password == hashlib.sha256(password.encode()).hexdigest() or 
                    stored_password == password):
                    
                    # تسجيل الدخول بنجاح
                    session['user_id'] = user['id']
                    session['username'] = user['username']
                    session['role'] = user['role']
                    session['full_name'] = user['full_name'] or user['username']  # الاسم الكامل أو اسم المستخدم
                    session.permanent = True
                    
                    flash(f'مرحباً {username}!', 'success')
                    return redirect(url_for('dashboard'))
                else:
                    flash('كلمة المرور غير صحيحة', 'error')
            else:
                flash('اسم المستخدم غير موجود أو غير مفعل', 'error')
                
        except Exception as e:
            flash(f'خطأ في تسجيل الدخول: {str(e)}', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # إحصائيات سريعة
        stats = {}
        
        # عدد العملاء
        cursor.execute('SELECT COUNT(*) FROM customers')
        stats['customers'] = cursor.fetchone()[0]
        
        # عدد المعدات
        cursor.execute('SELECT COUNT(*) FROM equipment')
        stats['equipment'] = cursor.fetchone()[0]
        
        # عدد الإيجارات النشطة
        cursor.execute(SAFE_QUERIES['active_rentals'])
        stats['active_rentals'] = cursor.fetchone()[0]

        # عدد الإيجارات المتأخرة
        cursor.execute(SAFE_QUERIES['overdue_rentals'])
        stats['overdue_rentals'] = cursor.fetchone()[0]

        # الإيجارات الأخيرة
        cursor.execute(SAFE_QUERIES['recent_rentals'])
        recent_rentals = cursor.fetchall()
        
        conn.close()
        
        return render_template('dashboard.html',
                             stats=stats,
                             recent_rentals=recent_rentals,
                             user_role=session.get('role'))
        
    except Exception as e:
        flash(f'خطأ في تحميل لوحة التحكم: {str(e)}', 'error')
        return render_template('dashboard.html', stats={}, recent_rentals=[])

# مسارات أساسية مؤقتة (حتى يتم إنشاء blueprints كاملة)
@app.route('/equipment')
@login_required
def equipment():
    """صفحة المعدات"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # الحصول على جميع المعدات مع إحصائياتها
        cursor.execute('''
            SELECT e.id, e.name, e.model, e.serial_number, e.category, e.status,
                   e.price, e.daily_rental_price, e.description, e.created_at,
                   COUNT(DISTINCT r.id) as total_rentals,
                   COUNT(DISTINCT CASE WHEN r.status = 'Active' THEN r.id END) as active_rentals,
                   COALESCE(SUM(CASE WHEN r.status IN ('Active', 'Returned') THEN r.price END), 0) as total_revenue
            FROM equipment e
            LEFT JOIN rentals r ON e.id = r.equipment_id
            GROUP BY e.id, e.name, e.model, e.serial_number, e.category, e.status,
                     e.price, e.daily_rental_price, e.description, e.created_at
            ORDER BY e.created_at DESC
        ''')

        equipment_list = []
        for row in cursor.fetchall():
            equipment_list.append({
                'id': row['id'],
                'name': row['name'],
                'category': row['category'] or '',
                'model': row['model'] or '',
                'serial_number': row['serial_number'] or '',
                'daily_price': float(row['daily_rental_price'] or 0),
                'status': row['status'],
                'description': row['description'] or '',
                'created_date': row['created_at'],
                'total_rentals': row['total_rentals'],
                'active_rentals': row['active_rentals'],
                'total_revenue': float(row['total_revenue'])
            })

        # إحصائيات عامة
        cursor.execute('SELECT COUNT(*) FROM equipment')
        total_equipment = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM equipment WHERE status = "Available"')
        available_equipment = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM equipment WHERE status = "Rented"')
        rented_equipment = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(*) FROM equipment WHERE status IN ("Maintenance", "Damaged")')
        maintenance_equipment = cursor.fetchone()[0]

        cursor.execute('SELECT COALESCE(SUM(daily_rental_price), 0) FROM equipment WHERE status = "Available"')
        total_value = cursor.fetchone()[0]

        # إحصائيات الفئات
        cursor.execute('''
            SELECT category, COUNT(*) as count, COALESCE(SUM(daily_rental_price), 0) as value
            FROM equipment
            GROUP BY category
            ORDER BY count DESC
        ''')

        categories_data = []
        categories_list = []
        for row in cursor.fetchall():
            category_name = row['category'] or 'غير محدد'
            categories_data.append({
                'name': category_name,
                'count': row['count'],
                'value': float(row['value'])
            })
            categories_list.append(category_name)

        conn.close()

        # قائمة الحالات للفلتر
        statuses = [
            {'name': 'Available', 'display_name': 'متاح'},
            {'name': 'Rented', 'display_name': 'مؤجر'},
            {'name': 'Maintenance', 'display_name': 'صيانة'},
            {'name': 'Damaged', 'display_name': 'تالف'},
            {'name': 'Lost', 'display_name': 'مفقود'}
        ]

        stats = {
            'total_equipment': total_equipment,
            'available_equipment': available_equipment,
            'rented_equipment': rented_equipment,
            'maintenance_equipment': maintenance_equipment,
            'total_value': float(total_value)
        }

        return render_template('equipment.html',
                             equipment=equipment_list,
                             stats=stats,
                             categories=categories_list,
                             categories_data=categories_data,
                             statuses=statuses)

    except Exception as e:
        print(f"خطأ في تحميل صفحة المعدات: {e}")
        statuses = [
            {'name': 'Available', 'display_name': 'متاح'},
            {'name': 'Rented', 'display_name': 'مؤجر'},
            {'name': 'Maintenance', 'display_name': 'صيانة'},
            {'name': 'Damaged', 'display_name': 'تالف'},
            {'name': 'Lost', 'display_name': 'مفقود'}
        ]
        return render_template('equipment.html',
                             equipment=[],
                             stats={'total_equipment': 0, 'available_equipment': 0,
                                   'rented_equipment': 0, 'maintenance_equipment': 0,
                                   'total_value': 0},
                             categories=[],
                             categories_data=[],
                             statuses=statuses,
                             error=str(e))

@app.route('/equipment/<int:equipment_id>')
@login_required
def equipment_details(equipment_id):
    """صفحة تفاصيل المعدة"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # الحصول على بيانات المعدة
        cursor.execute('SELECT * FROM equipment WHERE id = ?', (equipment_id,))
        equipment_row = cursor.fetchone()

        if not equipment_row:
            return render_template('equipment_details.html',
                                 equipment=None,
                                 error='المعدة غير موجودة')

        equipment = {
            'id': equipment_row['id'],
            'name': equipment_row['name'],
            'category': equipment_row['category'] or '',
            'model': equipment_row['model'] or '',
            'serial_number': equipment_row['serial_number'] or '',
            'daily_price': float(equipment_row['daily_rental_price'] or 0),
            'status': equipment_row['status'],
            'description': equipment_row['description'] or '',
            'created_date': equipment_row['created_at']
        }

        # الحصول على تاريخ إيجارات المعدة
        cursor.execute('''
            SELECT r.*, c.name as customer_name, ri.days, ri.total_price
            FROM rental_items ri
            JOIN rentals r ON ri.rental_id = r.id
            LEFT JOIN customers c ON r.customer_id = c.id
            WHERE ri.equipment_id = ?
            ORDER BY r.rental_date DESC
        ''', (equipment_id,))

        rental_history = []
        for row in cursor.fetchall():
            rental_history.append({
                'id': row['id'],
                'customer_name': row['customer_name'] or 'غير محدد',
                'rental_date': row['rental_date'],
                'return_date': row['return_date'],
                'actual_return_date': row['actual_return_date'],
                'days': row['days'],
                'total_price': row['total_price'] or 0,
                'status': row['status']
            })

        # الحصول على سجل الإرجاع
        cursor.execute('''
            SELECT erl.*, r.rental_date, c.name as customer_name
            FROM equipment_return_log erl
            JOIN rentals r ON erl.rental_id = r.id
            LEFT JOIN customers c ON r.customer_id = c.id
            WHERE erl.equipment_id = ?
            ORDER BY erl.return_date DESC
        ''', (equipment_id,))

        return_history = []
        for row in cursor.fetchall():
            return_history.append({
                'return_date': row['return_date'],
                'condition': row['condition'],
                'notes': row['notes'] or '',
                'customer_name': row['customer_name'] or 'غير محدد',
                'rental_date': row['rental_date']
            })

        conn.close()

        return render_template('equipment_details.html',
                             equipment=equipment,
                             rental_history=rental_history,
                             return_history=return_history)

    except Exception as e:
        return render_template('equipment_details.html',
                             equipment=None,
                             rental_history=[],
                             return_history=[],
                             error=str(e))

@app.route('/rentals')
@login_required
def rentals():
    """صفحة الإيجارات المؤقتة"""
    return render_template('rentals.html')

@app.route('/inventory')
@login_required
def inventory():
    """صفحة المخزون الشامل"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # إحصائيات المعدات حسب الفئة
        cursor.execute('''
            SELECT
                category,
                COUNT(*) as total_items,
                SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available_items,
                SUM(CASE WHEN status = 'rented' THEN 1 ELSE 0 END) as rented_items,
                SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_items,
                SUM(CASE WHEN status = 'damaged' THEN 1 ELSE 0 END) as damaged_items
            FROM equipment
            GROUP BY category
            ORDER BY category
        ''')
        category_stats = []
        for row in cursor.fetchall():
            category_stats.append({
                'category': row['category'] or 'غير محدد',
                'total_items': row['total_items'],
                'available_items': row['available_items'],
                'rented_items': row['rented_items'],
                'maintenance_items': row['maintenance_items'],
                'damaged_items': row['damaged_items']
            })

        # جميع المعدات
        cursor.execute('''
            SELECT id, name, category, status, daily_price, description, created_date
            FROM equipment
            ORDER BY category, name
        ''')
        equipment_list = []
        for row in cursor.fetchall():
            equipment_list.append({
                'id': row['id'],
                'name': row['name'],
                'category': row['category'] or 'غير محدد',
                'status': row['status'],
                'daily_price': row['daily_price'] or 0,
                'description': row['description'] or '',
                'created_date': row['created_date']
            })

        # المعدات المفقودة
        cursor.execute('''
            SELECT le.*, e.name as equipment_name, e.category, c.name as customer_name
            FROM lost_equipment le
            JOIN equipment e ON le.equipment_id = e.id
            JOIN customers c ON le.customer_id = c.id
            WHERE le.status = 'Lost'
            ORDER BY le.lost_date DESC
        ''')
        lost_equipment = []
        for row in cursor.fetchall():
            lost_equipment.append({
                'id': row['id'],
                'equipment_name': row['equipment_name'],
                'category': row['category'],
                'customer_name': row['customer_name'],
                'lost_date': row['lost_date'],
                'compensation_amount': row['compensation_amount'] or 0,
                'compensation_paid': row['compensation_paid'] or 0,
                'status': row['status'],
                'notes': row['notes'] or ''
            })

        # المعدات الخارجية
        cursor.execute('''
            SELECT ee.*, c.name as customer_name
            FROM external_equipment ee
            JOIN customers c ON ee.customer_id = c.id
            WHERE ee.status = 'Active'
            ORDER BY ee.rental_start_date DESC
        ''')
        external_equipment = []
        for row in cursor.fetchall():
            external_equipment.append({
                'id': row['id'],
                'equipment_name': row['equipment_name'],
                'equipment_category': row['equipment_category'],
                'supplier_name': row['supplier_name'],
                'customer_name': row['customer_name'],
                'daily_rental_cost': row['daily_rental_cost'],
                'customer_daily_price': row['customer_daily_price'],
                'profit_margin': row['profit_margin'] or 0,
                'rental_start_date': row['rental_start_date'],
                'rental_end_date': row['rental_end_date'],
                'status': row['status']
            })

        conn.close()

        return render_template('inventory_advanced.html',
                             category_stats=category_stats,
                             equipment_list=equipment_list,
                             lost_equipment=lost_equipment,
                             external_equipment=external_equipment)

    except Exception as e:
        return render_template('inventory_advanced.html',
                             category_stats=[],
                             equipment_list=[],
                             lost_equipment=[],
                             external_equipment=[],
                             error=str(e))

@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير المؤقتة"""
    return render_template('reports.html')

@app.route('/new-rental')
@app.route('/new_rental')
@login_required
def new_rental():
    """صفحة إنشاء إيجار جديد"""
    return render_template('new_rental_advanced.html')

# API endpoints للبيانات
@app.route('/api/customers/search')
@login_required
def search_customers():
    """البحث عن العملاء"""
    query = request.args.get('q', '').strip()
    if len(query) < 2:
        return jsonify([])

    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name, phone, email
            FROM customers
            WHERE name LIKE ? OR phone LIKE ?
            ORDER BY name
            LIMIT 10
        ''', (f'%{query}%', f'%{query}%'))

        customers = []
        for row in cursor.fetchall():
            customers.append({
                'id': row['id'],
                'name': row['name'],
                'phone': row['phone'] or '',
                'email': row['email'] or ''
            })

        conn.close()
        return jsonify(customers)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/customers', methods=['POST'])
@login_required
def create_customer():
    """إنشاء عميل جديد"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'اسم العميل مطلوب'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار رقم الهاتف
        if data.get('phone'):
            cursor.execute('SELECT id FROM customers WHERE phone = ?', (data['phone'],))
            if cursor.fetchone():
                return jsonify({'error': 'رقم الهاتف مستخدم بالفعل'}), 400

        # إدراج العميل الجديد
        cursor.execute('''
            INSERT INTO customers (name, phone, email, address, national_id, notes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            data['name'],
            data.get('phone', ''),
            data.get('email', ''),
            data.get('address', ''),
            data.get('national_id', ''),
            data.get('notes', '')
        ))

        customer_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء العميل بنجاح',
            'customer_id': customer_id
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/customers/<int:customer_id>', methods=['PUT'])
@login_required
def update_customer(customer_id):
    """تحديث بيانات عميل"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'اسم العميل مطلوب'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود العميل
        cursor.execute('SELECT id FROM customers WHERE id = ?', (customer_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'العميل غير موجود'}), 404

        # التحقق من عدم تكرار رقم الهاتف (إذا تم تغييره)
        if data.get('phone'):
            cursor.execute('SELECT id FROM customers WHERE phone = ? AND id != ?',
                         (data['phone'], customer_id))
            if cursor.fetchone():
                return jsonify({'error': 'رقم الهاتف مستخدم بالفعل'}), 400

        # تحديث بيانات العميل
        cursor.execute('''
            UPDATE customers
            SET name = ?, phone = ?, email = ?, address = ?, national_id = ?, notes = ?
            WHERE id = ?
        ''', (
            data['name'],
            data.get('phone', ''),
            data.get('email', ''),
            data.get('address', ''),
            data.get('national_id', ''),
            data.get('notes', ''),
            customer_id
        ))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات العميل بنجاح'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/customers/<int:customer_id>', methods=['DELETE'])
@login_required
def delete_customer(customer_id):
    """حذف عميل"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود العميل
        cursor.execute('SELECT name FROM customers WHERE id = ?', (customer_id,))
        customer = cursor.fetchone()
        if not customer:
            return jsonify({'error': 'العميل غير موجود'}), 404

        # التحقق من عدم وجود إيجارات نشطة
        cursor.execute('''
            SELECT COUNT(*) FROM rentals
            WHERE customer_id = ? AND status = 'active'
        ''', (customer_id,))

        active_rentals = cursor.fetchone()[0]
        if active_rentals > 0:
            return jsonify({'error': 'لا يمكن حذف العميل لوجود إيجارات نشطة'}), 400

        # حذف العميل
        cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'تم حذف العميل "{customer["name"]}" بنجاح'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload/customer_photo', methods=['POST'])
@login_required
def upload_customer_photo():
    """رفع صورة العميل"""
    try:
        if 'photo' not in request.files:
            return jsonify({'error': 'لم يتم اختيار صورة'}), 400

        file = request.files['photo']
        customer_id = request.form.get('customer_id')
        photo_type = request.form.get('photo_type', 'profile')  # profile أو id_photo

        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار صورة'}), 400

        if not customer_id:
            return jsonify({'error': 'معرف العميل مطلوب'}), 400

        # التحقق من نوع الملف
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG, أو GIF'}), 400

        # إنشاء مجلد الصور إذا لم يكن موجود
        import os
        upload_folder = 'static/uploads/customers'
        os.makedirs(upload_folder, exist_ok=True)

        # إنشاء اسم ملف فريد
        import uuid
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{customer_id}_{photo_type}_{uuid.uuid4().hex[:8]}.{file_extension}"
        file_path = os.path.join(upload_folder, filename)

        # حفظ الملف
        file.save(file_path)

        # تحديث قاعدة البيانات
        conn = db.get_connection()
        cursor = conn.cursor()

        if photo_type == 'profile':
            cursor.execute('UPDATE customers SET photo_path = ? WHERE id = ?',
                         (f'/static/uploads/customers/{filename}', customer_id))
        else:  # id_photo
            cursor.execute('UPDATE customers SET id_photo_path = ? WHERE id = ?',
                         (f'/static/uploads/customers/{filename}', customer_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم رفع الصورة بنجاح',
            'file_path': f'/static/uploads/customers/{filename}'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/equipment', methods=['POST'])
@login_required
def create_equipment():
    """إنشاء معدة جديدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'اسم المعدة مطلوب'}), 400

        if not data.get('category'):
            return jsonify({'error': 'فئة المعدة مطلوبة'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار الرقم التسلسلي
        if data.get('serial_number'):
            cursor.execute('SELECT id FROM equipment WHERE serial_number = ?',
                         (data['serial_number'],))
            if cursor.fetchone():
                return jsonify({'error': 'الرقم التسلسلي مستخدم بالفعل'}), 400

        # تحويل الحالة إلى الشكل الصحيح
        status_mapping = {
            'available': 'Available',
            'rented': 'Rented',
            'maintenance': 'Maintenance',
            'damaged': 'Damaged',
            'lost': 'Lost'
        }
        status = status_mapping.get(data.get('status', 'available'), 'Available')

        # إدراج المعدة الجديدة
        cursor.execute('''
            INSERT INTO equipment (
                name, category, model, serial_number, daily_rental_price,
                status, description
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            data['name'],
            data['category'],
            data.get('model', ''),
            data.get('serial_number', ''),
            float(data.get('daily_price', 0)),
            status,
            data.get('description', '')
        ))

        equipment_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء المعدة بنجاح',
            'equipment_id': equipment_id
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/equipment/<int:equipment_id>', methods=['PUT'])
@login_required
def update_equipment(equipment_id):
    """تحديث بيانات معدة"""
    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'اسم المعدة مطلوب'}), 400

        if not data.get('category'):
            return jsonify({'error': 'فئة المعدة مطلوبة'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود المعدة
        cursor.execute('SELECT id FROM equipment WHERE id = ?', (equipment_id,))
        if not cursor.fetchone():
            return jsonify({'error': 'المعدة غير موجودة'}), 404

        # التحقق من عدم تكرار الرقم التسلسلي (إذا تم تغييره)
        if data.get('serial_number'):
            cursor.execute('SELECT id FROM equipment WHERE serial_number = ? AND id != ?',
                         (data['serial_number'], equipment_id))
            if cursor.fetchone():
                return jsonify({'error': 'الرقم التسلسلي مستخدم بالفعل'}), 400

        # تحويل الحالة إلى الشكل الصحيح
        status_mapping = {
            'available': 'Availlable',
            'rented': 'Rented',
            'maintenance': 'Maintenance',
            'lost': 'Lost'
        }
        status = status_mapping.get(data.get('status', 'available'), 'Availlable')

        # تحديث بيانات المعدة
        cursor.execute('''
            UPDATE equipment
            SET name = ?, category = ?, model = ?, serial_number = ?,
                daily_rental_price = ?, status = ?, description = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (
            data['name'],
            data['category'],
            data.get('model', ''),
            data.get('serial_number', ''),
            float(data.get('daily_price', 0)),
            status,
            data.get('description', ''),
            equipment_id
        ))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات المعدة بنجاح'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/equipment/<int:equipment_id>', methods=['DELETE'])
@login_required
def delete_equipment_api(equipment_id):
    """حذف معدة"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود المعدة
        cursor.execute('SELECT name FROM equipment WHERE id = ?', (equipment_id,))
        equipment = cursor.fetchone()
        if not equipment:
            return jsonify({'error': 'المعدة غير موجودة'}), 404

        # التحقق من عدم وجود إيجارات نشطة
        cursor.execute('''
            SELECT COUNT(*) FROM rental_items ri
            JOIN rentals r ON ri.rental_id = r.id
            WHERE ri.equipment_id = ? AND r.status = 'active'
        ''', (equipment_id,))

        active_rentals = cursor.fetchone()[0]
        if active_rentals > 0:
            return jsonify({'error': 'لا يمكن حذف المعدة لوجود إيجارات نشطة'}), 400

        # حذف المعدة
        cursor.execute('DELETE FROM equipment WHERE id = ?', (equipment_id,))
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'تم حذف المعدة "{equipment["name"]}" بنجاح'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload/equipment_photo', methods=['POST'])
@login_required
def upload_equipment_photo():
    """رفع صورة المعدة"""
    try:
        if 'photo' not in request.files:
            return jsonify({'error': 'لم يتم اختيار صورة'}), 400

        file = request.files['photo']
        equipment_id = request.form.get('equipment_id')

        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار صورة'}), 400

        if not equipment_id:
            return jsonify({'error': 'معرف المعدة مطلوب'}), 400

        # التحقق من نوع الملف
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG, أو GIF'}), 400

        # إنشاء مجلد الصور إذا لم يكن موجود
        import os
        upload_folder = 'static/uploads/equipment'
        os.makedirs(upload_folder, exist_ok=True)

        # إنشاء اسم ملف فريد
        import uuid
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"{equipment_id}_{uuid.uuid4().hex[:8]}.{file_extension}"
        file_path = os.path.join(upload_folder, filename)

        # حفظ الملف
        file.save(file_path)

        # تحديث قاعدة البيانات
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('UPDATE equipment SET photo_path = ? WHERE id = ?',
                     (f'/static/uploads/equipment/{filename}', equipment_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم رفع الصورة بنجاح',
            'file_path': f'/static/uploads/equipment/{filename}'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/equipment/available')
@login_required
def get_available_equipment():
    """الحصول على المعدات المتاحة"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name, category, daily_price, status, description
            FROM equipment
            WHERE status = 'available'
            ORDER BY category, name
        ''')

        equipment = []
        for row in cursor.fetchall():
            equipment.append({
                'id': row['id'],
                'name': row['name'],
                'category': row['category'] or 'غير محدد',
                'daily_price': float(row['daily_price'] or 0),
                'status': row['status'],
                'description': row['description'] or ''
            })

        conn.close()
        return jsonify(equipment)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rental/create', methods=['POST'])
@login_required
def create_rental_api():
    """إنشاء إيجار جديد"""
    try:
        data = request.get_json()

        customer_id = data.get('customer_id')
        equipment_items = data.get('equipment', [])
        notes = data.get('notes', '')

        if not customer_id or not equipment_items:
            return jsonify({'error': 'بيانات غير مكتملة'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # إنشاء الإيجار الرئيسي
        cursor.execute('''
            INSERT INTO rentals (customer_id, rental_date, status, notes, created_by)
            VALUES (?, datetime('now'), 'active', ?, ?)
        ''', (customer_id, notes, session['user_id']))

        rental_id = cursor.lastrowid

        total_amount = 0

        # إضافة عناصر الإيجار
        for item in equipment_items:
            equipment_id = item['equipment_id']
            days = int(item['days'])
            daily_price = float(item['daily_price'])
            item_total = days * daily_price
            total_amount += item_total

            cursor.execute('''
                INSERT INTO rental_items (rental_id, equipment_id, days, daily_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', (rental_id, equipment_id, days, daily_price, item_total))

            # تحديث حالة المعدة
            cursor.execute('''
                UPDATE equipment SET status = 'rented' WHERE id = ?
            ''', (equipment_id,))

        # تحديث المبلغ الإجمالي
        cursor.execute('''
            UPDATE rentals SET total_amount = ? WHERE id = ?
        ''', (total_amount, rental_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'rental_id': rental_id,
            'total_amount': total_amount
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# API endpoints للمخزون
@app.route('/api/inventory/equipment/<int:equipment_id>/update-status', methods=['POST'])
@login_required
def update_equipment_status(equipment_id):
    """تحديث حالة المعدة"""
    try:
        data = request.get_json()
        new_status = data.get('status')

        # تحويل الحالة إلى الشكل الصحيح
        status_mapping = {
            'available': 'Available',
            'rented': 'Rented',
            'maintenance': 'Maintenance',
            'damaged': 'Damaged',
            'lost': 'Lost'
        }

        if new_status not in status_mapping:
            return jsonify({'error': 'حالة غير صحيحة'}), 400

        new_status = status_mapping[new_status]

        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE equipment
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (new_status, equipment_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم تحديث حالة المعدة'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/inventory/equipment/<int:equipment_id>/delete', methods=['DELETE'])
@login_required
def delete_equipment(equipment_id):
    """حذف معدة"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود إيجارات نشطة
        cursor.execute('''
            SELECT COUNT(*) FROM rental_items ri
            JOIN rentals r ON ri.rental_id = r.id
            WHERE ri.equipment_id = ? AND r.status = 'active'
        ''', (equipment_id,))

        if cursor.fetchone()[0] > 0:
            return jsonify({'error': 'لا يمكن حذف معدة مؤجرة حالياً'}), 400

        cursor.execute('DELETE FROM equipment WHERE id = ?', (equipment_id,))
        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم حذف المعدة'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/inventory/lost-equipment/<int:lost_id>/compensate', methods=['POST'])
@login_required
def compensate_lost_equipment(lost_id):
    """تعويض معدة مفقودة"""
    try:
        data = request.get_json()
        compensation_paid = float(data.get('compensation_paid', 0))

        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE lost_equipment
            SET compensation_paid = ?,
                status = CASE
                    WHEN compensation_paid >= compensation_amount THEN 'Compensated'
                    ELSE 'Lost'
                END,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (compensation_paid, lost_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم تحديث التعويض'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/inventory/external-equipment/add', methods=['POST'])
@login_required
def add_external_equipment():
    """إضافة معدة خارجية"""
    try:
        data = request.get_json()

        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO external_equipment (
                equipment_name, equipment_model, equipment_category,
                supplier_name, supplier_phone, supplier_address,
                daily_rental_cost, customer_daily_price, profit_margin,
                rental_start_date, rental_end_date, customer_id, notes, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Active')
        ''', (
            data['equipment_name'], data.get('equipment_model', ''),
            data['equipment_category'], data['supplier_name'],
            data.get('supplier_phone', ''), data.get('supplier_address', ''),
            float(data['daily_rental_cost']), float(data['customer_daily_price']),
            float(data.get('profit_margin', 0)), data['rental_start_date'],
            data['rental_end_date'], int(data['customer_id']),
            data.get('notes', '')
        ))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': 'تم إضافة المعدة الخارجية'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/return-equipment', methods=['POST'])
@login_required
def process_equipment_return():
    """معالجة إرجاع المعدات"""
    try:
        data = request.get_json()
        rental_id = data.get('rental_id')
        equipment_returns = data.get('equipment', [])
        general_notes = data.get('general_notes', '')

        if not rental_id or not equipment_returns:
            return jsonify({'error': 'بيانات غير مكتملة'}), 400

        conn = db.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود الإيجار
        cursor.execute('SELECT * FROM rentals WHERE id = ? AND status = "active"', (rental_id,))
        rental = cursor.fetchone()
        if not rental:
            return jsonify({'error': 'الإيجار غير موجود أو غير نشط'}), 404

        returned_count = 0
        lost_count = 0
        damaged_count = 0

        for equipment_return in equipment_returns:
            equipment_id = equipment_return.get('equipment_id')
            condition = equipment_return.get('condition')
            notes = equipment_return.get('notes', '')

            # تحديث حالة المعدة حسب الحالة
            if condition == 'good':
                # المعدة سليمة - إرجاعها للمخزون
                cursor.execute('''
                    UPDATE equipment
                    SET status = 'available', updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (equipment_id,))
                returned_count += 1

            elif condition == 'damaged':
                # المعدة معطوبة - تحديث الحالة
                cursor.execute('''
                    UPDATE equipment
                    SET status = 'damaged', updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (equipment_id,))
                damaged_count += 1

            elif condition == 'lost':
                # المعدة مفقودة - إضافتها لسجل المفقودات
                cursor.execute('''
                    UPDATE equipment
                    SET status = 'Lost', updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (equipment_id,))

                # إضافة للمعدات المفقودة
                cursor.execute('''
                    INSERT INTO lost_equipment (
                        equipment_id, customer_id, lost_date,
                        compensation_amount, status, notes
                    ) VALUES (?, ?, DATE('now'), ?, 'Lost', ?)
                ''', (equipment_id, rental['customer_id'],
                      equipment_return.get('compensation_amount', 0), notes))
                lost_count += 1

            # تسجيل عملية الإرجاع
            cursor.execute('''
                INSERT INTO equipment_return_log (
                    rental_id, equipment_id, return_date, condition, notes
                ) VALUES (?, ?, DATE('now'), ?, ?)
            ''', (rental_id, equipment_id, condition, notes))

        # تحديث حالة الإيجار
        cursor.execute('''
            UPDATE rentals
            SET status = 'returned',
                actual_return_date = DATE('now'),
                return_notes = ?
            WHERE id = ?
        ''', (general_notes, rental_id))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم إرجاع المعدات بنجاح',
            'summary': {
                'returned': returned_count,
                'damaged': damaged_count,
                'lost': lost_count
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# إنشاء الجداول المطلوبة إذا لم تكن موجودة
def create_rental_tables():
    """إنشاء جداول الإيجارات والمخزون إذا لم تكن موجودة"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # جدول عناصر الإيجار
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rental_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rental_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                days INTEGER NOT NULL DEFAULT 1,
                daily_price REAL NOT NULL DEFAULT 0,
                total_price REAL NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rental_id) REFERENCES rentals (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        ''')

        # جدول المعدات المفقودة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS lost_equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                customer_id INTEGER NOT NULL,
                lost_date DATE NOT NULL,
                compensation_amount REAL DEFAULT 0,
                compensation_paid REAL DEFAULT 0,
                status TEXT DEFAULT 'Lost',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        # جدول المعدات الخارجية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS external_equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_name TEXT NOT NULL,
                equipment_model TEXT,
                equipment_category TEXT NOT NULL,
                supplier_name TEXT NOT NULL,
                supplier_phone TEXT,
                supplier_address TEXT,
                daily_rental_cost REAL NOT NULL,
                customer_daily_price REAL NOT NULL,
                profit_margin REAL DEFAULT 0,
                rental_start_date DATE NOT NULL,
                rental_end_date DATE NOT NULL,
                customer_id INTEGER NOT NULL,
                status TEXT DEFAULT 'Active',
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        # جدول سجل إرجاع المعدات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment_return_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rental_id INTEGER NOT NULL,
                equipment_id INTEGER NOT NULL,
                return_date DATE NOT NULL,
                condition TEXT NOT NULL,
                notes TEXT DEFAULT '',
                returned_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rental_id) REFERENCES rentals (id),
                FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                FOREIGN KEY (returned_by) REFERENCES users (id)
            )
        ''')

        # إضافة أعمدة للمعدات إذا لم تكن موجودة
        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN daily_price REAL DEFAULT 0')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN description TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN model TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN serial_number TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN purchase_date DATE')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN purchase_price REAL DEFAULT 0')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE equipment ADD COLUMN photo_path TEXT')
        except:
            pass

        # إضافة أعمدة للإيجارات إذا لم تكن موجودة
        try:
            cursor.execute('ALTER TABLE rentals ADD COLUMN total_amount REAL DEFAULT 0')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE rentals ADD COLUMN created_by INTEGER')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE rentals ADD COLUMN actual_return_date DATE')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE rentals ADD COLUMN return_notes TEXT')
        except:
            pass

        # إضافة أعمدة للعملاء إذا لم تكن موجودة
        try:
            cursor.execute('ALTER TABLE customers ADD COLUMN photo_path TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE customers ADD COLUMN id_photo_path TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE customers ADD COLUMN national_id TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE customers ADD COLUMN address TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE customers ADD COLUMN notes TEXT')
        except:
            pass

        try:
            cursor.execute('ALTER TABLE customers ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
        except:
            pass

        conn.commit()
        conn.close()

    except Exception as e:
        print(f"خطأ في إنشاء الجداول: {e}")

# إنشاء الجداول عند بدء التطبيق
create_rental_tables()

@app.route('/create-multi-rental')
@login_required
def create_multi_rental():
    """إنشاء إيجار متعدد"""
    return render_template('create_multi_rental.html')

@app.route('/rental-details/<int:rental_id>')
@login_required
def rental_details(rental_id):
    """تفاصيل الإيجار"""
    return render_template('rental_details.html', rental_id=rental_id)

@app.route('/multi-rental-details/<int:rental_id>')
@login_required
def multi_rental_details(rental_id):
    """تفاصيل الإيجار المتعدد"""
    return render_template('multi_rental_details.html', rental_id=rental_id)

@app.route('/edit-multi-rental/<int:rental_id>')
@login_required
def edit_multi_rental(rental_id):
    """تعديل الإيجار المتعدد"""
    return render_template('edit_multi_rental.html', rental_id=rental_id)

# مسارات مؤقتة لمنع أخطاء القوالب
@app.route('/customers')
@login_required
def customers():
    """صفحة العملاء"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # الحصول على جميع العملاء مع إحصائياتهم
        cursor.execute('''
            SELECT c.*,
                   COUNT(DISTINCT r.id) as total_rentals,
                   COUNT(DISTINCT CASE WHEN r.status = 'active' THEN r.id END) as active_rentals,
                   COALESCE(SUM(CASE WHEN r.status IN ('active', 'returned') THEN r.total_amount END), 0) as total_spent,
                   COUNT(DISTINCT le.id) as lost_equipment_count
            FROM customers c
            LEFT JOIN rentals r ON c.id = r.customer_id
            LEFT JOIN lost_equipment le ON c.id = le.customer_id AND le.status = 'Lost'
            GROUP BY c.id
            ORDER BY c.created_date DESC
        ''')

        customers_list = []
        for row in cursor.fetchall():
            customers_list.append({
                'id': row['id'],
                'name': row['name'],
                'phone': row['phone'] or '',
                'email': row['email'] or '',
                'address': row['address'] or '',
                'national_id': row['national_id'] or '',
                'notes': row['notes'] or '',
                'photo_path': row['photo_path'] or '',
                'id_photo_path': row['id_photo_path'] or '',
                'created_date': row['created_date'],
                'total_rentals': row['total_rentals'],
                'active_rentals': row['active_rentals'],
                'total_spent': float(row['total_spent']),
                'lost_equipment_count': row['lost_equipment_count']
            })

        # إحصائيات عامة
        cursor.execute('SELECT COUNT(*) FROM customers')
        total_customers = cursor.fetchone()[0]

        cursor.execute('''
            SELECT COUNT(DISTINCT customer_id) FROM rentals
            WHERE status = 'active'
        ''')
        active_customers = cursor.fetchone()[0]

        cursor.execute('''
            SELECT COALESCE(SUM(total_amount), 0) FROM rentals
            WHERE status IN ('active', 'returned')
        ''')
        total_revenue = cursor.fetchone()[0]

        conn.close()

        stats = {
            'total_customers': total_customers,
            'active_customers': active_customers,
            'total_revenue': float(total_revenue),
            'new_this_month': len([c for c in customers_list
                                 if c['created_date'] and c['created_date'].startswith('2025-07')])
        }

        return render_template('customers.html',
                             customers=customers_list,
                             stats=stats)

    except Exception as e:
        return render_template('customers.html',
                             customers=[],
                             stats={'total_customers': 0, 'active_customers': 0,
                                   'total_revenue': 0, 'new_this_month': 0},
                             error=str(e))

@app.route('/customer/<int:customer_id>')
@login_required
def customer_details(customer_id):
    """صفحة تفاصيل العميل"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        # الحصول على بيانات العميل
        cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        customer_row = cursor.fetchone()

        if not customer_row:
            return render_template('customer_details.html',
                                 customer=None,
                                 error='العميل غير موجود')

        customer = {
            'id': customer_row['id'],
            'name': customer_row['name'],
            'phone': customer_row['phone'] or '',
            'email': customer_row['email'] or '',
            'address': customer_row['address'] or '',
            'national_id': customer_row['national_id'] or '',
            'notes': customer_row['notes'] or '',
            'photo_path': customer_row['photo_path'] or '',
            'id_photo_path': customer_row['id_photo_path'] or '',
            'created_date': customer_row['created_date']
        }

        # الحصول على إيجارات العميل
        cursor.execute('''
            SELECT r.*, COUNT(ri.id) as equipment_count
            FROM rentals r
            LEFT JOIN rental_items ri ON r.id = ri.rental_id
            WHERE r.customer_id = ?
            GROUP BY r.id
            ORDER BY r.rental_date DESC
        ''', (customer_id,))

        rentals = []
        for row in cursor.fetchall():
            rentals.append({
                'id': row['id'],
                'rental_date': row['rental_date'],
                'return_date': row['return_date'],
                'actual_return_date': row['actual_return_date'],
                'total_amount': row['total_amount'] or 0,
                'status': row['status'],
                'equipment_count': row['equipment_count'],
                'return_notes': row['return_notes'] or ''
            })

        # الحصول على المعدات المفقودة
        cursor.execute('''
            SELECT le.*, e.name as equipment_name, e.category
            FROM lost_equipment le
            JOIN equipment e ON le.equipment_id = e.id
            WHERE le.customer_id = ? AND le.status = 'Lost'
            ORDER BY le.lost_date DESC
        ''', (customer_id,))

        lost_equipment = []
        for row in cursor.fetchall():
            lost_equipment.append({
                'id': row['id'],
                'equipment_name': row['equipment_name'],
                'category': row['category'],
                'lost_date': row['lost_date'],
                'compensation_amount': row['compensation_amount'] or 0,
                'compensation_paid': row['compensation_paid'] or 0,
                'status': row['status'],
                'notes': row['notes'] or ''
            })

        conn.close()

        return render_template('customer_details.html',
                             customer=customer,
                             rentals=rentals,
                             lost_equipment=lost_equipment)

    except Exception as e:
        return render_template('customer_details.html',
                             customer=None,
                             rentals=[],
                             lost_equipment=[],
                             error=str(e))

@app.route('/offices')
@login_required
def offices():
    """صفحة المكاتب المؤقتة"""
    return render_template('offices.html')

@app.route('/add_equipment_enhanced')
@login_required
def add_equipment_enhanced():
    """إضافة معدة محسنة - إعادة توجيه"""
    return redirect(url_for('equipment'))

@app.route('/lost_equipment')
@login_required
def lost_equipment():
    """صفحة المعدات المفقودة"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT le.*, e.name as equipment_name, e.category, c.name as customer_name
            FROM lost_equipment le
            JOIN equipment e ON le.equipment_id = e.id
            JOIN customers c ON le.customer_id = c.id
            ORDER BY le.lost_date DESC
        ''')

        lost_items = []
        for row in cursor.fetchall():
            lost_items.append({
                'id': row['id'],
                'equipment_name': row['equipment_name'],
                'category': row['category'],
                'customer_name': row['customer_name'],
                'lost_date': row['lost_date'],
                'compensation_amount': row['compensation_amount'] or 0,
                'compensation_paid': row['compensation_paid'] or 0,
                'status': row['status'],
                'notes': row['notes'] or ''
            })

        conn.close()
        return render_template('lost_equipment_advanced.html', lost_items=lost_items)

    except Exception as e:
        return redirect(url_for('inventory'))

@app.route('/external_equipment')
@login_required
def external_equipment():
    """صفحة المعدات الخارجية"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT ee.*, c.name as customer_name
            FROM external_equipment ee
            JOIN customers c ON ee.customer_id = c.id
            ORDER BY ee.rental_start_date DESC
        ''')

        external_items = []
        for row in cursor.fetchall():
            external_items.append({
                'id': row['id'],
                'equipment_name': row['equipment_name'],
                'equipment_category': row['equipment_category'],
                'supplier_name': row['supplier_name'],
                'customer_name': row['customer_name'],
                'daily_rental_cost': row['daily_rental_cost'],
                'customer_daily_price': row['customer_daily_price'],
                'profit_margin': row['profit_margin'] or 0,
                'rental_start_date': row['rental_start_date'],
                'rental_end_date': row['rental_end_date'],
                'status': row['status']
            })

        conn.close()
        return render_template('external_equipment_advanced.html', external_items=external_items)

    except Exception as e:
        return redirect(url_for('inventory'))

@app.route('/return_equipment')
@app.route('/return_equipment/<int:rental_id>')
@login_required
def return_equipment(rental_id=None):
    """صفحة إرجاع المعدات"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()

        rental_info = None
        equipment_list = []

        if rental_id:
            # الحصول على معلومات الإيجار
            cursor.execute('''
                SELECT r.*, c.name as customer_name, c.phone as customer_phone
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                WHERE r.id = ? AND r.status = 'active'
            ''', (rental_id,))

            rental_row = cursor.fetchone()
            if rental_row:
                rental_info = {
                    'id': rental_row['id'],
                    'customer_name': rental_row['customer_name'],
                    'customer_phone': rental_row['customer_phone'],
                    'rental_date': rental_row['rental_date'],
                    'return_date': rental_row['return_date'],
                    'total_amount': rental_row['total_amount'] or 0,
                    'status': rental_row['status']
                }

                # الحصول على المعدات المؤجرة
                cursor.execute('''
                    SELECT ri.*, e.name, e.category, e.description, e.daily_price
                    FROM rental_items ri
                    JOIN equipment e ON ri.equipment_id = e.id
                    WHERE ri.rental_id = ?
                ''', (rental_id,))

                for row in cursor.fetchall():
                    equipment_list.append({
                        'id': row['equipment_id'],
                        'rental_item_id': row['id'],
                        'name': row['name'],
                        'category': row['category'],
                        'description': row['description'] or '',
                        'daily_price': row['daily_price'] or 0,
                        'days': row['days'],
                        'total_price': row['total_price']
                    })
        else:
            # عرض جميع الإيجارات النشطة للاختيار
            cursor.execute('''
                SELECT r.id, r.rental_date, r.return_date, r.total_amount,
                       c.name as customer_name, c.phone as customer_phone,
                       COUNT(ri.id) as equipment_count
                FROM rentals r
                LEFT JOIN customers c ON r.customer_id = c.id
                LEFT JOIN rental_items ri ON r.id = ri.rental_id
                WHERE r.status = 'active'
                GROUP BY r.id
                ORDER BY r.rental_date DESC
            ''')

            active_rentals = []
            for row in cursor.fetchall():
                active_rentals.append({
                    'id': row['id'],
                    'customer_name': row['customer_name'],
                    'customer_phone': row['customer_phone'],
                    'rental_date': row['rental_date'],
                    'return_date': row['return_date'],
                    'total_amount': row['total_amount'] or 0,
                    'equipment_count': row['equipment_count']
                })

            conn.close()
            return render_template('return_equipment_selection.html',
                                 active_rentals=active_rentals)

        conn.close()
        return render_template('return_equipment_advanced.html',
                             rental_info=rental_info,
                             equipment_list=equipment_list)

    except Exception as e:
        return render_template('return_equipment_advanced.html',
                             rental_info=None,
                             equipment_list=[],
                             error=str(e))

@app.route('/inventory_items')
@login_required
def inventory_items():
    """عناصر المخزون - إعادة توجيه"""
    return redirect(url_for('inventory'))

@app.route('/rental_reports')
@login_required
def rental_reports():
    """تقارير الإيجارات - إعادة توجيه"""
    return redirect(url_for('reports'))

@app.route('/equipment_reports')
@login_required
def equipment_reports():
    """تقارير المعدات - إعادة توجيه"""
    return redirect(url_for('reports'))

@app.route('/admin_users')
@login_required
def admin_users():
    """إدارة المستخدمين - إعادة توجيه"""
    return redirect(url_for('dashboard'))

@app.route('/company_settings_page')
@login_required
def company_settings_page():
    """إعدادات الشركة - إعادة توجيه"""
    return redirect(url_for('dashboard'))

@app.route('/manage_permissions')
@login_required
def manage_permissions():
    """إدارة الصلاحيات - إعادة توجيه"""
    return redirect(url_for('dashboard'))

@app.route('/backup_page')
@login_required
def backup_page():
    """النسخ الاحتياطي - إعادة توجيه"""
    return redirect(url_for('dashboard'))

# معالج الأخطاء
@app.errorhandler(404)
def not_found_error(error):
    """معالج خطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """معالج خطأ 500"""
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    """معالج خطأ 403"""
    return render_template('errors/403.html'), 403

# دوال مساعدة للقوالب
@app.template_filter('datetime')
def datetime_filter(value):
    """فلتر تنسيق التاريخ والوقت"""
    return format_datetime(value)

@app.template_filter('currency')
def currency_filter(value):
    """فلتر تنسيق العملة"""
    return format_currency(value)

@app.template_global()
def get_role_name(role):
    """دالة عامة للحصول على اسم الدور"""
    return get_role_display_name(role)

# إعداد الجلسات
@app.before_request
def before_request():
    """معالج ما قبل الطلب"""
    # تحديث وقت آخر نشاط
    if 'user_id' in session:
        session['last_activity'] = datetime.now().isoformat()

# استيراد وتسجيل المسارات
def register_blueprints():
    """تسجيل جميع المسارات"""
    try:
        # استيراد المسارات المتوفرة
        from routes.customers import customers_bp

        # تسجيل المسارات المتوفرة
        app.register_blueprint(customers_bp, url_prefix='/customers')

        print("✅ تم تسجيل المسارات المتوفرة:")
        print("   - /customers (إدارة العملاء)")

        # المسارات التي سيتم إضافتها لاحقاً
        pending_routes = [
            "equipment (إدارة المعدات)",
            "rentals (إدارة الإيجارات)",
            "inventory (إدارة المخزون)",
            "reports (التقارير)",
            "admin (إدارة النظام)",
            "api (واجهات برمجة التطبيقات)"
        ]

        print("📋 المسارات المخطط إضافتها:")
        for route in pending_routes:
            print(f"   - {route}")

    except ImportError as e:
        print(f"⚠️ بعض المسارات غير متوفرة بعد: {e}")
        print("💡 سيتم استخدام المسارات الأساسية فقط")
    except Exception as e:
        print(f"❌ خطأ في تسجيل المسارات: {e}")

# تشغيل التطبيق
def run_app():
    """تشغيل التطبيق"""
    try:
        # تسجيل المسارات
        register_blueprints()
        # الحصول على عنوان IP المحلي
        local_ip = get_local_ip()
        
        print("=" * 60)
        print("🚀 نظام إدارة تأجير المعدات")
        print("=" * 60)
        print(f"🌐 الخادم يعمل على:")
        print(f"   • المحلي: http://127.0.0.1:{config.DEFAULT_PORT}")
        print(f"   • الشبكة: http://{local_ip}:{config.DEFAULT_PORT}")
        print("=" * 60)
        print("📋 للوصول للنظام:")
        print("   • اسم المستخدم: admin")
        print("   • كلمة المرور: admin123")
        print("=" * 60)
        print("⚠️ للإيقاف: اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل الخادم
        app.run(
            host=config.DEFAULT_HOST,
            port=config.DEFAULT_PORT,
            debug=False,  # إيقاف debug لتجنب مشاكل إعادة التشغيل
            threaded=True,
            use_reloader=False  # إيقاف reloader
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == '__main__':
    run_app()
