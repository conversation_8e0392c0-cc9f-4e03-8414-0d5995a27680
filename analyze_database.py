#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def analyze_database():
    """تحليل قاعدة البيانات"""
    config = get_config()
    conn = sqlite3.connect(config.DATABASE_PATH)
    cursor = conn.cursor()

    print('=== فحص جداول قاعدة البيانات ===')
    cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
    tables = cursor.fetchall()
    for table in tables:
        print(f'جدول: {table[0]}')

    print('\n=== فحص جدول equipment ===')
    cursor.execute('PRAGMA table_info(equipment)')
    columns = cursor.fetchall()
    for col in columns:
        print(f'  {col[1]} ({col[2]})')

    print('\n=== فحص جدول external_equipment ===')
    try:
        cursor.execute('PRAGMA table_info(external_equipment)')
        columns = cursor.fetchall()
        for col in columns:
            print(f'  {col[1]} ({col[2]})')
    except Exception as e:
        print(f'خطأ في جدول external_equipment: {e}')

    print('\n=== عدد المعدات في كل جدول ===')
    cursor.execute('SELECT COUNT(*) FROM equipment')
    equipment_count = cursor.fetchone()[0]
    print(f'المعدات الداخلية: {equipment_count}')

    try:
        cursor.execute('SELECT COUNT(*) FROM external_equipment')
        external_count = cursor.fetchone()[0]
        print(f'المعدات الخارجية: {external_count}')
    except Exception as e:
        print(f'خطأ في عد المعدات الخارجية: {e}')

    print('\n=== عينة من المعدات الداخلية ===')
    cursor.execute('SELECT id, name, status FROM equipment LIMIT 5')
    equipment_sample = cursor.fetchall()
    for eq in equipment_sample:
        print(f'  ID: {eq[0]}, Name: {eq[1]}, Status: {eq[2]}')

    print('\n=== عينة من المعدات الخارجية ===')
    try:
        cursor.execute('SELECT id, equipment_name, status FROM external_equipment LIMIT 5')
        external_sample = cursor.fetchall()
        for eq in external_sample:
            print(f'  ID: {eq[0]}, Name: {eq[1]}, Status: {eq[2]}')
    except Exception as e:
        print(f'خطأ في جلب المعدات الخارجية: {e}')

    print('\n=== فحص جدول offices ===')
    try:
        cursor.execute('PRAGMA table_info(offices)')
        columns = cursor.fetchall()
        print('أعمدة جدول offices:')
        for col in columns:
            print(f'  {col[1]} ({col[2]})')
        
        cursor.execute('SELECT COUNT(*) FROM offices')
        offices_count = cursor.fetchone()[0]
        print(f'عدد المكاتب: {offices_count}')
    except Exception as e:
        print(f'خطأ في جدول offices: {e}')

    conn.close()

if __name__ == '__main__':
    analyze_database()
