# -*- coding: utf-8 -*-
"""
Configuration settings for the rental management system
"""

import os
from datetime import timedelta

class Config:
    """إعدادات النظام الأساسية"""
    
    # مسارات النظام
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    TEMPLATE_DIR = os.path.join(os.path.dirname(BASE_DIR), 'templates')
    STATIC_DIR = os.path.join(os.path.dirname(BASE_DIR), 'static')
    
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = os.path.join(os.path.dirname(BASE_DIR), 'database', 'rental_system.db')
    
    # إعدادات الملفات
    UPLOAD_FOLDER = os.path.join(STATIC_DIR, 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # إعدادات الصور
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    CUSTOMER_PHOTOS_DIR = os.path.join(os.path.dirname(BASE_DIR), 'customer_photos')
    COMPANY_ASSETS_DIR = os.path.join(os.path.dirname(BASE_DIR), 'company_assets')
    
    # إعدادات النسخ الاحتياطية
    BACKUP_DIR = os.path.join(os.path.dirname(BASE_DIR), 'backups')
    
    # إعدادات الخادم
    DEFAULT_HOST = '0.0.0.0'
    DEFAULT_PORT = 5000
    DEBUG = False
    
    # إعدادات الأمان
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    MAX_EXPORT_RECORDS = 10000
    
    # إعدادات الإشعارات
    OVERDUE_NOTIFICATION_DAYS = 1
    LOW_STOCK_THRESHOLD = 5

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    SESSION_COOKIE_SECURE = False

class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    SESSION_COOKIE_SECURE = True

# اختيار الإعدادات حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """الحصول على إعدادات النظام"""
    env = os.environ.get('FLASK_ENV', 'default')
    return config.get(env, config['default'])
