{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة تأجير المعدات{% endblock %}

{% block extra_css %}
<style>
    /* إصلاح الخطوط العربية */
    body, .card, .btn, h1, h2, h3, h4, h5, h6, p, span, div {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        direction: rtl;
        text-align: right;
    }

    .card-title, .card-text {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    }

    /* تحسين عرض النصوص العربية */
    .arabic-text {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-weight: 500;
        letter-spacing: 0.5px;
        line-height: 1.6;
    }

    /* تحسين الأزرار */
    .btn {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-weight: 600;
        border-radius: 8px;
        padding: 10px 20px;
    }

    /* تحسين البطاقات */
    .card {
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }

    /* تحسين الإحصائيات */
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }

    .stat-card .card-body {
        padding: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- فحص الصلاحيات -->
    {% if session.role != 'admin' %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning text-center" role="alert">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h4 class="arabic-text">عذراً، هذه الصفحة متاحة لمدير النظام فقط</h4>
                <p class="arabic-text">يرجى تسجيل الدخول بحساب مدير النظام للوصول إلى التقارير والإحصائيات</p>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>العودة للوحة التحكم
                </a>
            </div>
        </div>
    </div>
    {% else %}

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold arabic-text">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        التقارير والإحصائيات
                    </h1>
                    <p class="text-muted mb-0 arabic-text">تحليل شامل لأداء النظام والإيرادات - مدير النظام</p>
                </div>
                <div>
                    <button class="btn btn-success" onclick="printEquipmentList()">
                        <i class="fas fa-print me-2"></i>طباعة قائمة المعدات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إيرادات الشهر</h6>
                            <h3 class="mb-0">{{ "%.2f"|format(monthly_revenue) }} ج.م</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إجمالي الإيجارات</h6>
                            <h3 class="mb-0">{{ total_rentals }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-handshake fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">معدل الاستخدام</h6>
                            <h3 class="mb-0">{{ "%.1f"|format((equipment_by_category.values()|sum(attribute='rented') / equipment_by_category.values()|sum(attribute='total') * 100) if equipment_by_category else 0) }}%</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stats-card" style="background: linear-gradient(135deg, #e83e8c 0%, #6f42c1 100%);">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">متوسط سعر الإيجار</h6>
                            <h3 class="mb-0">{{ "%.0f"|format(monthly_revenue / total_rentals if total_rentals > 0 else 0) }} ج.م</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Equipment by Category -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>المعدات حسب الفئة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الفئة</th>
                                    <th>الإجمالي</th>
                                    <th>متاح</th>
                                    <th>مؤجر</th>
                                    <th>معدل الاستخدام</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category, data in equipment_by_category.items() %}
                                <tr>
                                    <td><strong>{{ category }}</strong></td>
                                    <td>{{ data.total }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ data.available }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ data.rented }}</span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            {% set usage_rate = (data.rented / data.total * 100) if data.total > 0 else 0 %}
                                            <div class="progress-bar" role="progressbar" style="width: {{ usage_rate }}%">
                                                {{ "%.1f"|format(usage_rate) }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-camera fa-2x text-primary mb-2"></i>
                                <h4 class="mb-1">{{ equipment_by_category.values()|sum(attribute='total') }}</h4>
                                <small class="text-muted">إجمالي المعدات</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="mb-1">{{ equipment_by_category.values()|sum(attribute='available') }}</h4>
                                <small class="text-muted">معدات متاحة</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-handshake fa-2x text-warning mb-2"></i>
                                <h4 class="mb-1">{{ equipment_by_category.values()|sum(attribute='rented') }}</h4>
                                <small class="text-muted">معدات مؤجرة</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                <h4 class="mb-1">{{ equipment_by_category|length }}</h4>
                                <small class="text-muted">فئات المعدات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Reports -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>تقارير مفصلة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                    <h5>تقرير الإيرادات</h5>
                                    <p class="text-muted">تحليل مفصل للإيرادات الشهرية والسنوية</p>
                                    <button class="btn btn-primary" onclick="generateRevenueReport()">
                                        <i class="fas fa-download me-2"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border-success">
                                <div class="card-body text-center">
                                    <i class="fas fa-camera fa-3x text-success mb-3"></i>
                                    <h5>تقرير المعدات</h5>
                                    <p class="text-muted">حالة المعدات ومعدلات الاستخدام</p>
                                    <button class="btn btn-success" onclick="generateEquipmentReport()">
                                        <i class="fas fa-download me-2"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-3x text-info mb-3"></i>
                                    <h5>تقرير العملاء</h5>
                                    <p class="text-muted">إحصائيات العملاء وأنشطة الإيجار</p>
                                    <button class="btn btn-info" onclick="generateCustomersReport()">
                                        <i class="fas fa-download me-2"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-download me-2"></i>خيارات التصدير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>تصدير Excel
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="printReport()">
                                <i class="fas fa-print me-2"></i>طباعة التقرير
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-secondary w-100" onclick="emailReport()">
                                <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Generate revenue report
    function generateRevenueReport() {
        showLoadingAlert('جاري إنشاء تقرير الإيرادات...');

        fetch('/api/reports/revenue')
            .then(response => response.json())
            .then(data => {
                hideLoadingAlert();
                if (data.success) {
                    displayRevenueReport(data);
                } else {
                    showAlert('error', data.error || 'فشل في إنشاء تقرير الإيرادات');
                }
            })
            .catch(error => {
                hideLoadingAlert();
                showAlert('error', 'خطأ في الاتصال بالخادم');
                console.error('Error:', error);
            });
    }

    // Generate equipment report
    function generateEquipmentReport() {
        showLoadingAlert('جاري إنشاء تقرير المعدات...');

        fetch('/api/reports/equipment')
            .then(response => response.json())
            .then(data => {
                hideLoadingAlert();
                if (data.success) {
                    displayEquipmentReport(data);
                } else {
                    showAlert('error', data.error || 'فشل في إنشاء تقرير المعدات');
                }
            })
            .catch(error => {
                hideLoadingAlert();
                showAlert('error', 'خطأ في الاتصال بالخادم');
                console.error('Error:', error);
            });
    }

    // Generate customers report
    function generateCustomersReport() {
        showLoadingAlert('جاري إنشاء تقرير العملاء...');

        fetch('/api/reports/customers')
            .then(response => response.json())
            .then(data => {
                hideLoadingAlert();
                if (data.success) {
                    displayCustomersReport(data);
                } else {
                    showAlert('error', data.error || 'فشل في إنشاء تقرير العملاء');
                }
            })
            .catch(error => {
                hideLoadingAlert();
                showAlert('error', 'خطأ في الاتصال بالخادم');
                console.error('Error:', error);
            });
    }

    // Print equipment list
    function printEquipmentList() {
        showLoadingAlert('جاري تحضير قائمة المعدات للطباعة...');

        fetch('/api/reports/print-equipment')
            .then(response => response.json())
            .then(data => {
                hideLoadingAlert();
                if (data.success) {
                    printEquipmentListData(data);
                } else {
                    showAlert('error', data.error || 'فشل في تحضير قائمة المعدات');
                }
            })
            .catch(error => {
                hideLoadingAlert();
                showAlert('error', 'خطأ في الاتصال بالخادم');
                console.error('Error:', error);
            });
    }

    // Export functions
    function exportToPDF() {
        showLoadingAlert('جاري تصدير التقرير إلى PDF...');

        // إنشاء رابط تحميل مخفي
        const link = document.createElement('a');
        link.href = '/api/reports/export/pdf';
        link.download = `تقرير_شامل_${new Date().toISOString().slice(0,10)}.pdf`;
        link.style.display = 'none';

        // إضافة الرابط للصفحة وتفعيله
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // إخفاء رسالة التحميل بعد ثانيتين
        setTimeout(() => {
            hideLoadingAlert();
            showAlert('success', 'تم بدء تحميل ملف PDF');
        }, 2000);
    }

    function exportToExcel() {
        showLoadingAlert('جاري تصدير التقرير إلى Excel...');

        // إنشاء رابط تحميل مخفي
        const link = document.createElement('a');
        link.href = '/api/reports/export/excel';
        link.download = `تقرير_شامل_${new Date().toISOString().slice(0,10)}.xlsx`;
        link.style.display = 'none';

        // إضافة الرابط للصفحة وتفعيله
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // إخفاء رسالة التحميل بعد ثانيتين
        setTimeout(() => {
            hideLoadingAlert();
            showAlert('success', 'تم بدء تحميل ملف Excel');
        }, 2000);
    }

    function printReport() {
        window.print();
    }

    function emailReport() {
        showAlert('info', 'إرسال التقرير بالبريد قيد التطوير - سيتم إضافته قريباً');
    }

    // Display revenue report
    function displayRevenueReport(data) {
        let content = `
            <div class="arabic-text">
                <h4>تقرير الإيرادات</h4>
                <p><strong>إجمالي الإيرادات:</strong> ${data.total_revenue} ريال</p>
                <p><strong>إجمالي الإيجارات:</strong> ${data.total_rentals}</p>
                <h5>الإيرادات الشهرية:</h5>
                <ul>
        `;

        for (const [month, revenue] of Object.entries(data.monthly_revenue)) {
            content += `<li>${month}: ${revenue} ريال</li>`;
        }

        content += `
                </ul>
            </div>
        `;

        showModal('تقرير الإيرادات', content);
    }

    // Display equipment report
    function displayEquipmentReport(data) {
        const stats = data.equipment_stats;
        let content = `
            <div class="arabic-text">
                <h4>تقرير المعدات</h4>
                <div class="row mb-3">
                    <div class="col-md-3"><strong>إجمالي المعدات:</strong> ${stats.total}</div>
                    <div class="col-md-3"><strong>متاحة:</strong> ${stats.available}</div>
                    <div class="col-md-3"><strong>مؤجرة:</strong> ${stats.rented}</div>
                    <div class="col-md-3"><strong>صيانة:</strong> ${stats.maintenance}</div>
                </div>
                <h5>حسب الفئة:</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الفئة</th>
                                <th>الإجمالي</th>
                                <th>متاحة</th>
                                <th>مؤجرة</th>
                                <th>صيانة</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        for (const [category, categoryStats] of Object.entries(stats.by_category)) {
            content += `
                <tr>
                    <td>${category}</td>
                    <td>${categoryStats.total}</td>
                    <td>${categoryStats.available}</td>
                    <td>${categoryStats.rented}</td>
                    <td>${categoryStats.maintenance}</td>
                </tr>
            `;
        }

        content += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        showModal('تقرير المعدات', content);
    }

    // Display customers report
    function displayCustomersReport(data) {
        const stats = data.customer_stats;
        let content = `
            <div class="arabic-text">
                <h4>تقرير العملاء</h4>
                <div class="row mb-3">
                    <div class="col-md-6"><strong>إجمالي العملاء:</strong> ${stats.total_customers}</div>
                    <div class="col-md-6"><strong>العملاء النشطين:</strong> ${stats.active_customers}</div>
                </div>
                <h5>أفضل العملاء:</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>عدد الإيجارات</th>
                                <th>إجمالي المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        stats.top_customers.forEach(customer => {
            content += `
                <tr>
                    <td>${customer.name}</td>
                    <td>${customer.rentals_count}</td>
                    <td>${customer.total_amount} ريال</td>
                </tr>
            `;
        });

        content += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        showModal('تقرير العملاء', content);
    }

    // Print equipment list
    function printEquipmentListData(data) {
        let printContent = `
            <html>
            <head>
                <title>قائمة المعدات</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .category { margin-bottom: 20px; }
                    .category h3 { background: #f8f9fa; padding: 10px; margin: 0; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; }
                    .status-available { color: green; }
                    .status-rented { color: orange; }
                    .status-maintenance { color: red; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>قائمة المعدات</h1>
                    <p>تاريخ الإنشاء: ${data.generated_at}</p>
                    <p>إجمالي المعدات: ${data.total_equipment}</p>
                </div>
        `;

        for (const [category, equipment] of Object.entries(data.equipment_by_category)) {
            printContent += `
                <div class="category">
                    <h3>${category}</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>اسم المعدة</th>
                                <th>الرقم التسلسلي</th>
                                <th>الحالة</th>
                                <th>السعر</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            equipment.forEach(eq => {
                const statusClass = `status-${eq.status.toLowerCase()}`;
                printContent += `
                    <tr>
                        <td>${eq.name}</td>
                        <td>${eq.serial_number || 'غير محدد'}</td>
                        <td class="${statusClass}">${eq.status}</td>
                        <td>${eq.price || 0} ريال</td>
                    </tr>
                `;
            });

            printContent += `
                        </tbody>
                    </table>
                </div>
            `;
        }

        printContent += `
            </body>
            </html>
        `;

        // Open print window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    }

    // Helper functions
    function showLoadingAlert(message) {
        // يمكن تحسين هذا لاحقاً بإضافة spinner
        showAlert('info', message);
    }

    function hideLoadingAlert() {
        // إخفاء رسالة التحميل
    }

    function showModal(title, content) {
        // إنشاء modal بسيط
        const modal = `
            <div class="modal fade" id="reportModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة modal سابق إن وجد
        const existingModal = document.getElementById('reportModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة modal جديد
        document.body.insertAdjacentHTML('beforeend', modal);

        // عرض modal
        const modalElement = new bootstrap.Modal(document.getElementById('reportModal'));
        modalElement.show();
    }

    // Auto-refresh data every 30 seconds
    setInterval(function() {
        // تحديث الإحصائيات
        console.log('تحديث الإحصائيات...');
    }, 30000);

    // Initialize charts (placeholder for future implementation)
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل صفحة التقارير');

        // يمكن إضافة مكتبات الرسوم البيانية هنا مثل Chart.js
        // initializeCharts();
    });
</script>

<style>
    /* تحسين الخطوط العربية للطباعة */
    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

    .progress {
        background-color: #e9ecef;
    }

    .progress-bar {
        background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
        color: white;
        font-weight: bold;
        font-size: 12px;
        font-family: 'Cairo', sans-serif !important;
    }

    .card.border-primary:hover,
    .card.border-success:hover,
    .card.border-info:hover {
        transform: translateY(-2px);
        transition: transform 0.2s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* تحسين النصوص في الجداول */
    .table {
        font-family: 'Cairo', sans-serif !important;
    }

    .table th,
    .table td {
        font-family: 'Cairo', sans-serif !important;
        text-align: right;
        vertical-align: middle;
    }

    /* تحسين Modal */
    .modal-title,
    .modal-body {
        font-family: 'Cairo', sans-serif !important;
        direction: rtl;
        text-align: right;
    }

    /* تحسين الأزرار */
    .btn {
        font-family: 'Cairo', sans-serif !important;
        font-weight: 500;
    }

    /* تحسين الإحصائيات */
    .stat-number,
    .stat-label {
        font-family: 'Cairo', sans-serif !important;
    }

    @media print {
        .btn, .card-header {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        body, * {
            font-family: 'Cairo', Arial, sans-serif !important;
        }
    }
</style>
{% endblock %}
