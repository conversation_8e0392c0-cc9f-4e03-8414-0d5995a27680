#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المخزن
Inventory Management Interface
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
from typing import Dict, List, Optional
from datetime import datetime, date

from inventory_forms import InventoryItemForm, SupplierForm, InventoryMovementForm
from collapsible_frame import CollapsibleFrame

class InventoryManagementTab:
    """تبويب إدارة المخزن"""
    
    def __init__(self, parent, db_manager, user_manager):
        """تهيئة تبويب إدارة المخزن"""
        self.parent = parent
        self.db = db_manager
        self.user_manager = user_manager
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
            self.arabic_font_bold = font.Font(family="Arial Unicode MS", size=10, weight="bold")
            self.arabic_font_small = font.Font(family="Arial Unicode MS", size=9)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
            self.arabic_font_bold = font.Font(family="Arial", size=10, weight="bold")
            self.arabic_font_small = font.Font(family="Arial", size=9)
        
        # متغيرات البحث والفلترة
        self.search_var = tk.StringVar()
        self.category_filter_var = tk.StringVar()
        self.stock_filter_var = tk.StringVar()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات الأولية
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المخزن"""
        # الإطار الرئيسي
        self.main_frame = ttk.Frame(self.parent)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        
        # إطار المحتوى الرئيسي
        content_frame = ttk.Frame(self.main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # تقسيم الواجهة إلى أقسام قابلة للطي
        self.create_collapsible_sections(content_frame)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = ttk.Frame(self.main_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # العنوان
        title_label = ttk.Label(toolbar, text="🏪 إدارة المخزن", 
                               font=self.arabic_font_bold)
        title_label.pack(side=tk.LEFT)
        
        # أزرار سريعة
        buttons_frame = ttk.Frame(toolbar)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(buttons_frame, text="إضافة قطعة غيار", 
                  command=self.add_inventory_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="إضافة مورد", 
                  command=self.add_supplier).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="حركة مخزون", 
                  command=self.add_inventory_movement).pack(side=tk.LEFT, padx=2)
        ttk.Button(buttons_frame, text="تحديث", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=2)
    
    def create_collapsible_sections(self, parent):
        """إنشاء الأقسام القابلة للطي"""
        
        # قسم قطع الغيار والإكسسوارات
        self.inventory_section = CollapsibleFrame(
            parent, "📦 قطع الغيار والإكسسوارات", self.arabic_font_bold
        )
        self.inventory_section.pack(fill=tk.BOTH, expand=True, pady=2)
        self.create_inventory_items_section(self.inventory_section.content_frame)
        
        # قسم الموردين
        self.suppliers_section = CollapsibleFrame(
            parent, "🏢 الموردين", self.arabic_font_bold
        )
        self.suppliers_section.pack(fill=tk.X, pady=2)
        self.create_suppliers_section(self.suppliers_section.content_frame)
        
        # قسم حركة المخزون
        self.movements_section = CollapsibleFrame(
            parent, "📊 حركة المخزون", self.arabic_font_bold
        )
        self.movements_section.pack(fill=tk.X, pady=2)
        self.create_movements_section(self.movements_section.content_frame)
        
        # قسم التنبيهات والتقارير
        self.alerts_section = CollapsibleFrame(
            parent, "⚠️ التنبيهات والتقارير", self.arabic_font_bold
        )
        self.alerts_section.pack(fill=tk.X, pady=2)
        self.create_alerts_section(self.alerts_section.content_frame)
    
    def create_inventory_items_section(self, parent):
        """إنشاء قسم قطع الغيار"""
        # شريط البحث والفلترة
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # البحث
        ttk.Label(search_frame, text="🔍", font=self.arabic_font).pack(side=tk.LEFT)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, 
                                font=self.arabic_font, width=25)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر الفئة
        ttk.Label(search_frame, text="الفئة:", font=self.arabic_font).pack(side=tk.LEFT)
        self.category_filter_combo = ttk.Combobox(search_frame, textvariable=self.category_filter_var,
                                                 state="readonly", width=15)
        self.category_filter_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.category_filter_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # فلتر المخزون
        ttk.Label(search_frame, text="المخزون:", font=self.arabic_font).pack(side=tk.LEFT)
        stock_filter_combo = ttk.Combobox(search_frame, textvariable=self.stock_filter_var,
                                         state="readonly", width=15)
        stock_filter_combo['values'] = ["الكل", "مخزون منخفض", "نفد المخزون", "متوفر"]
        stock_filter_combo.set("الكل")
        stock_filter_combo.pack(side=tk.LEFT, padx=(5, 10))
        stock_filter_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(search_frame)
        actions_frame.pack(side=tk.RIGHT)
        
        ttk.Button(actions_frame, text="تعديل", 
                  command=self.edit_inventory_item).pack(side=tk.LEFT, padx=2)
        ttk.Button(actions_frame, text="حذف", 
                  command=self.delete_inventory_item).pack(side=tk.LEFT, padx=2)
        
        # جدول قطع الغيار
        self.create_inventory_tree(parent)
    
    def create_inventory_tree(self, parent):
        """إنشاء جدول قطع الغيار"""
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # تعريف الأعمدة
        columns = ("id", "name", "category", "brand", "model", "current_stock", 
                  "min_stock", "unit_price", "rental_price", "supplier")
        
        self.inventory_tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=12)
        
        # تكوين الأعمدة
        column_headers = {
            "id": "المعرف",
            "name": "اسم القطعة",
            "category": "الفئة",
            "brand": "العلامة التجارية",
            "model": "الموديل",
            "current_stock": "المخزون الحالي",
            "min_stock": "الحد الأدنى",
            "unit_price": "سعر الشراء",
            "rental_price": "سعر الإيجار",
            "supplier": "المورد"
        }
        
        column_widths = {
            "id": 60,
            "name": 150,
            "category": 100,
            "brand": 100,
            "model": 100,
            "current_stock": 80,
            "min_stock": 80,
            "unit_price": 80,
            "rental_price": 80,
            "supplier": 120
        }
        
        for col in columns:
            self.inventory_tree.heading(col, text=column_headers[col])
            self.inventory_tree.column(col, width=column_widths[col], minwidth=50)
        
        # شريط التمرير
        inventory_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL,
                                           command=self.inventory_tree.yview)
        self.inventory_tree.configure(yscrollcommand=inventory_scrollbar.set)
        
        self.inventory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        inventory_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج
        self.inventory_tree.bind('<Double-1>', lambda e: self.edit_inventory_item())
    
    def create_suppliers_section(self, parent):
        """إنشاء قسم الموردين"""
        # أزرار الإجراءات
        actions_frame = ttk.Frame(parent)
        actions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(actions_frame, text="تعديل مورد", 
                  command=self.edit_supplier).pack(side=tk.LEFT, padx=2)
        ttk.Button(actions_frame, text="حذف مورد", 
                  command=self.delete_supplier).pack(side=tk.LEFT, padx=2)
        
        # جدول الموردين
        suppliers_frame = ttk.Frame(parent)
        suppliers_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        supplier_columns = ("id", "name", "contact_person", "phone", "email")
        self.suppliers_tree = ttk.Treeview(suppliers_frame, columns=supplier_columns, 
                                          show="headings", height=6)
        
        supplier_headers = {
            "id": "المعرف",
            "name": "اسم المورد",
            "contact_person": "الشخص المسؤول",
            "phone": "الهاتف",
            "email": "البريد الإلكتروني"
        }
        
        for col in supplier_columns:
            self.suppliers_tree.heading(col, text=supplier_headers[col])
            self.suppliers_tree.column(col, width=120)
        
        suppliers_scrollbar = ttk.Scrollbar(suppliers_frame, orient=tk.VERTICAL,
                                           command=self.suppliers_tree.yview)
        self.suppliers_tree.configure(yscrollcommand=suppliers_scrollbar.set)
        
        self.suppliers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        suppliers_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.suppliers_tree.bind('<Double-1>', lambda e: self.edit_supplier())
    
    def create_movements_section(self, parent):
        """إنشاء قسم حركة المخزون"""
        # فلتر التاريخ
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(filter_frame, text="من تاريخ:", font=self.arabic_font).pack(side=tk.LEFT)
        self.start_date_var = tk.StringVar(value=date.today().replace(day=1).isoformat())
        ttk.Entry(filter_frame, textvariable=self.start_date_var, width=12).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(filter_frame, text="إلى تاريخ:", font=self.arabic_font).pack(side=tk.LEFT)
        self.end_date_var = tk.StringVar(value=date.today().isoformat())
        ttk.Entry(filter_frame, textvariable=self.end_date_var, width=12).pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(filter_frame, text="فلترة", 
                  command=self.filter_movements).pack(side=tk.LEFT, padx=5)
        
        # جدول حركة المخزون
        movements_frame = ttk.Frame(parent)
        movements_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        movement_columns = ("id", "item_name", "movement_type", "quantity", 
                           "unit_cost", "total_cost", "movement_date")
        self.movements_tree = ttk.Treeview(movements_frame, columns=movement_columns, 
                                          show="headings", height=8)
        
        movement_headers = {
            "id": "المعرف",
            "item_name": "القطعة",
            "movement_type": "نوع الحركة",
            "quantity": "الكمية",
            "unit_cost": "سعر الوحدة",
            "total_cost": "الإجمالي",
            "movement_date": "التاريخ"
        }
        
        for col in movement_columns:
            self.movements_tree.heading(col, text=movement_headers[col])
            self.movements_tree.column(col, width=100)
        
        movements_scrollbar = ttk.Scrollbar(movements_frame, orient=tk.VERTICAL,
                                           command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=movements_scrollbar.set)
        
        self.movements_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        movements_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_alerts_section(self, parent):
        """إنشاء قسم التنبيهات والتقارير"""
        # إحصائيات سريعة
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.stats_labels = {}
        stats_info = [
            ("total_items", "إجمالي القطع"),
            ("low_stock_items", "مخزون منخفض"),
            ("out_of_stock", "نفد المخزون"),
            ("total_value", "قيمة المخزون")
        ]
        
        for i, (key, label) in enumerate(stats_info):
            frame = ttk.Frame(stats_frame)
            frame.pack(side=tk.LEFT, padx=10)
            
            ttk.Label(frame, text=label, font=self.arabic_font).pack()
            self.stats_labels[key] = ttk.Label(frame, text="0", font=self.arabic_font_bold)
            self.stats_labels[key].pack()
        
        # قائمة التنبيهات
        alerts_frame = ttk.LabelFrame(parent, text="تنبيهات المخزون المنخفض")
        alerts_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.alerts_listbox = tk.Listbox(alerts_frame, font=self.arabic_font, height=6)
        alerts_scrollbar = ttk.Scrollbar(alerts_frame, orient=tk.VERTICAL,
                                        command=self.alerts_listbox.yview)
        self.alerts_listbox.configure(yscrollcommand=alerts_scrollbar.set)
        
        self.alerts_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def load_data(self):
        """تحميل جميع البيانات"""
        self.load_categories()
        self.load_inventory_items()
        self.load_suppliers()
        self.load_movements()
        self.update_statistics()
        self.update_alerts()

    def load_categories(self):
        """تحميل فئات المخزون"""
        try:
            categories = self.db.get_inventory_categories()
            category_names = ["الكل"] + [cat['name'] for cat in categories]
            self.category_filter_combo['values'] = category_names
            self.category_filter_combo.set("الكل")
        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")

    def load_inventory_items(self):
        """تحميل قطع الغيار"""
        try:
            # مسح البيانات السابقة
            for item in self.inventory_tree.get_children():
                self.inventory_tree.delete(item)

            # تطبيق الفلاتر
            items = self.get_filtered_items()

            # إضافة البيانات الجديدة
            for item in items:
                # تحديد لون الصف حسب مستوى المخزون
                tags = []
                current_stock = item.get('current_stock', 0)
                min_stock = item.get('min_stock_level', 0)

                if current_stock == 0:
                    tags.append('out_of_stock')
                elif current_stock <= min_stock:
                    tags.append('low_stock')

                values = (
                    item.get('id', ''),
                    item.get('name', ''),
                    item.get('category_name', ''),
                    item.get('brand', ''),
                    item.get('model', ''),
                    item.get('current_stock', 0),
                    item.get('min_stock_level', 0),
                    f"{item.get('unit_price', 0):.2f}",
                    f"{item.get('rental_price', 0):.2f}",
                    item.get('supplier_name', '')
                )

                self.inventory_tree.insert("", tk.END, values=values, tags=tags)

            # تكوين ألوان الصفوف
            self.inventory_tree.tag_configure('low_stock', background='#fff2cc')
            self.inventory_tree.tag_configure('out_of_stock', background='#ffcccc')

        except Exception as e:
            print(f"خطأ في تحميل قطع الغيار: {e}")

    def get_filtered_items(self) -> List[Dict]:
        """الحصول على قطع الغيار مع تطبيق الفلاتر"""
        try:
            # البحث النصي
            search_term = self.search_var.get().strip()
            if search_term:
                items = self.db.search_inventory_items(search_term)
            else:
                items = self.db.get_inventory_items()

            # فلتر الفئة
            category_filter = self.category_filter_var.get()
            if category_filter and category_filter != "الكل":
                items = [item for item in items if item.get('category_name') == category_filter]

            # فلتر المخزون
            stock_filter = self.stock_filter_var.get()
            if stock_filter == "مخزون منخفض":
                items = [item for item in items
                        if item.get('current_stock', 0) <= item.get('min_stock_level', 0)
                        and item.get('current_stock', 0) > 0]
            elif stock_filter == "نفد المخزون":
                items = [item for item in items if item.get('current_stock', 0) == 0]
            elif stock_filter == "متوفر":
                items = [item for item in items
                        if item.get('current_stock', 0) > item.get('min_stock_level', 0)]

            return items
        except Exception as e:
            print(f"خطأ في فلترة قطع الغيار: {e}")
            return []

    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            # مسح البيانات السابقة
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # تحميل البيانات الجديدة
            suppliers = self.db.get_suppliers()
            for supplier in suppliers:
                values = (
                    supplier.get('id', ''),
                    supplier.get('name', ''),
                    supplier.get('contact_person', ''),
                    supplier.get('phone', ''),
                    supplier.get('email', '')
                )
                self.suppliers_tree.insert("", tk.END, values=values)

        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")

    def load_movements(self):
        """تحميل حركة المخزون"""
        try:
            # مسح البيانات السابقة
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            # تحميل البيانات الجديدة
            start_date = self.start_date_var.get()
            end_date = self.end_date_var.get()

            movements = self.db.get_inventory_movements(
                start_date=start_date, end_date=end_date
            )

            for movement in movements:
                # ترجمة نوع الحركة
                movement_type_translations = {
                    'Purchase': 'شراء',
                    'Sale': 'بيع',
                    'Rental': 'إيجار',
                    'Return': 'إرجاع',
                    'Adjustment': 'تعديل',
                    'Damage': 'تلف',
                    'Loss': 'فقدان'
                }

                movement_type = movement_type_translations.get(
                    movement.get('movement_type', ''), movement.get('movement_type', '')
                )

                values = (
                    movement.get('id', ''),
                    movement.get('item_name', ''),
                    movement_type,
                    movement.get('quantity', 0),
                    f"{movement.get('unit_cost', 0):.2f}" if movement.get('unit_cost') else '',
                    f"{movement.get('total_cost', 0):.2f}" if movement.get('total_cost') else '',
                    movement.get('movement_date', '')
                )
                self.movements_tree.insert("", tk.END, values=values)

        except Exception as e:
            print(f"خطأ في تحميل حركة المخزون: {e}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            items = self.db.get_inventory_items()

            total_items = len(items)
            low_stock_items = len([item for item in items
                                 if item.get('current_stock', 0) <= item.get('min_stock_level', 0)
                                 and item.get('current_stock', 0) > 0])
            out_of_stock = len([item for item in items if item.get('current_stock', 0) == 0])

            total_value = sum(item.get('current_stock', 0) * item.get('unit_price', 0)
                            for item in items)

            self.stats_labels['total_items'].config(text=str(total_items))
            self.stats_labels['low_stock_items'].config(text=str(low_stock_items))
            self.stats_labels['out_of_stock'].config(text=str(out_of_stock))
            self.stats_labels['total_value'].config(text=f"{total_value:.2f}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_alerts(self):
        """تحديث التنبيهات"""
        try:
            self.alerts_listbox.delete(0, tk.END)

            low_stock_items = self.db.get_low_stock_items()

            for item in low_stock_items:
                current_stock = item.get('current_stock', 0)
                min_stock = item.get('min_stock_level', 0)

                if current_stock == 0:
                    alert_text = f"⛔ {item.get('name', '')} - نفد المخزون"
                else:
                    alert_text = f"⚠️ {item.get('name', '')} - مخزون منخفض ({current_stock}/{min_stock})"

                self.alerts_listbox.insert(tk.END, alert_text)

        except Exception as e:
            print(f"خطأ في تحديث التنبيهات: {e}")

    # معالجات الأحداث
    def on_search_change(self, event=None):
        """معالج تغيير البحث"""
        self.load_inventory_items()

    def on_filter_change(self, event=None):
        """معالج تغيير الفلاتر"""
        self.load_inventory_items()

    def filter_movements(self):
        """فلترة حركة المخزون"""
        self.load_movements()

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.load_data()
        messagebox.showinfo("تم", "تم تحديث البيانات بنجاح")

    # وظائف إدارة قطع الغيار
    def add_inventory_item(self):
        """إضافة قطعة غيار جديدة"""
        try:
            categories = self.db.get_inventory_categories()
            suppliers = self.db.get_suppliers()

            form = InventoryItemForm(
                self.main_frame, self.db, categories, suppliers,
                callback=self.load_inventory_items
            )
            form.show()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نموذج إضافة قطعة الغيار: {str(e)}")

    def edit_inventory_item(self):
        """تعديل قطعة غيار"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قطعة غيار للتعديل")
            return

        try:
            item_id = self.inventory_tree.item(selection[0])['values'][0]
            item_data = self.db.get_inventory_items(item_id=item_id)

            if not item_data:
                messagebox.showerror("خطأ", "لم يتم العثور على قطعة الغيار")
                return

            categories = self.db.get_inventory_categories()
            suppliers = self.db.get_suppliers()

            form = InventoryItemForm(
                self.main_frame, self.db, categories, suppliers,
                item_data=item_data[0], callback=self.load_inventory_items
            )
            form.show()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تعديل قطعة الغيار: {str(e)}")

    def delete_inventory_item(self):
        """حذف قطعة غيار"""
        selection = self.inventory_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار قطعة غيار للحذف")
            return

        try:
            item_id = self.inventory_tree.item(selection[0])['values'][0]
            item_name = self.inventory_tree.item(selection[0])['values'][1]

            if messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف قطعة الغيار '{item_name}'؟"):
                if self.db.delete_inventory_item(item_id):
                    messagebox.showinfo("نجح", "تم حذف قطعة الغيار بنجاح")
                    self.load_inventory_items()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف قطعة الغيار")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف قطعة الغيار: {str(e)}")

    # وظائف إدارة الموردين
    def add_supplier(self):
        """إضافة مورد جديد"""
        try:
            form = SupplierForm(self.main_frame, self.db, callback=self.load_suppliers)
            form.show()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نموذج إضافة المورد: {str(e)}")

    def edit_supplier(self):
        """تعديل مورد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return

        try:
            supplier_id = self.suppliers_tree.item(selection[0])['values'][0]
            supplier_data = self.db.get_suppliers(supplier_id=supplier_id)

            if not supplier_data:
                messagebox.showerror("خطأ", "لم يتم العثور على المورد")
                return

            form = SupplierForm(
                self.main_frame, self.db, supplier_data=supplier_data[0],
                callback=self.load_suppliers
            )
            form.show()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تعديل المورد: {str(e)}")

    def delete_supplier(self):
        """حذف مورد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return

        try:
            supplier_id = self.suppliers_tree.item(selection[0])['values'][0]
            supplier_name = self.suppliers_tree.item(selection[0])['values'][1]

            if messagebox.askyesno("تأكيد الحذف",
                                 f"هل أنت متأكد من حذف المورد '{supplier_name}'؟"):
                if self.db.delete_supplier(supplier_id):
                    messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                    self.load_suppliers()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف المورد")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف المورد: {str(e)}")

    # وظائف حركة المخزون
    def add_inventory_movement(self):
        """إضافة حركة مخزون"""
        try:
            items = self.db.get_inventory_items()
            suppliers = self.db.get_suppliers()

            form = InventoryMovementForm(
                self.main_frame, self.db, items, suppliers,
                callback=lambda: (self.load_movements(), self.load_inventory_items(), self.update_statistics())
            )
            form.show()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نموذج حركة المخزون: {str(e)}")

    def pack(self, **kwargs):
        """تطبيق pack على الإطار الرئيسي"""
        self.main_frame.pack(**kwargs)

    def grid(self, **kwargs):
        """تطبيق grid على الإطار الرئيسي"""
        self.main_frame.grid(**kwargs)
