# -*- coding: utf-8 -*-
"""
مساعد قاعدة البيانات - أسماء الأعمدة الصحيحة
Database Helper - Correct Column Names
"""

class DatabaseColumns:
    """أسماء الأعمدة الصحيحة في قاعدة البيانات"""
    
    # جدول rentals
    RENTALS = {
        'id': 'id',
        'customer_id': 'customer_id',
        'equipment_id': 'equipment_id',
        'rental_date': 'rental_date',  # ليس start_date
        'return_date': 'return_date',  # ليس end_date
        'price': 'price',              # ليس total_amount
        'status': 'status',
        'actual_return_date': 'actual_return_date',
        'created_date': 'created_date', # ليس created_at
        'notes': 'notes',
        'rental_group_id': 'rental_group_id',
        'invoice_number': 'invoice_number',
        'rental_number': 'rental_number'
    }
    
    # جدول customers
    CUSTOMERS = {
        'id': 'id',
        'name': 'name',
        'phone': 'phone',
        'national_id': 'national_id',
        'photo_path': 'photo_path',
        'id_photo_path': 'id_photo_path',
        'created_date': 'created_date',
        'email': 'email',
        'address': 'address'
    }
    
    # جدول equipment
    EQUIPMENT = {
        'id': 'id',
        'name': 'name',
        'model': 'model',
        'serial_number': 'serial_number',
        'category': 'category',
        'price': 'price',
        'daily_rental_price': 'daily_rental_price',
        'status': 'status',
        'description': 'description',
        'created_at': 'created_at',
        'updated_at': 'updated_at',
        'source_type': 'source_type',
        'supplier_id': 'supplier_id'
    }
    
    # جدول rental_groups
    RENTAL_GROUPS = {
        'id': 'id',
        'customer_id': 'customer_id',
        'rental_date': 'rental_date',
        'return_date': 'return_date',
        'total_price': 'total_price',
        'status': 'status',
        'notes': 'notes',
        'created_by': 'created_by',
        'created_date': 'created_date',
        'updated_date': 'updated_date',
        'invoice_number': 'invoice_number',
        'rental_number': 'rental_number',
        'discount_amount': 'discount_amount',
        'paid_amount': 'paid_amount',
        'remaining_amount': 'remaining_amount',
        'payment_status': 'payment_status',
        'actual_return_date': 'actual_return_date'
    }

def get_safe_query(table, columns=None):
    """إنشاء استعلام آمن مع أسماء الأعمدة الصحيحة"""
    if columns is None:
        return f"SELECT * FROM {table}"
    
    if isinstance(columns, list):
        return f"SELECT {', '.join(columns)} FROM {table}"
    
    return f"SELECT {columns} FROM {table}"

# أمثلة على الاستعلامات الصحيحة
SAFE_QUERIES = {
    'active_rentals': """
        SELECT COUNT(*) FROM rentals 
        WHERE status = 'active'
    """,
    
    'overdue_rentals': """
        SELECT COUNT(*) FROM rentals 
        WHERE status = 'active' AND return_date < date('now')
    """,
    
    'recent_rentals': """
        SELECT r.id, c.name as customer_name, r.rental_date, r.return_date, r.price, r.status
        FROM rentals r
        JOIN customers c ON r.customer_id = c.id
        ORDER BY r.created_date DESC
        LIMIT 5
    """,
    
    'customer_rentals': """
        SELECT r.*, 
               GROUP_CONCAT(e.name) as equipment_names
        FROM rentals r
        LEFT JOIN rental_items ri ON r.id = ri.rental_id
        LEFT JOIN equipment e ON ri.equipment_id = e.id
        WHERE r.customer_id = ?
        GROUP BY r.id
        ORDER BY r.created_date DESC
    """
}
