{% extends "base.html" %}

{% block title %}إدارة المعدات - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-camera me-2 text-primary"></i>
                        إدارة المعدات والمخزون
                    </h1>
                    <p class="text-muted mb-0">إدارة وتتبع جميع المعدات والإكسسوارات</p>
                </div>
                <div>
                    <div class="btn-group me-2" role="group">
                        <button class="btn btn-outline-secondary" onclick="window.location.href='{{ url_for('inventory') }}'">
                            <i class="fas fa-boxes me-2"></i>لوحة المخزون
                        </button>
                        <button class="btn btn-outline-info" onclick="window.location.href='{{ url_for('inventory_items') }}'">
                            <i class="fas fa-list me-2"></i>عناصر المخزون
                        </button>
                    </div>
                    <button class="btn btn-primary" onclick="showAddEquipmentModal()">
                        <i class="fas fa-plus me-2"></i>إضافة معدة جديدة
                    </button>
                    <button class="btn btn-success ms-2" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">فلتر حسب الفئة:</label>
                            <select class="form-select" id="categoryFilter" onchange="filterEquipment()">
                                <option value="">جميع الفئات</option>
                                {% for category in categories %}
                                <option value="{{ category }}">{{ category }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">فلتر حسب الحالة:</label>
                            <select class="form-select" id="statusFilter" onchange="filterEquipment()">
                                <option value="">جميع الحالات</option>
                                {% for status in statuses %}
                                <option value="{{ status.name }}">{{ status.display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">البحث:</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن معدة..." onkeyup="filterEquipment()">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-secondary w-100" onclick="clearFilters()">
                                <i class="fas fa-times me-2"></i>مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Equipment Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>قائمة المعدات
                        <span class="badge bg-primary ms-2" id="equipmentCount">{{ equipment|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="equipmentTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم المعدة</th>
                                    <th>الموديل</th>
                                    <th>الرقم التسلسلي</th>
                                    <th>الفئة</th>
                                    <th>المصدر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for eq in equipment %}
                                <tr data-category="{{ eq.category }}" data-status="{{ eq.status }}">
                                    <td>{{ eq.id }}</td>
                                    <td>
                                        <strong>{{ eq.name }}</strong>
                                    </td>
                                    <td>{{ eq.model }}</td>
                                    <td>
                                        <code>{{ eq.serial_number }}</code>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ eq.category }}</span>
                                    </td>
                                    <td>
                                        {% if eq.source == 'external' %}
                                        <span class="badge bg-warning" title="{{ eq.supplier }}">
                                            <i class="fas fa-external-link-alt"></i> خارجي
                                        </span>
                                        {% else %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-home"></i> داخلي
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if eq.status == 'Available' %}
                                        <span class="badge bg-success">متاح</span>
                                        {% elif eq.status == 'Rented' %}
                                        <span class="badge bg-warning">مؤجر</span>
                                        {% elif eq.status == 'Maintenance' %}
                                        <span class="badge bg-secondary">صيانة</span>
                                        {% else %}
                                        <span class="badge bg-dark">{{ eq.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if eq.source == 'external' %}
                                        <span class="badge bg-secondary">معدة خارجية</span>
                                        <small class="text-muted d-block">{{ eq.supplier }}</small>
                                        {% else %}
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editEquipment({{ eq.id }}, '{{ eq.name }}', '{{ eq.model }}', '{{ eq.serial_number }}', '{{ eq.category }}', '{{ eq.status }}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteEquipment({{ eq.id }}, '{{ eq.name }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="viewEquipment({{ eq.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Equipment Modal -->
<div class="modal fade" id="addEquipmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة معدة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addEquipmentForm">
                    <div class="mb-3">
                        <label class="form-label">اسم المعدة:</label>
                        <input type="text" class="form-control" id="equipmentName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الموديل:</label>
                        <input type="text" class="form-control" id="equipmentModel" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرقم التسلسلي:</label>
                        <input type="text" class="form-control" id="equipmentSerial" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الفئة:</label>
                        <select class="form-select" id="equipmentCategory" required>
                            <option value="">اختر الفئة</option>
                            <option value="كاميرات">كاميرات</option>
                            <option value="عدسات">عدسات</option>
                            <option value="معدات إضاءة">معدات إضاءة</option>
                            <option value="ملحقات">ملحقات</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الحالة:</label>
                        <select class="form-select" id="equipmentStatus" required>
                            <option value="Available">متاح</option>
                            <option value="Rented">مؤجر</option>
                            <option value="Maintenance">صيانة</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveEquipment()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Equipment Modal -->
<div class="modal fade" id="editEquipmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>تعديل المعدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editEquipmentForm">
                    <input type="hidden" id="editEquipmentId">

                    <!-- معلومات أساسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag me-1"></i>اسم المعدة:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="editEquipmentName" required
                                   placeholder="أدخل اسم المعدة">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-cube me-1"></i>الموديل:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="editEquipmentModel" required
                                   placeholder="أدخل موديل المعدة">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-barcode me-1"></i>الرقم التسلسلي:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="editEquipmentSerial" required
                                   placeholder="أدخل الرقم التسلسلي">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-list me-1"></i>الفئة:
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="editEquipmentCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="كاميرات">📷 كاميرات</option>
                                <option value="عدسات">🔍 عدسات</option>
                                <option value="معدات إضاءة">💡 معدات إضاءة</option>
                                <option value="ملحقات">🔧 ملحقات</option>
                                <option value="حوامل">📐 حوامل وترايبود</option>
                                <option value="بطاريات">🔋 بطاريات وشواحن</option>
                                <option value="ذاكرة">💾 كروت ذاكرة</option>
                                <option value="كابلات">🔌 كابلات وأسلاك</option>
                            </select>
                        </div>
                    </div>

                    <!-- حالة المعدة -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-check-circle me-2"></i>حالة المعدة
                            </h6>
                        </div>
                        <div class="col-md-12 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-flag me-1"></i>الحالة:
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="editEquipmentStatus" required>
                                <option value="Available">✅ متاح</option>
                                <option value="Rented">🔄 مؤجر</option>
                                <option value="Maintenance">🔧 تحت الصيانة</option>
                                <option value="Damaged">❌ معطل</option>
                                <option value="Lost">❓ مفقود</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="updateEquipment()">
                    <i class="fas fa-save me-1"></i>حفظ التعديلات
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filter equipment
    function filterEquipment() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const searchInput = document.getElementById('searchInput').value.toLowerCase();
        
        const rows = document.querySelectorAll('#equipmentTable tbody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const category = row.dataset.category;
            const status = row.dataset.status;
            const text = row.textContent.toLowerCase();
            
            const categoryMatch = !categoryFilter || category === categoryFilter;
            const statusMatch = !statusFilter || status === statusFilter;
            const searchMatch = !searchInput || text.includes(searchInput);
            
            if (categoryMatch && statusMatch && searchMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        document.getElementById('equipmentCount').textContent = visibleCount;
    }
    
    // Clear filters
    function clearFilters() {
        document.getElementById('categoryFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('searchInput').value = '';
        filterEquipment();
    }
    
    // Show add equipment modal
    function showAddEquipmentModal() {
        const modal = new bootstrap.Modal(document.getElementById('addEquipmentModal'));
        modal.show();
    }
    
    // Save equipment
    function saveEquipment() {
        const data = {
            name: document.getElementById('equipmentName').value,
            model: document.getElementById('equipmentModel').value,
            serial_number: document.getElementById('equipmentSerial').value,
            category: document.getElementById('equipmentCategory').value,
            status: document.getElementById('equipmentStatus').value
        };
        
        if (!data.name || !data.model || !data.serial_number || !data.category || !data.status) {
            showAlert('danger', 'يرجى ملء جميع الحقول');
            return;
        }
        
        fetch('/api/equipment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('addEquipmentModal')).hide();
                location.reload();
            } else {
                showAlert('danger', data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في إضافة المعدة');
        });
    }
    
    // Edit equipment
    function editEquipment(id, name, model, serial, category, status) {
        document.getElementById('editEquipmentId').value = id;
        document.getElementById('editEquipmentName').value = name;
        document.getElementById('editEquipmentModel').value = model;
        document.getElementById('editEquipmentSerial').value = serial;
        document.getElementById('editEquipmentCategory').value = category;
        document.getElementById('editEquipmentStatus').value = status;

        const modal = new bootstrap.Modal(document.getElementById('editEquipmentModal'));
        modal.show();
    }

    // Update equipment
    function updateEquipment() {
        const id = document.getElementById('editEquipmentId').value;
        const data = {
            name: document.getElementById('editEquipmentName').value,
            model: document.getElementById('editEquipmentModel').value,
            serial_number: document.getElementById('editEquipmentSerial').value,
            category: document.getElementById('editEquipmentCategory').value,
            status: document.getElementById('editEquipmentStatus').value
        };

        if (!data.name || !data.model || !data.serial_number || !data.category || !data.status) {
            showAlert('danger', 'يرجى ملء جميع الحقول');
            return;
        }

        fetch(`/api/equipment/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('editEquipmentModal')).hide();
                location.reload();
            } else {
                showAlert('danger', data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في تعديل المعدة');
        });
    }

    // Delete equipment
    function deleteEquipment(id, name) {
        if (confirm(`هل أنت متأكد من حذف المعدة "${name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
            fetch(`/api/equipment/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    location.reload();
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ في حذف المعدة');
            });
        }
    }

    // Load equipment data dynamically
    function loadEquipmentData() {
        fetch('/api/equipment/all')
            .then(response => response.json())
            .then(data => {
                if (data.equipment) {
                    console.log(`تم تحميل ${data.count} معدة`);
                    updateEquipmentTable(data.equipment);
                } else {
                    console.error('خطأ في تحميل المعدات:', data.error);
                    showAlert('danger', 'فشل في تحميل المعدات: ' + (data.error || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('خطأ في الشبكة:', error);
                showAlert('danger', 'خطأ في الاتصال بالخادم');
            });
    }

    // Update equipment table
    function updateEquipmentTable(equipment) {
        const tbody = document.querySelector('#equipmentTable tbody');
        if (!tbody) {
            console.error('لم يتم العثور على جدول المعدات');
            return;
        }

        tbody.innerHTML = '';

        equipment.forEach(eq => {
            const statusBadge = getStatusBadge(eq.status);
            const row = `
                <tr data-category="${eq.category}" data-status="${eq.status}">
                    <td>${eq.id}</td>
                    <td><strong>${eq.name}</strong></td>
                    <td>${eq.model}</td>
                    <td><code>${eq.serial_number}</code></td>
                    <td><span class="badge bg-info">${eq.category}</span></td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewEquipment(${eq.id})" title="عرض">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editEquipment(${eq.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteEquipment(${eq.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    }

    // Get status badge HTML
    function getStatusBadge(status) {
        switch(status) {
            case 'Available':
                return '<span class="badge bg-success">متاح</span>';
            case 'Rented':
                return '<span class="badge bg-warning">مؤجر</span>';
            case 'Maintenance':
                return '<span class="badge bg-secondary">صيانة</span>';
            case 'Damaged':
                return '<span class="badge bg-danger">تالف</span>';
            case 'Lost':
                return '<span class="badge bg-dark">مفقود</span>';
            default:
                return '<span class="badge bg-light text-dark">غير محدد</span>';
        }
    }

    // View equipment details
    function viewEquipment(id) {
        fetch(`/api/equipment/${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const eq = data.equipment;
                    const details = `
                        <strong>اسم المعدة:</strong> ${eq.name}<br>
                        <strong>الموديل:</strong> ${eq.model}<br>
                        <strong>الرقم التسلسلي:</strong> ${eq.serial_number}<br>
                        <strong>الفئة:</strong> ${eq.category}<br>
                        <strong>الحالة:</strong> ${eq.status}<br>
                        <strong>تاريخ الإضافة:</strong> ${eq.created_date}
                    `;

                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <h6><i class="fas fa-info-circle me-2"></i>تفاصيل المعدة</h6>
                        ${details}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;

                    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ في عرض تفاصيل المعدة');
            });
    }

    // Load data when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadEquipmentData();
    });
</script>
{% endblock %}
