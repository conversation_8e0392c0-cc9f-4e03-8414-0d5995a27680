{% extends "base.html" %}

{% block title %}إعدادات الشركة{% endblock %}

{% block extra_css %}
<style>
    .logo-preview {
        max-width: 200px;
        max-height: 200px;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background-color: #f8f9fa;
    }
    
    .logo-preview img {
        max-width: 100%;
        max-height: 150px;
        object-fit: contain;
    }
    
    .settings-card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }
    
    .color-input {
        width: 60px;
        height: 40px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .form-section {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-building me-2 text-primary"></i>إعدادات الشركة
                    </h2>
                    <p class="text-muted mb-0">إدارة معلومات الشركة والهوية البصرية</p>
                </div>
                <div>
                    <button class="btn btn-success me-2" onclick="saveAllSettings()">
                        <i class="fas fa-save me-2"></i>حفظ جميع التغييرات
                    </button>
                    <button class="btn btn-outline-secondary" onclick="resetSettings()">
                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- معلومات الشركة الأساسية -->
        <div class="col-lg-8">
            <div class="card settings-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات الشركة الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <form id="companyInfoForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الشركة:</label>
                                <input type="text" class="form-control" id="companyName" 
                                       value="{{ settings.get('company_name', '') }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني:</label>
                                <input type="email" class="form-control" id="companyEmail" 
                                       value="{{ settings.get('company_email', '') }}">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف الأول:</label>
                                <input type="tel" class="form-control" id="companyPhone" 
                                       value="{{ settings.get('company_phone', '') }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف الثاني:</label>
                                <input type="tel" class="form-control" id="companyPhone2" 
                                       value="{{ settings.get('company_phone2', '') }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">عنوان الشركة:</label>
                            <textarea class="form-control" id="companyAddress" rows="3">{{ settings.get('company_address', '') }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع الإلكتروني:</label>
                                <input type="url" class="form-control" id="companyWebsite" 
                                       value="{{ settings.get('company_website', '') }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رمز العملة:</label>
                                <input type="text" class="form-control" id="currencySymbol" 
                                       value="{{ settings.get('currency_symbol', 'ج.م') }}">
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إعدادات التقارير -->
            <div class="card settings-card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>إعدادات التقارير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">لون رأس التقارير:</label>
                            <div class="d-flex align-items-center">
                                <input type="color" class="color-input me-2" id="reportHeaderColor" 
                                       value="{{ settings.get('report_header_color', '#2c3e50') }}">
                                <input type="text" class="form-control" id="reportHeaderColorText" 
                                       value="{{ settings.get('report_header_color', '#2c3e50') }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">معدل الضريبة (%):</label>
                            <input type="number" class="form-control" id="taxRate" min="0" max="100" step="0.1"
                                   value="{{ settings.get('tax_rate', '14') }}">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">نص تذييل التقارير:</label>
                        <input type="text" class="form-control" id="reportFooterText" 
                               value="{{ settings.get('report_footer_text', '') }}">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">شروط الإيجار:</label>
                        <textarea class="form-control" id="rentalTerms" rows="3">{{ settings.get('rental_terms', '') }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- الشعار والهوية البصرية -->
        <div class="col-lg-4">
            <div class="card settings-card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-image me-2"></i>شعار الشركة
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="logo-preview mb-3" id="logoPreview">
                        {% if logo_info.has_logo %}
                            <img src="/static/{{ settings.get('company_logo', '') }}" alt="شعار الشركة" id="logoImage">
                        {% else %}
                            <div class="text-muted">
                                <i class="fas fa-image fa-3x mb-2"></i>
                                <p>لا يوجد شعار</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <input type="file" class="form-control" id="logoFile" accept="image/*" style="display: none;">
                        <button class="btn btn-primary btn-sm me-2" onclick="document.getElementById('logoFile').click()">
                            <i class="fas fa-upload me-1"></i>رفع شعار
                        </button>
                        {% if logo_info.has_logo %}
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteLogo()">
                            <i class="fas fa-trash me-1"></i>حذف
                        </button>
                        {% endif %}
                    </div>
                    
                    {% if logo_info.has_logo %}
                    <div class="text-start">
                        <small class="text-muted">
                            <strong>معلومات الشعار:</strong><br>
                            تاريخ الإنشاء: {{ logo_info.created_date.strftime('%Y-%m-%d') if logo_info.created_date else 'غير محدد' }}<br>
                            الأحجام المتاحة: {{ logo_info.sizes.keys() | list | length }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- معاينة سريعة -->
            <div class="card settings-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="preview-section p-3 border rounded" style="background-color: var(--header-color, #2c3e50); color: white;">
                        <div class="d-flex align-items-center mb-2">
                            {% if logo_info.has_logo %}
                            <img src="/static/{{ settings.get('company_logo', '') }}" alt="شعار" style="height: 30px; margin-left: 10px;">
                            {% endif %}
                            <strong id="previewCompanyName">{{ settings.get('company_name', 'اسم الشركة') }}</strong>
                        </div>
                        <small id="previewAddress">{{ settings.get('company_address', 'عنوان الشركة') }}</small><br>
                        <small id="previewPhone">{{ settings.get('company_phone', 'رقم الهاتف') }}</small>
                    </div>
                    
                    <div class="mt-3 p-2 border-top">
                        <small class="text-muted" id="previewFooter">{{ settings.get('report_footer_text', 'نص التذييل') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث معاينة الألوان
    document.getElementById('reportHeaderColor').addEventListener('change', function() {
        const color = this.value;
        document.getElementById('reportHeaderColorText').value = color;
        document.querySelector('.preview-section').style.backgroundColor = color;
        document.documentElement.style.setProperty('--header-color', color);
    });
    
    // تحديث المعاينة السريعة
    function updatePreview() {
        document.getElementById('previewCompanyName').textContent = 
            document.getElementById('companyName').value || 'اسم الشركة';
        document.getElementById('previewAddress').textContent = 
            document.getElementById('companyAddress').value || 'عنوان الشركة';
        document.getElementById('previewPhone').textContent = 
            document.getElementById('companyPhone').value || 'رقم الهاتف';
        document.getElementById('previewFooter').textContent = 
            document.getElementById('reportFooterText').value || 'نص التذييل';
    }
    
    // ربط أحداث التحديث
    ['companyName', 'companyAddress', 'companyPhone', 'reportFooterText'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });
    
    // رفع الشعار
    document.getElementById('logoFile').addEventListener('change', function() {
        if (this.files && this.files[0]) {
            uploadLogo(this.files[0]);
        }
    });
    
    // رفع الشعار
    async function uploadLogo(file) {
        const formData = new FormData();
        formData.append('logo', file);
        
        try {
            const response = await fetch('/api/upload/company_logo', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
                // تحديث معاينة الشعار
                const logoImage = document.getElementById('logoImage');
                if (logoImage) {
                    logoImage.src = '/static/' + result.logo_path + '?t=' + Date.now();
                } else {
                    location.reload(); // إعادة تحميل الصفحة لإظهار الشعار الجديد
                }
            } else {
                showAlert('danger', result.error);
            }
        } catch (error) {
            showAlert('danger', 'حدث خطأ في رفع الشعار');
        }
    }
    
    // حفظ جميع الإعدادات
    async function saveAllSettings() {
        const settings = {
            company_name: document.getElementById('companyName').value,
            company_email: document.getElementById('companyEmail').value,
            company_phone: document.getElementById('companyPhone').value,
            company_phone2: document.getElementById('companyPhone2').value,
            company_address: document.getElementById('companyAddress').value,
            company_website: document.getElementById('companyWebsite').value,
            currency_symbol: document.getElementById('currencySymbol').value,
            report_header_color: document.getElementById('reportHeaderColor').value,
            tax_rate: document.getElementById('taxRate').value,
            report_footer_text: document.getElementById('reportFooterText').value,
            rental_terms: document.getElementById('rentalTerms').value
        };
        
        try {
            const response = await fetch('/api/company-settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
            } else {
                showAlert('danger', result.error);
            }
        } catch (error) {
            showAlert('danger', 'حدث خطأ في حفظ الإعدادات');
        }
    }
    
    // إعادة تعيين الإعدادات
    function resetSettings() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
            location.reload();
        }
    }
    
    // حذف الشعار
    async function deleteLogo() {
        if (confirm('هل أنت متأكد من حذف الشعار؟')) {
            // هنا يمكن إضافة API لحذف الشعار
            showAlert('info', 'ميزة حذف الشعار قيد التطوير');
        }
    }
    
    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updatePreview();
        
        // تطبيق لون الرأس الحالي
        const currentColor = document.getElementById('reportHeaderColor').value;
        document.querySelector('.preview-section').style.backgroundColor = currentColor;
        document.documentElement.style.setProperty('--header-color', currentColor);
    });
</script>
{% endblock %}
