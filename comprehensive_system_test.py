#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime
import time

def comprehensive_system_test():
    """اختبار شامل لجميع صفحات النظام"""
    base_url = "http://127.0.0.1:5000"
    
    print("🚀 اختبار شامل لنظام إدارة تأجير المعدات")
    print("=" * 70)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 الخادم: {base_url}")
    print("=" * 70)
    
    # إنشاء session للحفاظ على تسجيل الدخول
    session = requests.Session()
    
    # 1. اختبار تسجيل الدخول
    print("\n1️⃣ اختبار تسجيل الدخول...")
    login_success = test_login(session, base_url)
    if not login_success:
        print("❌ فشل تسجيل الدخول - توقف الاختبار")
        return
    
    # قائمة الصفحات الأساسية للاختبار
    main_pages = [
        ("/dashboard", "لوحة التحكم"),
        ("/equipment", "صفحة المعدات"),
        ("/customers", "صفحة العملاء"),
        ("/offices", "صفحة المكاتب"),
        ("/new_rental", "صفحة إنشاء الإيجار"),
        ("/inventory", "صفحة المخزون"),
        ("/external_equipment", "المعدات الخارجية"),
        ("/rentals", "صفحة الإيجارات"),
    ]
    
    # قائمة API endpoints للاختبار
    api_endpoints = [
        ("/api/equipment/all", "جميع المعدات"),
        ("/api/equipment/available", "المعدات المتاحة"),
        ("/api/customers", "جميع العملاء"),
        ("/api/external-equipment/available", "المعدات الخارجية المتاحة"),
        ("/api/offices/3/equipment", "معدات المكتب 3"),
    ]
    
    # صفحات تفصيلية للاختبار
    detail_pages = [
        ("/offices/1", "تفاصيل المكتب 1"),
        ("/offices/3", "تفاصيل المكتب 3"),
        ("/customer/15", "تفاصيل العميل 15"),
    ]
    
    results = {
        'main_pages': {},
        'api_endpoints': {},
        'detail_pages': {},
        'summary': {}
    }
    
    # 2. اختبار الصفحات الأساسية
    print("\n2️⃣ اختبار الصفحات الأساسية...")
    for url, name in main_pages:
        result = test_page(session, base_url, url, name)
        results['main_pages'][url] = result
        time.sleep(0.5)  # تأخير بسيط بين الطلبات
    
    # 3. اختبار API endpoints
    print("\n3️⃣ اختبار API endpoints...")
    for url, name in api_endpoints:
        result = test_api_endpoint(session, base_url, url, name)
        results['api_endpoints'][url] = result
        time.sleep(0.5)
    
    # 4. اختبار الصفحات التفصيلية
    print("\n4️⃣ اختبار الصفحات التفصيلية...")
    for url, name in detail_pages:
        result = test_page(session, base_url, url, name)
        results['detail_pages'][url] = result
        time.sleep(0.5)
    
    # 5. تحليل النتائج وإنشاء التقرير
    print("\n5️⃣ تحليل النتائج...")
    summary = analyze_results(results)
    results['summary'] = summary
    
    # 6. عرض التقرير النهائي
    print_final_report(results)
    
    return results

def test_login(session, base_url):
    """اختبار تسجيل الدخول"""
    try:
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = session.post(f"{base_url}/login", data=login_data)
        
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تسجيل الدخول نجح")
            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return False

def test_page(session, base_url, url, name):
    """اختبار صفحة عادية"""
    try:
        response = session.get(f"{base_url}{url}")
        
        result = {
            'name': name,
            'status_code': response.status_code,
            'content_length': len(response.text),
            'success': False,
            'has_table': False,
            'has_data': False,
            'row_count': 0,
            'errors': []
        }
        
        if response.status_code == 200:
            content = response.text
            result['success'] = True
            
            # فحص وجود الجدول
            if '<table' in content and 'tbody' in content:
                result['has_table'] = True
            
            # فحص وجود البيانات
            if any(keyword in content for keyword in ['كاميرا', 'أحمد', 'محمد', 'استوديو']):
                result['has_data'] = True
            
            # عد الصفوف حسب نوع الصفحة
            if 'equipment' in url:
                result['row_count'] = content.count('<tr data-category=')
            elif 'customers' in url:
                result['row_count'] = content.count('<tr data-customer=')
            elif 'offices' in url and url == '/offices':
                result['row_count'] = content.count('<tr data-office=')
            
            print(f"✅ {name}: {response.status_code} - {len(content)} حرف - {result['row_count']} صف")
        
        elif response.status_code == 302:
            result['success'] = True
            print(f"🔄 {name}: إعادة توجيه")
        
        else:
            result['errors'].append(f"HTTP {response.status_code}")
            print(f"❌ {name}: خطأ {response.status_code}")
        
        return result
        
    except Exception as e:
        print(f"❌ {name}: خطأ - {e}")
        return {
            'name': name,
            'success': False,
            'errors': [str(e)]
        }

def test_api_endpoint(session, base_url, url, name):
    """اختبار API endpoint"""
    try:
        response = session.get(f"{base_url}{url}")
        
        result = {
            'name': name,
            'status_code': response.status_code,
            'success': False,
            'data_count': 0,
            'response_size': 0,
            'errors': []
        }
        
        if response.status_code == 200:
            try:
                data = response.json()
                result['success'] = True
                result['response_size'] = len(response.text)
                
                # حساب عدد العناصر
                if isinstance(data, dict):
                    if 'equipment' in data:
                        result['data_count'] = len(data['equipment'])
                    elif 'customers' in data:
                        result['data_count'] = len(data['customers'])
                    elif 'count' in data:
                        result['data_count'] = data['count']
                elif isinstance(data, list):
                    result['data_count'] = len(data)
                
                print(f"✅ {name}: {result['data_count']} عنصر")
                
            except json.JSONDecodeError:
                result['errors'].append("Invalid JSON response")
                print(f"❌ {name}: استجابة JSON غير صحيحة")
        else:
            result['errors'].append(f"HTTP {response.status_code}")
            print(f"❌ {name}: خطأ {response.status_code}")
        
        return result
        
    except Exception as e:
        print(f"❌ {name}: خطأ - {e}")
        return {
            'name': name,
            'success': False,
            'errors': [str(e)]
        }

def analyze_results(results):
    """تحليل النتائج وإنشاء ملخص"""
    summary = {
        'total_tests': 0,
        'successful_tests': 0,
        'failed_tests': 0,
        'success_rate': 0,
        'categories': {}
    }
    
    # تحليل كل فئة
    for category, tests in results.items():
        if category == 'summary':
            continue
            
        category_summary = {
            'total': len(tests),
            'successful': 0,
            'failed': 0,
            'success_rate': 0
        }
        
        for test_result in tests.values():
            if test_result.get('success', False):
                category_summary['successful'] += 1
            else:
                category_summary['failed'] += 1
        
        if category_summary['total'] > 0:
            category_summary['success_rate'] = (category_summary['successful'] / category_summary['total']) * 100
        
        summary['categories'][category] = category_summary
        summary['total_tests'] += category_summary['total']
        summary['successful_tests'] += category_summary['successful']
        summary['failed_tests'] += category_summary['failed']
    
    if summary['total_tests'] > 0:
        summary['success_rate'] = (summary['successful_tests'] / summary['total_tests']) * 100
    
    return summary

def print_final_report(results):
    """طباعة التقرير النهائي"""
    print("\n" + "=" * 70)
    print("📊 التقرير النهائي للاختبار الشامل")
    print("=" * 70)
    
    summary = results['summary']
    
    print(f"📈 إجمالي الاختبارات: {summary['total_tests']}")
    print(f"✅ الاختبارات الناجحة: {summary['successful_tests']}")
    print(f"❌ الاختبارات الفاشلة: {summary['failed_tests']}")
    print(f"🎯 معدل النجاح: {summary['success_rate']:.1f}%")
    
    print(f"\n📋 تفاصيل الفئات:")
    for category, data in summary['categories'].items():
        category_name = {
            'main_pages': 'الصفحات الأساسية',
            'api_endpoints': 'API Endpoints',
            'detail_pages': 'الصفحات التفصيلية'
        }.get(category, category)
        
        print(f"   🔸 {category_name}: {data['successful']}/{data['total']} ({data['success_rate']:.1f}%)")
    
    # عرض الاختبارات الفاشلة
    print(f"\n❌ الاختبارات الفاشلة:")
    failed_found = False
    for category, tests in results.items():
        if category == 'summary':
            continue
        for test_result in tests.values():
            if not test_result.get('success', False):
                failed_found = True
                errors = ', '.join(test_result.get('errors', ['Unknown error']))
                print(f"   • {test_result['name']}: {errors}")
    
    if not failed_found:
        print("   🎉 لا توجد اختبارات فاشلة!")
    
    # تقييم عام
    if summary['success_rate'] >= 90:
        status = "🌟 ممتاز"
    elif summary['success_rate'] >= 80:
        status = "✅ جيد جداً"
    elif summary['success_rate'] >= 70:
        status = "⚠️ جيد"
    else:
        status = "❌ يحتاج تحسين"
    
    print(f"\n🏆 التقييم العام: {status}")
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

if __name__ == "__main__":
    comprehensive_system_test()
