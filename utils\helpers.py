# -*- coding: utf-8 -*-
"""
Helper functions for the rental management system
"""

import socket
from datetime import datetime

def get_role_display_name(role):
    """تحويل دور المستخدم إلى نص عربي"""
    role_names = {
        'admin': 'مدير النظام',
        'manager': 'مدير',
        'employee': 'موظف'
    }
    return role_names.get(role, role)

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # إنشاء اتصال وهمي للحصول على IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def format_datetime(dt):
    """تنسيق التاريخ والوقت"""
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt)
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d %H:%M')
    
    return str(dt)

def format_date(dt):
    """تنسيق التاريخ فقط"""
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt)
        except:
            return dt
    
    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d')
    
    return str(dt)

def calculate_rental_days(start_date, end_date):
    """حساب عدد أيام الإيجار"""
    try:
        if isinstance(start_date, str):
            start_date = datetime.fromisoformat(start_date)
        if isinstance(end_date, str):
            end_date = datetime.fromisoformat(end_date)
        
        delta = end_date - start_date
        return max(1, delta.days)  # على الأقل يوم واحد
    except:
        return 1

def format_currency(amount):
    """تنسيق المبلغ المالي"""
    try:
        return f"{float(amount):,.2f} ج.م"
    except:
        return "0.00 ج.م"

def validate_phone(phone):
    """التحقق من صحة رقم الهاتف"""
    if not phone:
        return False
    
    # إزالة المسافات والرموز
    phone = phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
    
    # فحص الطول والأرقام
    if len(phone) >= 10 and phone.isdigit():
        return True
    
    return False

def validate_national_id(national_id):
    """التحقق من صحة الرقم القومي"""
    if not national_id:
        return False
    
    # إزالة المسافات
    national_id = national_id.replace(' ', '')
    
    # فحص الطول والأرقام (14 رقم للرقم القومي المصري)
    if len(national_id) == 14 and national_id.isdigit():
        return True
    
    return False

def safe_float(value, default=0.0):
    """تحويل آمن إلى float"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """تحويل آمن إلى int"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default
