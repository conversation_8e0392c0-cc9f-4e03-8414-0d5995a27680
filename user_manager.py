#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المستخدمين ونظام الصلاحيات
User Manager and Permissions System
"""

import sqlite3
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum

class UserRole(Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"          # مدير النظام - صلاحيات كاملة
    MANAGER = "manager"      # مدير - نفس صلاحيات admin (للتوافق)
    EMPLOYEE = "employee"    # موظف - صلاحيات محدودة: إضافة وتعديل العملاء والإيجارات فقط

class Permission(Enum):
    """الصلاحيات المتاحة"""
    # إدارة المعدات
    EQUIPMENT_VIEW = "equipment_view"
    EQUIPMENT_ADD = "equipment_add"
    EQUIPMENT_EDIT = "equipment_edit"
    EQUIPMENT_DELETE = "equipment_delete"
    
    # إدارة العملاء
    CUSTOMERS_VIEW = "customers_view"
    CUSTOMERS_ADD = "customers_add"
    CUSTOMERS_EDIT = "customers_edit"
    CUSTOMERS_DELETE = "customers_delete"
    
    # إدارة الإيجارات
    RENTALS_VIEW = "rentals_view"
    RENTALS_ADD = "rentals_add"
    RENTALS_EDIT = "rentals_edit"
    RENTALS_DELETE = "rentals_delete"
    RENTALS_RETURN = "rentals_return"
    
    # التقارير
    REPORTS_VIEW = "reports_view"
    REPORTS_EXPORT = "reports_export"

    # إدارة الفواتير
    INVOICES_VIEW = "invoices_view"
    INVOICES_MANAGE = "invoices_manage"
    
    # إدارة النظام
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_RESTORE = "system_restore"
    SYSTEM_SETTINGS = "system_settings"
    
    # إدارة المستخدمين
    USERS_VIEW = "users_view"
    USERS_ADD = "users_add"
    USERS_EDIT = "users_edit"
    USERS_DELETE = "users_delete"

    # إدارة المخزون
    INVENTORY_VIEW = "inventory_view"
    INVENTORY_ADD = "inventory_add"
    INVENTORY_EDIT = "inventory_edit"
    INVENTORY_DELETE = "inventory_delete"

class UserManager:
    """مدير المستخدمين ونظام الصلاحيات"""
    
    def __init__(self, db_path: str = "rental_system.db"):
        """تهيئة مدير المستخدمين"""
        self.db_path = db_path
        self.current_user = None
        self.session_token = None
        self.session_expiry = None
        
        # إعداد الصلاحيات لكل دور
        self.role_permissions = {
            UserRole.ADMIN: list(Permission),  # جميع الصلاحيات
            UserRole.MANAGER: list(Permission),  # جميع الصلاحيات
            UserRole.EMPLOYEE: [
                # الصلاحيات الأساسية للموظف - لا يمكن تغييرها
                Permission.CUSTOMERS_VIEW,     # عرض العملاء
                Permission.CUSTOMERS_ADD,      # إضافة عملاء
                Permission.CUSTOMERS_EDIT,     # تعديل العملاء
                Permission.RENTALS_VIEW,       # عرض الإيجارات
                Permission.RENTALS_ADD,        # إضافة إيجارات
                Permission.RENTALS_EDIT,       # تعديل الإيجارات
                Permission.RENTALS_RETURN,     # إرجاع المعدات
                Permission.EQUIPMENT_VIEW      # عرض المعدات
                # ملاحظة: لا يوجد صلاحيات حذف للموظف
            ]
        }
        
        # التحقق من وجود قاعدة البيانات وإنشاء الجداول إذا لزم الأمر
        self._ensure_database_structure()

    def _ensure_database_structure(self):
        """التأكد من وجود هيكل قاعدة البيانات الصحيح"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول المستخدمين
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
            if not cursor.fetchone():
                # إنشاء الجداول إذا لم تكن موجودة
                self._create_tables()
            else:
                # التحقق من هيكل الجدول الموجود
                cursor.execute("PRAGMA table_info(users)")
                columns = [col[1] for col in cursor.fetchall()]

                # إذا كان الجدول يستخدم النظام القديم، لا نفعل شيئاً
                # سنعتمد على الهيكل الموجود
                pass

            conn.close()

        except Exception as e:
            print(f"خطأ في التحقق من هيكل قاعدة البيانات: {e}")

    def _create_tables(self):
        """إنشاء جداول المستخدمين (النسخة المبسطة)"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # التحقق من وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            # الجدول موجود، لا نحتاج لإنشائه
            conn.close()
            return

        # إنشاء جدول المستخدمين بالهيكل الصحيح
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                failed_login_attempts INTEGER DEFAULT 0
            )
        """)

        # جدول الصلاحيات المخصصة للمستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_custom_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission TEXT NOT NULL,
                granted_by INTEGER NOT NULL,
                granted_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (granted_by) REFERENCES users (id),
                UNIQUE(user_id, permission)
            )
        """)

        # جدول سجل النشاطات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # جدول الصلاحيات المخصصة للموظفين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_custom_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission TEXT NOT NULL,
                granted_by INTEGER NOT NULL,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (granted_by) REFERENCES users (id),
                UNIQUE(user_id, permission)
            )
        """)

        conn.commit()
        conn.close()
    
    def create_default_admin(self):
        """إنشاء المستخدم الافتراضي (مدير النظام) - تم تعطيلها لأن المستخدمين موجودون"""
        # المستخدمون موجودون بالفعل في قاعدة البيانات
        pass
    
    def _hash_password(self, password: str, salt: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000).hex()
    
    def _verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """التحقق من كلمة المرور"""
        return self._hash_password(password, salt) == password_hash
    
    def authenticate(self, username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        تسجيل دخول المستخدم
        
        Returns:
            Tuple[bool, str, Optional[Dict]]: (نجح/فشل, رسالة, بيانات المستخدم)
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # البحث عن المستخدم
            cursor.execute("""
                SELECT id, username, password, full_name, role, is_active,
                       failed_login_attempts
                FROM users WHERE username = ?
            """, (username,))

            user_data = cursor.fetchone()

            if not user_data:
                self._log_activity(None, "LOGIN_FAILED", f"محاولة دخول بمستخدم غير موجود: {username}")
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", None

            user_id, db_username, stored_password_hash, full_name, role, is_active, failed_attempts = user_data

            # التحقق من حالة المستخدم
            if not is_active:
                self._log_activity(user_id, "LOGIN_FAILED", "محاولة دخول لحساب معطل")
                return False, "الحساب معطل. يرجى الاتصال بالمدير", None

            # تخطي فحص قفل الحساب (غير مطلوب في النسخة المبسطة)

            # التحقق من كلمة المرور باستخدام SHA256 البسيط
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if password_hash == stored_password_hash:
                # تسجيل دخول ناجح
                cursor.execute("""
                    UPDATE users SET failed_login_attempts = 0
                    WHERE id = ?
                """, (user_id,))

                conn.commit()
                
                # حفظ بيانات المستخدم الحالي
                user_role = UserRole(role)
                self.current_user = {
                    'id': user_id,
                    'username': db_username,
                    'full_name': full_name,
                    'role': user_role,
                    'permissions': self.get_user_permissions(user_id)  # استخدام الصلاحيات المخصصة
                }

                self._log_activity(user_id, "LOGIN_SUCCESS", "تسجيل دخول ناجح")

                return True, "تم تسجيل الدخول بنجاح", self.current_user
            
            else:
                # كلمة مرور خاطئة
                failed_attempts += 1
                
                if failed_attempts >= 5:
                    # تعطيل الحساب بعد 5 محاولات فاشلة
                    cursor.execute("""
                        UPDATE users SET failed_login_attempts = ?, is_active = 0
                        WHERE id = ?
                    """, (failed_attempts, user_id))

                    self._log_activity(user_id, "ACCOUNT_LOCKED", f"تم تعطيل الحساب بعد {failed_attempts} محاولات فاشلة")
                    conn.commit()
                    return False, "تم تعطيل الحساب بسبب المحاولات المتكررة. يرجى الاتصال بالمدير", None
                
                else:
                    cursor.execute("UPDATE users SET failed_login_attempts = ? WHERE id = ?", (failed_attempts, user_id))
                    remaining_attempts = 5 - failed_attempts
                    
                    self._log_activity(user_id, "LOGIN_FAILED", f"كلمة مرور خاطئة - المحاولة {failed_attempts}")
                    conn.commit()
                    return False, f"كلمة المرور غير صحيحة. المحاولات المتبقية: {remaining_attempts}", None
        
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            return False, "حدث خطأ في النظام", None
        
        finally:
            conn.close()
    
    def logout(self):
        """تسجيل خروج المستخدم"""
        if self.current_user and self.session_token:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إلغاء الجلسة
            cursor.execute("UPDATE user_sessions SET is_active = 0 WHERE session_token = ?", (self.session_token,))
            conn.commit()
            conn.close()
            
            self._log_activity(self.current_user['id'], "LOGOUT", "تسجيل خروج")
            
            # مسح بيانات الجلسة
            self.current_user = None
            self.session_token = None
            self.session_expiry = None
    
    def has_permission(self, permission: Permission) -> bool:
        """التحقق من صلاحية المستخدم"""
        if not self.current_user:
            return False
        
        return permission in self.current_user['permissions']
    
    def require_permission(self, permission: Permission) -> bool:
        """طلب صلاحية معينة (يرفع استثناء إذا لم تكن متوفرة)"""
        if not self.has_permission(permission):
            raise PermissionError(f"ليس لديك صلاحية: {permission.value}")
        return True
    
    def _log_activity(self, user_id: Optional[int], action: str, details: str = "", ip_address: str = ""):
        """تسجيل نشاط المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO user_activity_log (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            """, (user_id, action, details, ip_address))
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")
    
    def is_session_valid(self) -> bool:
        """التحقق من صحة الجلسة الحالية"""
        if not self.session_token or not self.session_expiry:
            return False
        
        if datetime.now() > self.session_expiry:
            self.logout()
            return False
        
        return True

    def add_user(self, username: str, password: str, full_name: str, role: UserRole,
                 email: str = "", phone: str = "") -> Tuple[bool, str]:
        """
        إضافة مستخدم جديد

        Returns:
            Tuple[bool, str]: (نجح/فشل, رسالة)
        """
        # التحقق من الصلاحية
        if not self.has_permission(Permission.USERS_ADD):
            return False, "ليس لديك صلاحية إضافة المستخدمين"

        # التحقق من صحة البيانات
        if len(username) < 3:
            return False, "اسم المستخدم يجب أن يكون 3 أحرف على الأقل"

        if len(password) < 6:
            return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"

        if not full_name.strip():
            return False, "الاسم الكامل مطلوب"

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من عدم وجود المستخدم
            cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
            if cursor.fetchone():
                return False, "اسم المستخدم موجود بالفعل"

            # تشفير كلمة المرور (نظام مبسط)
            import hashlib
            password_hash = hashlib.sha256(password.encode()).hexdigest()

            # إضافة المستخدم
            cursor.execute("""
                INSERT INTO users (username, password, full_name, role, email, phone)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (username, password_hash, full_name, role.value, email, phone))
            conn.commit()
            conn.close()

            self._log_activity(self.current_user['id'], "USER_ADDED", f"إضافة مستخدم جديد: {username}")

            return True, f"تم إضافة المستخدم {username} بنجاح"

        except Exception as e:
            return False, f"خطأ في إضافة المستخدم: {str(e)}"

    def get_role_permissions(self, role: UserRole) -> List[Permission]:
        """الحصول على صلاحيات الدور الأساسية"""
        if role == UserRole.ADMIN or role == UserRole.MANAGER:
            # مدير النظام والمدير لهما جميع الصلاحيات
            return [permission for permission in Permission]

        elif role == UserRole.EMPLOYEE:
            # الموظف له صلاحيات أساسية محدودة: إضافة وتعديل العملاء والإيجارات وكتابة الملاحظات فقط
            return [
                # إدارة العملاء - إضافة وتعديل فقط
                Permission.CUSTOMERS_VIEW,    # عرض العملاء
                Permission.CUSTOMERS_ADD,     # إضافة عملاء
                Permission.CUSTOMERS_EDIT,    # تعديل العملاء
                # إدارة الإيجارات - إضافة وتعديل فقط
                Permission.RENTALS_VIEW,      # عرض الإيجارات
                Permission.RENTALS_ADD,       # إضافة إيجارات
                Permission.RENTALS_EDIT,      # تعديل الإيجارات (يشمل كتابة الملاحظات)
                # عرض المعدات فقط (للاختيار عند الإيجار)
                Permission.EQUIPMENT_VIEW     # عرض المعدات
            ]

        else:
            return []

    def get_user_permissions(self, user_id: int) -> List[Permission]:
        """الحصول على جميع صلاحيات المستخدم (الأساسية + المخصصة)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على دور المستخدم
            cursor.execute("SELECT role FROM users WHERE id = ?", (user_id,))
            result = cursor.fetchone()

            if not result:
                return []

            role = UserRole(result[0])

            # الصلاحيات الأساسية للدور
            base_permissions = self.get_role_permissions(role)

            # الصلاحيات المخصصة للمستخدم
            cursor.execute("SELECT permission FROM user_custom_permissions WHERE user_id = ?", (user_id,))
            custom_permissions = [Permission(row[0]) for row in cursor.fetchall()]

            conn.close()

            # دمج الصلاحيات وإزالة المكررات
            all_permissions = list(set(base_permissions + custom_permissions))
            return all_permissions

        except Exception as e:
            print(f"خطأ في الحصول على صلاحيات المستخدم: {e}")
            return []

    def grant_permission(self, user_id: int, permission: Permission) -> Tuple[bool, str]:
        """منح صلاحية مخصصة لمستخدم"""
        # التحقق من صلاحية المدير
        if not self.has_permission(Permission.USERS_EDIT):
            return False, "ليس لديك صلاحية تعديل صلاحيات المستخدمين"

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود المستخدم
            cursor.execute("SELECT role FROM users WHERE id = ?", (user_id,))
            result = cursor.fetchone()

            if not result:
                return False, "المستخدم غير موجود"

            # التحقق من أن المستخدم موظف
            if UserRole(result[0]) != UserRole.EMPLOYEE:
                return False, "يمكن منح صلاحيات مخصصة للموظفين فقط"

            # إضافة الصلاحية
            cursor.execute("""
                INSERT OR REPLACE INTO user_custom_permissions (user_id, permission, granted_by)
                VALUES (?, ?, ?)
            """, (user_id, permission.value, self.current_user['id']))

            conn.commit()
            conn.close()

            self._log_activity(self.current_user['id'], "PERMISSION_GRANTED",
                             f"منح صلاحية {permission.value} للمستخدم {user_id}")

            return True, "تم منح الصلاحية بنجاح"

        except Exception as e:
            return False, f"خطأ في منح الصلاحية: {str(e)}"

    def revoke_permission(self, user_id: int, permission: Permission) -> Tuple[bool, str]:
        """سحب صلاحية مخصصة من مستخدم"""
        # التحقق من صلاحية المدير
        if not self.has_permission(Permission.USERS_EDIT):
            return False, "ليس لديك صلاحية تعديل صلاحيات المستخدمين"

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف الصلاحية المخصصة
            cursor.execute("""
                DELETE FROM user_custom_permissions
                WHERE user_id = ? AND permission = ?
            """, (user_id, permission.value))

            if cursor.rowcount == 0:
                return False, "الصلاحية غير موجودة أو هي صلاحية أساسية"

            conn.commit()
            conn.close()

            self._log_activity(self.current_user['id'], "PERMISSION_REVOKED",
                             f"سحب صلاحية {permission.value} من المستخدم {user_id}")

            return True, "تم سحب الصلاحية بنجاح"

        except Exception as e:
            return False, f"خطأ في سحب الصلاحية: {str(e)}"

    def get_role_display_name(self, role: UserRole) -> str:
        """الحصول على الاسم المعروض للدور"""
        role_names = {
            UserRole.ADMIN: "مدير النظام",
            UserRole.MANAGER: "مدير",
            UserRole.EMPLOYEE: "موظف"
        }
        return role_names.get(role, role.value)

    def get_permission_display_name(self, permission: Permission) -> str:
        """الحصول على الاسم المعروض للصلاحية"""
        permission_names = {
            Permission.EQUIPMENT_VIEW: "عرض المعدات",
            Permission.EQUIPMENT_ADD: "إضافة معدات",
            Permission.EQUIPMENT_EDIT: "تعديل المعدات",
            Permission.EQUIPMENT_DELETE: "حذف المعدات",
            Permission.CUSTOMERS_VIEW: "عرض العملاء",
            Permission.CUSTOMERS_ADD: "إضافة عملاء",
            Permission.CUSTOMERS_EDIT: "تعديل العملاء",
            Permission.CUSTOMERS_DELETE: "حذف العملاء",
            Permission.RENTALS_VIEW: "عرض الإيجارات",
            Permission.RENTALS_ADD: "إضافة إيجارات",
            Permission.RENTALS_EDIT: "تعديل الإيجارات",
            Permission.RENTALS_DELETE: "حذف الإيجارات",
            Permission.RENTALS_RETURN: "إرجاع المعدات",
            Permission.REPORTS_VIEW: "عرض التقارير",
            Permission.REPORTS_EXPORT: "تصدير التقارير",
            Permission.SYSTEM_BACKUP: "النسخ الاحتياطي",
            Permission.SYSTEM_RESTORE: "الاستعادة",
            Permission.SYSTEM_SETTINGS: "إعدادات النظام",
            Permission.USERS_VIEW: "عرض المستخدمين",
            Permission.USERS_ADD: "إضافة مستخدمين",
            Permission.USERS_EDIT: "تعديل المستخدمين",
            Permission.USERS_DELETE: "حذف المستخدمين"
        }
        return permission_names.get(permission, permission.value)

    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, username, full_name, role, email, phone, is_active,
                       created_at, last_login
                FROM users
                WHERE username = ?
            """, (username,))

            result = cursor.fetchone()
            if result:
                user_dict = {
                    'id': result[0],
                    'username': result[1],
                    'full_name': result[2],
                    'role': result[3],
                    'email': result[4],
                    'phone': result[5],
                    'is_active': bool(result[6]),
                    'created_at': result[7],
                    'last_login': result[8]
                }
                return user_dict
            return None

        except Exception as e:
            print(f"خطأ في الحصول على المستخدم: {e}")
            return None

    def get_users(self) -> List[Dict]:
        """الحصول على قائمة المستخدمين"""
        if not self.has_permission(Permission.USERS_VIEW):
            return []

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, username, full_name, role, email, phone, is_active,
                       created_at, last_login, failed_login_attempts
                FROM users
                ORDER BY created_at DESC
            """)

            users = []
            for row in cursor.fetchall():
                users.append({
                    'id': row[0],
                    'username': row[1],
                    'full_name': row[2],
                    'role': row[3],
                    'email': row[4],
                    'phone': row[5],
                    'is_active': bool(row[6]),
                    'created_at': row[7],
                    'last_login': row[8],
                    'failed_login_attempts': row[9]
                })

            conn.close()
            return users

        except Exception as e:
            print(f"خطأ في الحصول على المستخدمين: {e}")
            return []

    def get_user_activity_log(self, user_id: int = None, limit: int = 100) -> List[Dict]:
        """الحصول على سجل نشاطات المستخدمين"""
        if not self.has_permission(Permission.USERS_VIEW):
            return []

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if user_id:
                cursor.execute("""
                    SELECT al.id, al.user_id, u.username, u.full_name, al.action,
                           al.details, al.ip_address, al.timestamp
                    FROM user_activity_log al
                    LEFT JOIN users u ON al.user_id = u.id
                    WHERE al.user_id = ?
                    ORDER BY al.timestamp DESC
                    LIMIT ?
                """, (user_id, limit))
            else:
                cursor.execute("""
                    SELECT al.id, al.user_id, u.username, u.full_name, al.action,
                           al.details, al.ip_address, al.timestamp
                    FROM user_activity_log al
                    LEFT JOIN users u ON al.user_id = u.id
                    ORDER BY al.timestamp DESC
                    LIMIT ?
                """, (limit,))

            activities = []
            for row in cursor.fetchall():
                activities.append({
                    'id': row[0],
                    'user_id': row[1],
                    'username': row[2] or 'غير معروف',
                    'full_name': row[3] or 'غير معروف',
                    'action': row[4],
                    'details': row[5],
                    'ip_address': row[6],
                    'timestamp': row[7]
                })

            conn.close()
            return activities

        except Exception as e:
            print(f"خطأ في الحصول على سجل النشاطات: {e}")
            return []

    def update_user(self, user_id: int, full_name: str = None, role: UserRole = None,
                   email: str = None, phone: str = None, is_active: bool = None) -> Tuple[bool, str]:
        """تحديث بيانات المستخدم"""
        if not self.has_permission(Permission.USERS_EDIT):
            return False, "ليس لديك صلاحية تعديل المستخدمين"

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # بناء استعلام التحديث
            updates = []
            params = []

            if full_name is not None:
                updates.append("full_name = ?")
                params.append(full_name)

            if role is not None:
                updates.append("role = ?")
                params.append(role.value)

            if email is not None:
                updates.append("email = ?")
                params.append(email)

            if phone is not None:
                updates.append("phone = ?")
                params.append(phone)

            if is_active is not None:
                updates.append("is_active = ?")
                params.append(is_active)

            if not updates:
                return False, "لا توجد تحديثات للتطبيق"

            params.append(user_id)
            query = f"UPDATE users SET {', '.join(updates)} WHERE id = ?"

            cursor.execute(query, params)

            if cursor.rowcount == 0:
                return False, "المستخدم غير موجود"

            conn.commit()
            conn.close()

            self._log_activity(self.current_user['id'], "USER_UPDATED", f"تحديث المستخدم ID: {user_id}")

            return True, "تم تحديث المستخدم بنجاح"

        except Exception as e:
            return False, f"خطأ في تحديث المستخدم: {str(e)}"

    def delete_user(self, user_id: int) -> Tuple[bool, str]:
        """حذف مستخدم"""
        if not self.has_permission(Permission.USERS_DELETE):
            return False, "ليس لديك صلاحية حذف المستخدمين"

        # منع حذف المستخدم الحالي
        if self.current_user and self.current_user['id'] == user_id:
            return False, "لا يمكن حذف المستخدم الحالي"

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود المستخدم
            cursor.execute("SELECT username FROM users WHERE id = ?", (user_id,))
            user_data = cursor.fetchone()

            if not user_data:
                return False, "المستخدم غير موجود"

            username = user_data[0]

            # حذف المستخدم
            cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

            # حذف الجلسات المرتبطة
            cursor.execute("DELETE FROM user_sessions WHERE user_id = ?", (user_id,))

            conn.commit()
            conn.close()

            self._log_activity(self.current_user['id'], "USER_DELETED", f"حذف المستخدم: {username}")

            return True, f"تم حذف المستخدم {username} بنجاح"

        except Exception as e:
            return False, f"خطأ في حذف المستخدم: {str(e)}"

    def change_password(self, user_id: int, old_password: str, new_password: str) -> Tuple[bool, str]:
        """تغيير كلمة المرور"""
        # يمكن للمستخدم تغيير كلمة مروره أو للمدير تغيير كلمة مرور أي مستخدم
        if self.current_user['id'] != user_id and not self.has_permission(Permission.USERS_EDIT):
            return False, "ليس لديك صلاحية تغيير كلمة المرور"

        if len(new_password) < 6:
            return False, "كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل"

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على بيانات المستخدم
            cursor.execute("SELECT password FROM users WHERE id = ?", (user_id,))
            user_data = cursor.fetchone()

            if not user_data:
                return False, "المستخدم غير موجود"

            stored_password = user_data[0]

            # التحقق من كلمة المرور القديمة (إلا إذا كان المدير)
            if self.current_user['id'] == user_id:
                import hashlib
                expected_hash = hashlib.sha256(old_password.encode()).hexdigest()
                if expected_hash != stored_password:
                    return False, "كلمة المرور القديمة غير صحيحة"

            # تشفير كلمة المرور الجديدة (نظام مبسط)
            import hashlib
            new_password_hash = hashlib.sha256(new_password.encode()).hexdigest()

            # تحديث كلمة المرور
            cursor.execute("""
                UPDATE users SET password = ?, failed_login_attempts = 0
                WHERE id = ?
            """, (new_password_hash, user_id))

            # إلغاء جميع الجلسات النشطة للمستخدم
            cursor.execute("UPDATE user_sessions SET is_active = 0 WHERE user_id = ?", (user_id,))

            conn.commit()
            conn.close()

            self._log_activity(user_id, "PASSWORD_CHANGED", "تغيير كلمة المرور")

            return True, "تم تغيير كلمة المرور بنجاح"

        except Exception as e:
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"

    def get_all_permissions(self) -> List[Dict]:
        """الحصول على جميع الصلاحيات المتاحة"""
        permissions = []
        for perm in Permission:
            permissions.append({
                'key': perm.value,
                'name': self.get_permission_display_name(perm),
                'description': self._get_permission_description(perm),
                'category': self._get_permission_category(perm)
            })
        return permissions

    def get_permissions_by_category(self) -> Dict[str, List[Dict]]:
        """تجميع الصلاحيات حسب الفئة"""
        categories = {
            'equipment': {'name': 'إدارة المعدات', 'permissions': []},
            'customers': {'name': 'إدارة العملاء', 'permissions': []},
            'rentals': {'name': 'إدارة الإيجارات', 'permissions': []},
            'inventory': {'name': 'إدارة المخزون', 'permissions': []},
            'reports': {'name': 'التقارير', 'permissions': []},
            'users': {'name': 'إدارة المستخدمين', 'permissions': []},
            'settings': {'name': 'الإعدادات', 'permissions': []}
        }

        for perm in Permission:
            category = self._get_permission_category(perm)
            if category in categories:
                categories[category]['permissions'].append({
                    'key': perm.value,
                    'name': self.get_permission_display_name(perm),
                    'description': self._get_permission_description(perm)
                })

        return categories

    def get_available_roles(self) -> List[Dict]:
        """الحصول على الأدوار المتاحة"""
        return [
            {'key': 'admin', 'name': 'مدير النظام', 'color': 'danger'},
            {'key': 'manager', 'name': 'مدير', 'color': 'warning'},
            {'key': 'employee', 'name': 'موظف', 'color': 'info'}
        ]

    def update_user_permissions(self, user_id: int, permissions: List[str]) -> bool:
        """تحديث صلاحيات مستخدم محدد - مدير النظام فقط"""
        # التحقق من أن المستخدم الحالي مدير نظام
        if not self.current_user or self.current_user.get('role') != 'admin':
            return False

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف الصلاحيات المخصصة الحالية
            cursor.execute("DELETE FROM user_custom_permissions WHERE user_id = ?", (user_id,))

            # إضافة الصلاحيات الجديدة
            for permission in permissions:
                cursor.execute("""
                    INSERT INTO user_custom_permissions (user_id, permission, granted_by)
                    VALUES (?, ?, ?)
                """, (user_id, permission, self.current_user['id']))

            conn.commit()
            conn.close()

            self._log_activity(self.current_user['id'], "PERMISSIONS_UPDATED",
                             f"تحديث صلاحيات المستخدم {user_id}")

            return True

        except Exception as e:
            print(f"خطأ في تحديث صلاحيات المستخدم: {e}")
            return False

    def get_user_custom_permissions(self, user_id: int) -> List[str]:
        """الحصول على الصلاحيات المخصصة للمستخدم"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT permission FROM user_custom_permissions
                WHERE user_id = ?
            """, (user_id,))

            permissions = [row[0] for row in cursor.fetchall()]
            conn.close()

            return permissions

        except Exception as e:
            print(f"خطأ في الحصول على الصلاحيات المخصصة: {e}")
            return []

    def _get_permission_description(self, permission: Permission) -> str:
        """الحصول على وصف الصلاحية"""
        descriptions = {
            Permission.EQUIPMENT_VIEW: "عرض قائمة المعدات وتفاصيلها",
            Permission.EQUIPMENT_ADD: "إضافة معدات جديدة للنظام",
            Permission.EQUIPMENT_EDIT: "تعديل بيانات المعدات الموجودة",
            Permission.EQUIPMENT_DELETE: "حذف المعدات من النظام",
            Permission.CUSTOMERS_VIEW: "عرض قائمة العملاء وتفاصيلهم",
            Permission.CUSTOMERS_ADD: "إضافة عملاء جدد",
            Permission.CUSTOMERS_EDIT: "تعديل بيانات العملاء",
            Permission.CUSTOMERS_DELETE: "حذف العملاء من النظام",
            Permission.RENTALS_VIEW: "عرض قائمة الإيجارات",
            Permission.RENTALS_ADD: "إنشاء إيجارات جديدة",
            Permission.RENTALS_EDIT: "تعديل الإيجارات الموجودة",
            Permission.RENTALS_DELETE: "حذف الإيجارات",
            Permission.RENTALS_RETURN: "إرجاع المعدات المؤجرة",
            Permission.REPORTS_VIEW: "عرض التقارير",
            Permission.REPORTS_EXPORT: "تصدير التقارير",
            Permission.SYSTEM_BACKUP: "إنشاء نسخ احتياطية",
            Permission.SYSTEM_RESTORE: "استعادة النسخ الاحتياطية",
            Permission.SYSTEM_SETTINGS: "إدارة إعدادات النظام",
            Permission.USERS_VIEW: "عرض قائمة المستخدمين",
            Permission.USERS_ADD: "إضافة مستخدمين جدد",
            Permission.USERS_EDIT: "تعديل بيانات المستخدمين",
            Permission.USERS_DELETE: "حذف المستخدمين"
        }
        return descriptions.get(permission, "وصف غير متاح")

    def _get_permission_category(self, permission: Permission) -> str:
        """الحصول على فئة الصلاحية"""
        if permission.value.startswith('equipment_'):
            return 'equipment'
        elif permission.value.startswith('customers_'):
            return 'customers'
        elif permission.value.startswith('rentals_'):
            return 'rentals'
        elif permission.value.startswith('inventory_'):
            return 'inventory'
        elif permission.value.startswith('reports_'):
            return 'reports'
        elif permission.value.startswith('users_'):
            return 'users'
        elif permission.value.startswith('system_'):
            return 'settings'
        else:
            return 'general'
