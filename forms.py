#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI Forms module for Camera and Lens Rental Management System
Contains modal dialogs and data display components with Arabic support
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
from datetime import datetime, date
from typing import Dict, List, Optional, Callable
import os
from image_utils import image_handler, create_placeholder_image

class BaseDialog:
    """Base class for modal dialogs with Arabic support"""
    
    def __init__(self, parent, title: str, width: int = 400, height: int = 300):
        self.parent = parent
        self.result = None
        
        # Create modal dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry(f"{width}x{height}")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry(f"+{parent.winfo_rootx() + 50}+{parent.winfo_rooty() + 50}")
        
        # Configure Arabic font support
        self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        self.dialog.option_add("*Font", self.arabic_font)
        
        # Create main frame
        self.main_frame = ttk.Frame(self.dialog, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        
        # Create form fields
        self.create_form()
        
        # Create buttons
        self.create_buttons()
        
        # Bind Enter and Escape keys
        self.dialog.bind('<Return>', lambda e: self.on_ok())
        self.dialog.bind('<Escape>', lambda e: self.on_cancel())
        
        # Focus on first entry
        self.focus_first_entry()
    
    def create_form(self):
        """Override in subclasses to create form fields"""
        pass
    
    def create_buttons(self):
        """Create OK and Cancel buttons"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=100, column=0, columnspan=2, pady=(20, 0), sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="موافق", command=self.on_ok).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="إلغاء", command=self.on_cancel).pack(side=tk.RIGHT)
    
    def focus_first_entry(self):
        """Focus on first entry widget"""
        for widget in self.main_frame.winfo_children():
            if isinstance(widget, ttk.Entry):
                widget.focus()
                break
    
    def on_ok(self):
        """Handle OK button click"""
        if self.validate():
            self.result = self.get_values()
            self.dialog.destroy()
    
    def on_cancel(self):
        """Handle Cancel button click"""
        self.result = None
        self.dialog.destroy()
    
    def validate(self) -> bool:
        """Override in subclasses to validate form data"""
        return True
    
    def get_values(self) -> Dict:
        """Override in subclasses to return form values"""
        return {}
    
    def show(self):
        """Show dialog and return result"""
        self.dialog.wait_window()
        return self.result

class EquipmentDialog(BaseDialog):
    """Dialog for adding/editing equipment"""

    def __init__(self, parent, equipment_data: Optional[Dict] = None, db_manager=None):
        self.equipment_data = equipment_data
        self.is_edit = equipment_data is not None
        self.db_manager = db_manager

        title = "تعديل المعدة" if self.is_edit else "إضافة معدة جديدة"
        super().__init__(parent, title, 450, 250)
    
    def create_form(self):
        """Create equipment form fields"""
        # Name field
        ttk.Label(self.main_frame, text="اسم المعدة:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_entry = ttk.Entry(self.main_frame, font=self.arabic_font)
        self.name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)

        # Model field
        ttk.Label(self.main_frame, text="الموديل:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.model_entry = ttk.Entry(self.main_frame, font=self.arabic_font)
        self.model_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)

        # Serial Number field
        ttk.Label(self.main_frame, text="الرقم التسلسلي:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.serial_entry = ttk.Entry(self.main_frame)
        self.serial_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)

        # Category field
        ttk.Label(self.main_frame, text="نوع المعدة:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(self.main_frame, textvariable=self.category_var,
                                          values=["كاميرات", "عدسات", "معدات إضاءة"],
                                          state="readonly", font=self.arabic_font)
        self.category_combo.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)

        # Status field
        ttk.Label(self.main_frame, text="الحالة:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.status_var = tk.StringVar()

        # Load status options from database
        status_values = self.load_status_options()
        self.status_combo = ttk.Combobox(self.main_frame, textvariable=self.status_var,
                                        values=status_values, state="readonly", font=self.arabic_font)
        self.status_combo.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

        # Fill form if editing
        if self.is_edit:
            self.name_entry.insert(0, self.equipment_data.get('name', ''))
            self.model_entry.insert(0, self.equipment_data.get('model', ''))
            self.serial_entry.insert(0, self.equipment_data.get('serial_number', ''))
            self.category_var.set(self.equipment_data.get('category', 'كاميرات'))

            # تحديد الحالة الصحيحة
            current_status = self.equipment_data.get('status', 'Available')
            if hasattr(self, 'reverse_status_mapping') and current_status in self.reverse_status_mapping:
                self.status_var.set(self.reverse_status_mapping[current_status])
            else:
                self.status_var.set(current_status)
        else:
            self.category_var.set('كاميرات')
            # تحديد الحالة الافتراضية
            if hasattr(self, 'reverse_status_mapping') and 'Available' in self.reverse_status_mapping:
                self.status_var.set(self.reverse_status_mapping['Available'])
            else:
                self.status_var.set('Available')

    def load_status_options(self):
        """تحميل خيارات الحالة من قاعدة البيانات"""
        try:
            if self.db_manager:
                from equipment_status_manager import EquipmentStatusManager
                status_manager = EquipmentStatusManager(self.db_manager)
                statuses = status_manager.get_all_statuses()

                # إنشاء قائمة بأسماء الحالات مع الأسماء المعروضة
                status_options = []
                for status in statuses:
                    display_text = f"{status['display_name']} ({status['name']})"
                    status_options.append(display_text)

                # حفظ مطابقة الأسماء للاستخدام لاحقاً
                self.status_mapping = {f"{s['display_name']} ({s['name']})": s['name'] for s in statuses}
                self.reverse_status_mapping = {s['name']: f"{s['display_name']} ({s['name']})" for s in statuses}

                return status_options
            else:
                # الحالات الافتراضية إذا لم تكن قاعدة البيانات متاحة
                return ["متاح (Available)", "مؤجر (Rented)", "صيانة (Maintenance)"]
        except Exception as e:
            print(f"خطأ في تحميل حالات المعدات: {e}")
            return ["Available", "Rented", "Maintenance"]

    def validate(self) -> bool:
        """Validate equipment form data"""
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المعدة")
            self.name_entry.focus()
            return False
        
        if not self.model_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الموديل")
            self.model_entry.focus()
            return False
        
        if not self.serial_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الرقم التسلسلي")
            self.serial_entry.focus()
            return False
        
        return True
    
    def get_values(self) -> Dict:
        """Return equipment form values"""
        # تحويل الحالة المعروضة إلى اسم الحالة الفعلي
        status_display = self.status_var.get()
        actual_status = status_display

        if hasattr(self, 'status_mapping') and status_display in self.status_mapping:
            actual_status = self.status_mapping[status_display]

        return {
            'name': self.name_entry.get().strip(),
            'model': self.model_entry.get().strip(),
            'serial_number': self.serial_entry.get().strip(),
            'category': self.category_var.get(),
            'status': actual_status
        }

class CustomerDialog(BaseDialog):
    """Dialog for adding/editing customers"""

    def __init__(self, parent, customer_data: Optional[Dict] = None):
        self.customer_data = customer_data
        self.is_edit = customer_data is not None
        self.selected_photo_path = None
        self.selected_id_photo_path = None
        self.current_photo_path = customer_data.get('photo_path') if customer_data else None
        self.current_id_photo_path = customer_data.get('id_photo_path') if customer_data else None

        title = "تعديل العميل" if self.is_edit else "إضافة عميل جديد"
        super().__init__(parent, title, 600, 550)
    
    def create_form(self):
        """Create customer form fields"""
        # Name field
        ttk.Label(self.main_frame, text="اسم العميل:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_entry = ttk.Entry(self.main_frame, font=self.arabic_font)
        self.name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)

        # Phone field
        ttk.Label(self.main_frame, text="رقم الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.phone_entry = ttk.Entry(self.main_frame)
        self.phone_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)

        # National ID field
        ttk.Label(self.main_frame, text="الرقم القومي:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.national_id_entry = ttk.Entry(self.main_frame)
        self.national_id_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)

        # Profile Photo section
        photo_frame = ttk.LabelFrame(self.main_frame, text="الصورة الشخصية")
        photo_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10, padx=5)
        photo_frame.columnconfigure(1, weight=1)

        # Photo display
        self.photo_label = ttk.Label(photo_frame)
        self.photo_label.grid(row=0, column=0, rowspan=3, padx=10, pady=10)

        # Photo buttons
        ttk.Button(photo_frame, text="اختيار صورة شخصية", command=self.select_photo).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10, pady=2)
        ttk.Button(photo_frame, text="حذف الصورة", command=self.remove_photo).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=2)

        # Photo info label
        self.photo_info_label = ttk.Label(photo_frame, text="لا توجد صورة", font=("Arial", 8))
        self.photo_info_label.grid(row=2, column=1, sticky=tk.W, padx=10, pady=2)

        # ID Photo section
        id_photo_frame = ttk.LabelFrame(self.main_frame, text="صورة بطاقة الهوية")
        id_photo_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10, padx=5)
        id_photo_frame.columnconfigure(1, weight=1)

        # ID Photo display
        self.id_photo_label = ttk.Label(id_photo_frame)
        self.id_photo_label.grid(row=0, column=0, rowspan=3, padx=10, pady=10)

        # ID Photo buttons
        ttk.Button(id_photo_frame, text="اختيار صورة بطاقة الهوية", command=self.select_id_photo).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10, pady=2)
        ttk.Button(id_photo_frame, text="حذف صورة البطاقة", command=self.remove_id_photo).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=2)

        # ID Photo info label
        self.id_photo_info_label = ttk.Label(id_photo_frame, text="لا توجد صورة", font=("Arial", 8))
        self.id_photo_info_label.grid(row=2, column=1, sticky=tk.W, padx=10, pady=2)

        # Load existing photos if editing
        self.load_existing_photos()

        # Fill form if editing
        if self.is_edit:
            self.name_entry.insert(0, self.customer_data.get('name', ''))
            self.phone_entry.insert(0, self.customer_data.get('phone', ''))
            self.national_id_entry.insert(0, self.customer_data.get('national_id', '') or '')
    
    def validate(self) -> bool:
        """Validate customer form data"""
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            self.name_entry.focus()
            return False
        
        if not self.phone_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الهاتف")
            self.phone_entry.focus()
            return False
        
        return True
    
    def load_existing_photos(self):
        """Load existing customer photos if available"""
        # Load profile photo
        if self.current_photo_path:
            photo_image = image_handler.get_photo_for_display(self.current_photo_path)
            if photo_image:
                self.photo_label.configure(image=photo_image)
                self.photo_label.image = photo_image  # Keep a reference
                self.photo_info_label.configure(text="صورة موجودة")
            else:
                self.show_placeholder_photo()
        else:
            self.show_placeholder_photo()

        # Load ID photo
        if self.current_id_photo_path:
            id_photo_image = image_handler.get_photo_for_display(self.current_id_photo_path)
            if id_photo_image:
                self.id_photo_label.configure(image=id_photo_image)
                self.id_photo_label.image = id_photo_image  # Keep a reference
                self.id_photo_info_label.configure(text="صورة بطاقة موجودة")
            else:
                self.show_placeholder_id_photo()
        else:
            self.show_placeholder_id_photo()

    def show_placeholder_photo(self):
        """Show placeholder image when no profile photo is available"""
        placeholder = create_placeholder_image()
        if placeholder:
            self.photo_label.configure(image=placeholder)
            self.photo_label.image = placeholder
        self.photo_info_label.configure(text="لا توجد صورة")

    def show_placeholder_id_photo(self):
        """Show placeholder image when no ID photo is available"""
        placeholder = create_placeholder_image()
        if placeholder:
            self.id_photo_label.configure(image=placeholder)
            self.id_photo_label.image = placeholder
        self.id_photo_info_label.configure(text="لا توجد صورة")

    def select_photo(self):
        """Select photo file for customer"""
        file_path = image_handler.select_image_file(self.dialog)
        if file_path:
            # Validate the selected image
            is_valid, message = image_handler.validate_image(file_path)
            if is_valid:
                self.selected_photo_path = file_path
                # Show preview
                photo_image = image_handler.get_photo_for_display(file_path)
                if photo_image:
                    self.photo_label.configure(image=photo_image)
                    self.photo_label.image = photo_image
                    self.photo_info_label.configure(text="صورة جديدة محددة")
            else:
                messagebox.showerror("خطأ في الصورة", message)

    def select_id_photo(self):
        """Select ID photo file for customer"""
        file_path = image_handler.select_image_file(self.dialog)
        if file_path:
            # Validate the selected image
            is_valid, message = image_handler.validate_image(file_path)
            if is_valid:
                self.selected_id_photo_path = file_path
                # Show preview
                id_photo_image = image_handler.get_photo_for_display(file_path)
                if id_photo_image:
                    self.id_photo_label.configure(image=id_photo_image)
                    self.id_photo_label.image = id_photo_image
                    self.id_photo_info_label.configure(text="صورة بطاقة جديدة محددة")
            else:
                messagebox.showerror("خطأ في الصورة", message)

    def remove_photo(self):
        """Remove customer profile photo"""
        self.selected_photo_path = None
        self.current_photo_path = None
        self.show_placeholder_photo()

    def remove_id_photo(self):
        """Remove customer ID photo"""
        self.selected_id_photo_path = None
        self.current_id_photo_path = None
        self.show_placeholder_id_photo()

    def get_values(self) -> Dict:
        """Return customer form values"""
        return {
            'name': self.name_entry.get().strip(),
            'phone': self.phone_entry.get().strip(),
            'national_id': self.national_id_entry.get().strip() or None,
            'photo_path': self.selected_photo_path or self.current_photo_path,
            'id_photo_path': self.selected_id_photo_path or self.current_id_photo_path
        }

class RentalDialog(BaseDialog):
    """Dialog for creating new rentals"""
    
    def __init__(self, parent, customers: List[Dict], available_equipment: List[Dict]):
        self.customers = customers
        self.available_equipment = available_equipment
        super().__init__(parent, "إنشاء إيجار جديد", 500, 350)
    
    def create_form(self):
        """Create rental form fields"""
        # Customer selection
        ttk.Label(self.main_frame, text="العميل:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.customer_var = tk.StringVar()
        customer_values = [f"{c['name']} - {c['phone']}" for c in self.customers]
        self.customer_combo = ttk.Combobox(self.main_frame, textvariable=self.customer_var,
                                          values=customer_values, state="readonly",
                                          font=self.arabic_font)
        self.customer_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)

        # Equipment type filter
        ttk.Label(self.main_frame, text="نوع المعدة:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.equipment_type_var = tk.StringVar()
        self.equipment_type_combo = ttk.Combobox(self.main_frame, textvariable=self.equipment_type_var,
                                               values=["الكل", "كاميرات", "عدسات", "معدات إضاءة"],
                                               state="readonly", font=self.arabic_font)
        self.equipment_type_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        self.equipment_type_combo.set("الكل")
        self.equipment_type_combo.bind('<<ComboboxSelected>>', self.on_equipment_type_changed)

        # Equipment selection with availability indicator
        ttk.Label(self.main_frame, text="المعدة المتاحة:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.equipment_var = tk.StringVar()

        # Create equipment selection frame
        equipment_frame = ttk.Frame(self.main_frame)
        equipment_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        equipment_frame.columnconfigure(0, weight=1)

        self.equipment_var = tk.StringVar()
        self.equipment_combo = ttk.Combobox(equipment_frame, textvariable=self.equipment_var,
                                           state="readonly", font=self.arabic_font)
        self.equipment_combo.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # Refresh button for real-time availability
        ttk.Button(equipment_frame, text="تحديث", command=self.refresh_equipment_list).grid(row=0, column=1, padx=(5, 0))

        # Initialize equipment list
        self.update_equipment_list()
        
        # Rental date
        ttk.Label(self.main_frame, text="تاريخ الإيجار:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.rental_date_entry = ttk.Entry(self.main_frame)
        self.rental_date_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        self.rental_date_entry.insert(0, date.today().strftime('%Y-%m-%d'))

        # Return date
        ttk.Label(self.main_frame, text="تاريخ الإرجاع:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.return_date_entry = ttk.Entry(self.main_frame)
        self.return_date_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)

        # Price
        ttk.Label(self.main_frame, text="السعر:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.price_entry = ttk.Entry(self.main_frame)
        self.price_entry.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5)

        # Notes
        ttk.Label(self.main_frame, text="ملاحظات:").grid(row=6, column=0, sticky=tk.W+tk.N, pady=5)
        self.notes_text = tk.Text(self.main_frame, height=3, width=40, font=self.arabic_font)
        self.notes_text.grid(row=6, column=1, sticky=(tk.W, tk.E), pady=5)

        # Add scrollbar for notes
        notes_scrollbar = ttk.Scrollbar(self.main_frame, orient=tk.VERTICAL, command=self.notes_text.yview)
        notes_scrollbar.grid(row=6, column=2, sticky=(tk.N, tk.S), pady=5)
        self.notes_text.configure(yscrollcommand=notes_scrollbar.set)

        # Add date format hints
        ttk.Label(self.main_frame, text="(YYYY-MM-DD)", font=("Arial", 8)).grid(row=3, column=2, sticky=tk.W, padx=5)
        ttk.Label(self.main_frame, text="(YYYY-MM-DD)", font=("Arial", 8)).grid(row=4, column=2, sticky=tk.W, padx=5)

        # Notes hint
        ttk.Label(self.main_frame, text="(مثال: الكاميرا فيها خدش بسيط)",
                 font=("Arial", 8), foreground="gray").grid(row=7, column=1, sticky=tk.W, pady=(0, 5))

        # Note about inventory items
        note_frame = ttk.Frame(self.main_frame)
        note_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        ttk.Label(note_frame, text="ملاحظة: يمكن إضافة قطع الغيار والإكسسوارات بعد إنشاء الإيجار",
                 font=("Arial", 9), foreground="blue").pack()
    
    def validate(self) -> bool:
        """Validate rental form data"""
        if not self.customer_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار العميل")
            return False
        
        if not self.equipment_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار المعدة")
            return False
        
        if not self.rental_date_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ الإيجار")
            return False
        
        if not self.return_date_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ الإرجاع")
            return False
        
        if not self.price_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال السعر")
            return False
        
        # Validate date format
        try:
            datetime.strptime(self.rental_date_entry.get(), '%Y-%m-%d')
            datetime.strptime(self.return_date_entry.get(), '%Y-%m-%d')
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
            return False
        
        # Validate price
        try:
            float(self.price_entry.get())
        except ValueError:
            messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
            return False
        
        return True
    
    def on_equipment_type_changed(self, event=None):
        """Handle equipment type filter change"""
        self.update_equipment_list()

    def update_equipment_list(self):
        """Update equipment list based on selected type filter"""
        selected_type = self.equipment_type_var.get()

        # Filter available equipment
        available_equipment = [e for e in self.available_equipment if e['status'] == 'Available']

        # Apply type filter
        if selected_type and selected_type != "الكل":
            filtered_equipment = [e for e in available_equipment if e['category'] == selected_type]
        else:
            filtered_equipment = available_equipment

        # Update combobox values
        equipment_values = []
        for e in filtered_equipment:
            equipment_values.append(f"✓ {e['name']} - {e['model']} ({e['serial_number']}) - {e['category']}")

        self.equipment_combo['values'] = equipment_values

        # Clear current selection if it's not in the filtered list
        if self.equipment_var.get() and self.equipment_var.get() not in equipment_values:
            self.equipment_var.set("")

        # Store filtered equipment for get_values method
        self.filtered_equipment = filtered_equipment

    def refresh_equipment_list(self):
        """Refresh the equipment list to show current availability"""
        # This would need to be called from the parent with updated equipment list
        # For now, we'll just show a message
        messagebox.showinfo("تحديث", "يرجى إغلاق النافذة وإعادة فتحها للحصول على أحدث قائمة المعدات المتاحة")

    def get_values(self) -> Dict:
        """Return rental form values"""
        customer_index = self.customer_combo.current()
        equipment_index = self.equipment_combo.current()

        if customer_index == -1 or equipment_index == -1:
            return None

        # Use filtered equipment list if available, otherwise use all available equipment
        if hasattr(self, 'filtered_equipment'):
            equipment_list = self.filtered_equipment
        else:
            equipment_list = [e for e in self.available_equipment if e['status'] == 'Available']

        return {
            'customer_id': self.customers[customer_index]['id'],
            'equipment_id': equipment_list[equipment_index]['id'],
            'rental_date': self.rental_date_entry.get().strip(),
            'return_date': self.return_date_entry.get().strip(),
            'price': float(self.price_entry.get().strip()),
            'notes': self.notes_text.get("1.0", tk.END).strip()
        }

class DataGrid:
    """Reusable data grid component with search functionality"""

    def __init__(self, parent, columns: List[Dict], on_double_click: Optional[Callable] = None):
        self.parent = parent
        self.columns = columns
        self.on_double_click = on_double_click
        self.data = []

        # Create main frame
        self.frame = ttk.Frame(parent)

        # Create search frame
        self.search_frame = ttk.Frame(self.frame)
        self.search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(self.search_frame, text="بحث:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(self.search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.search_entry.bind('<KeyRelease>', self.on_search)

        ttk.Button(self.search_frame, text="مسح", command=self.clear_search).pack(side=tk.LEFT)

        # Create treeview with scrollbars
        tree_frame = ttk.Frame(self.frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Configure columns
        column_ids = [col['id'] for col in columns]
        self.tree = ttk.Treeview(tree_frame, columns=column_ids, show='headings')

        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col['id'], text=col['text'])
            self.tree.column(col['id'], width=col.get('width', 100),
                           minwidth=col.get('minwidth', 50))

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Bind double-click event
        if on_double_click:
            self.tree.bind('<Double-1>', self.on_item_double_click)

    def pack(self, **kwargs):
        """Pack the data grid frame"""
        self.frame.pack(**kwargs)

    def grid(self, **kwargs):
        """Grid the data grid frame"""
        self.frame.grid(**kwargs)

    def load_data(self, data: List[Dict]):
        """Load data into the grid"""
        self.data = data
        self.refresh_display()

    def refresh_display(self, filtered_data: Optional[List[Dict]] = None):
        """Refresh the display with current or filtered data"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        # Use filtered data if provided, otherwise use all data
        display_data = filtered_data if filtered_data is not None else self.data

        # Insert data with visual indicators for equipment status
        for row in display_data:
            values = []
            for col in self.columns:
                value = str(row.get(col['id'], ''))
                # Add visual indicators for equipment status
                if col['id'] == 'status' and 'status' in row:
                    if row['status'] == 'Available':
                        value = f"✓ {value}"
                    elif row['status'] == 'Rented':
                        value = f"🔒 {value}"
                    elif row['status'] == 'Maintenance':
                        value = f"🔧 {value}"
                values.append(value)

            # Set different tags based on status for potential styling
            tags = [str(row.get('id', ''))]
            if 'status' in row:
                tags.append(f"status_{row['status'].lower()}")

            self.tree.insert('', tk.END, values=values, tags=tuple(tags))

    def on_search(self, event=None):
        """Handle search input"""
        search_term = self.search_var.get().lower()
        if not search_term:
            self.refresh_display()
            return

        # Filter data based on search term
        filtered_data = []
        for row in self.data:
            # Search in all visible columns
            for col in self.columns:
                value = str(row.get(col['id'], '')).lower()
                if search_term in value:
                    filtered_data.append(row)
                    break

        self.refresh_display(filtered_data)

    def clear_search(self):
        """Clear search and show all data"""
        self.search_var.set('')
        self.refresh_display()

    def on_item_double_click(self, event):
        """Handle double-click on item"""
        selection = self.tree.selection()
        if selection and self.on_double_click:
            item = self.tree.item(selection[0])
            # Find the original data row by matching the first tag which should be the ID
            if item['tags']:
                try:
                    selected_id = int(item['tags'][0])
                    for row in self.data:
                        if row.get('id') == selected_id:
                            self.on_double_click(row)
                            break
                except (ValueError, IndexError):
                    # Fallback to original method if ID parsing fails
                    for row in self.data:
                        if str(row.get('id', '')) in item['tags']:
                            self.on_double_click(row)
                            break

    def get_selected_item(self) -> Optional[Dict]:
        """Get currently selected item data"""
        selection = self.tree.selection()
        if not selection:
            return None

        item = self.tree.item(selection[0])
        # Find the original data row by matching the first tag which should be the ID
        if item['tags']:
            try:
                selected_id = int(item['tags'][0])
                for row in self.data:
                    if row.get('id') == selected_id:
                        return row
            except (ValueError, IndexError):
                # Fallback to original method if ID parsing fails
                for row in self.data:
                    if str(row.get('id', '')) in item['tags']:
                        return row
        return None

class ReturnDialog(BaseDialog):
    """Dialog for processing equipment returns"""

    def __init__(self, parent, rental_data: Dict):
        self.rental_data = rental_data
        super().__init__(parent, "إرجاع المعدة", 400, 250)

    def create_form(self):
        """Create return form fields"""
        # Display rental information
        ttk.Label(self.main_frame, text="معلومات الإيجار:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        ttk.Label(self.main_frame, text="العميل:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(self.main_frame, text=self.rental_data.get('customer_name', ''),
                 font=self.arabic_font).grid(row=1, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.main_frame, text="المعدة:").grid(row=2, column=0, sticky=tk.W, pady=2)
        equipment_text = f"{self.rental_data.get('equipment_name', '')} - {self.rental_data.get('equipment_model', '')}"
        ttk.Label(self.main_frame, text=equipment_text,
                 font=self.arabic_font).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.main_frame, text="تاريخ الإيجار:").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Label(self.main_frame, text=self.rental_data.get('rental_date', '')).grid(
            row=3, column=1, sticky=tk.W, pady=2)

        ttk.Label(self.main_frame, text="تاريخ الإرجاع المتوقع:").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Label(self.main_frame, text=self.rental_data.get('return_date', '')).grid(
            row=4, column=1, sticky=tk.W, pady=2)

        # Actual return date
        ttk.Label(self.main_frame, text="تاريخ الإرجاع الفعلي:").grid(row=5, column=0, sticky=tk.W, pady=(10, 5))
        self.actual_return_date_entry = ttk.Entry(self.main_frame)
        self.actual_return_date_entry.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=(10, 5))
        self.actual_return_date_entry.insert(0, date.today().strftime('%Y-%m-%d'))

        ttk.Label(self.main_frame, text="(YYYY-MM-DD)", font=("Arial", 8)).grid(
            row=6, column=1, sticky=tk.W, pady=2)

    def validate(self) -> bool:
        """Validate return form data"""
        if not self.actual_return_date_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال تاريخ الإرجاع الفعلي")
            return False

        # Validate date format
        try:
            datetime.strptime(self.actual_return_date_entry.get(), '%Y-%m-%d')
        except ValueError:
            messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح. استخدم YYYY-MM-DD")
            return False

        return True

    def get_values(self) -> Dict:
        """Return form values"""
        return {
            'rental_id': self.rental_data['id'],
            'actual_return_date': self.actual_return_date_entry.get().strip()
        }

class ReportsDialog(BaseDialog):
    """Dialog for displaying various reports"""

    def __init__(self, parent, db_manager):
        self.db = db_manager
        super().__init__(parent, "التقارير والإحصائيات", 800, 600)

    def create_form(self):
        """Create reports interface"""
        # Create notebook for different report types
        self.reports_notebook = ttk.Notebook(self.main_frame)
        self.reports_notebook.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # Configure grid weights
        self.main_frame.rowconfigure(0, weight=1)

        # Revenue Report Tab
        self.create_revenue_report_tab()

        # Equipment Utilization Tab
        self.create_equipment_utilization_tab()

        # Equipment by Category Tab
        self.create_equipment_category_tab()

        # Customer Statistics Tab
        self.create_customer_statistics_tab()

        # Overdue Rentals Tab
        self.create_overdue_rentals_tab()

    def create_revenue_report_tab(self):
        """Create revenue report tab"""
        revenue_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(revenue_frame, text="تقرير الإيرادات")

        # Get revenue data
        revenue_data = self.db.get_revenue_report()

        # Display revenue statistics
        stats_frame = ttk.LabelFrame(revenue_frame, text="إحصائيات الإيرادات")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)

        revenue_stats = [
            ("إجمالي الإيجارات", revenue_data['total_rentals']),
            ("إجمالي الإيرادات", f"{revenue_data['total_revenue']:.2f}"),
            ("متوسط سعر الإيجار", f"{revenue_data['average_rental_price']:.2f}"),
            ("أقل سعر إيجار", f"{revenue_data['min_price']:.2f}"),
            ("أعلى سعر إيجار", f"{revenue_data['max_price']:.2f}")
        ]

        for i, (label, value) in enumerate(revenue_stats):
            row = i // 2
            col = i % 2

            ttk.Label(stats_frame, text=f"{label}:").grid(row=row, column=col*2,
                                                         sticky=tk.W, padx=10, pady=5)
            ttk.Label(stats_frame, text=str(value),
                     font=self.arabic_font).grid(row=row, column=col*2+1,
                                                sticky=tk.W, padx=10, pady=5)

    def create_equipment_utilization_tab(self):
        """Create equipment utilization tab"""
        equipment_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(equipment_frame, text="استخدام المعدات")

        # Equipment utilization data grid
        utilization_columns = [
            {'id': 'name', 'text': 'اسم المعدة', 'width': 140},
            {'id': 'model', 'text': 'الموديل', 'width': 100},
            {'id': 'category', 'text': 'النوع', 'width': 100},
            {'id': 'serial_number', 'text': 'الرقم التسلسلي', 'width': 120},
            {'id': 'status', 'text': 'الحالة', 'width': 80},
            {'id': 'total_rentals', 'text': 'عدد الإيجارات', 'width': 100},
            {'id': 'total_revenue', 'text': 'إجمالي الإيرادات', 'width': 120},
            {'id': 'last_rental_date', 'text': 'آخر إيجار', 'width': 100}
        ]

        self.utilization_grid = DataGrid(equipment_frame, utilization_columns)
        self.utilization_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Load utilization data
        utilization_data = self.db.get_equipment_utilization()
        self.utilization_grid.load_data(utilization_data)

    def create_equipment_category_tab(self):
        """Create equipment category statistics tab"""
        category_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(category_frame, text="إحصائيات حسب النوع")

        # Category statistics data grid
        category_columns = [
            {'id': 'category', 'text': 'نوع المعدة', 'width': 120},
            {'id': 'total_count', 'text': 'العدد الإجمالي', 'width': 100},
            {'id': 'available_count', 'text': 'المتاح', 'width': 80},
            {'id': 'rented_count', 'text': 'المؤجر', 'width': 80},
            {'id': 'maintenance_count', 'text': 'في الصيانة', 'width': 100}
        ]

        self.category_stats_grid = DataGrid(category_frame, category_columns)
        self.category_stats_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Load category statistics
        category_stats = self.db.get_equipment_by_category_stats()
        self.category_stats_grid.load_data(category_stats)

    def create_customer_statistics_tab(self):
        """Create customer statistics tab"""
        customer_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(customer_frame, text="إحصائيات العملاء")

        # Customer statistics data grid
        customer_columns = [
            {'id': 'name', 'text': 'اسم العميل', 'width': 150},
            {'id': 'phone', 'text': 'رقم الهاتف', 'width': 120},
            {'id': 'total_rentals', 'text': 'عدد الإيجارات', 'width': 100},
            {'id': 'total_spent', 'text': 'إجمالي المبلغ المدفوع', 'width': 150},
            {'id': 'active_rentals', 'text': 'الإيجارات النشطة', 'width': 120},
            {'id': 'last_rental_date', 'text': 'آخر إيجار', 'width': 100}
        ]

        self.customer_stats_grid = DataGrid(customer_frame, customer_columns)
        self.customer_stats_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Load customer statistics
        customer_stats = self.db.get_customer_statistics()
        self.customer_stats_grid.load_data(customer_stats)

    def create_overdue_rentals_tab(self):
        """Create overdue rentals tab"""
        overdue_frame = ttk.Frame(self.reports_notebook)
        self.reports_notebook.add(overdue_frame, text="الإيجارات المتأخرة")

        # Update overdue rentals button
        update_btn = ttk.Button(overdue_frame, text="تحديث الإيجارات المتأخرة",
                               command=self.update_overdue_rentals)
        update_btn.pack(pady=10)

        # Overdue rentals data grid
        overdue_columns = [
            {'id': 'id', 'text': 'رقم الإيجار', 'width': 80},
            {'id': 'customer_name', 'text': 'اسم العميل', 'width': 150},
            {'id': 'customer_phone', 'text': 'رقم الهاتف', 'width': 120},
            {'id': 'equipment_name', 'text': 'اسم المعدة', 'width': 150},
            {'id': 'equipment_model', 'text': 'الموديل', 'width': 120},
            {'id': 'rental_date', 'text': 'تاريخ الإيجار', 'width': 100},
            {'id': 'return_date', 'text': 'تاريخ الإرجاع المتوقع', 'width': 120},
            {'id': 'price', 'text': 'السعر', 'width': 80}
        ]

        self.overdue_grid = DataGrid(overdue_frame, overdue_columns)
        self.overdue_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Load overdue rentals
        self.refresh_overdue_rentals()

    def update_overdue_rentals(self):
        """Update overdue rentals status"""
        updated_count = self.db.update_overdue_rentals()
        messagebox.showinfo("تحديث", f"تم تحديث {updated_count} إيجار متأخر")
        self.refresh_overdue_rentals()

    def refresh_overdue_rentals(self):
        """Refresh overdue rentals data"""
        overdue_data = self.db.get_overdue_rentals()
        self.overdue_grid.load_data(overdue_data)

    def create_buttons(self):
        """Override to create custom buttons"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=100, column=0, columnspan=2, pady=(20, 0), sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="إغلاق", command=self.on_cancel).pack(side=tk.RIGHT)

class NotificationPanel:
    """Panel for displaying equipment return notifications"""

    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db = db_manager

        # Create main notification frame
        self.frame = ttk.LabelFrame(parent, text="تنبيهات الإرجاع")

        # Create notification display
        self.create_notification_display()

        # Auto-refresh timer
        self.auto_refresh()

    def create_notification_display(self):
        """Create the notification display interface"""
        # Control frame
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(control_frame, text="تحديث التنبيهات", command=self.refresh_notifications).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(control_frame, text="تحديث الحالات المتأخرة", command=self.update_overdue_status).pack(side=tk.LEFT)

        # Notification summary frame
        self.summary_frame = ttk.Frame(self.frame)
        self.summary_frame.pack(fill=tk.X, padx=5, pady=5)

        # Detailed notifications frame
        self.details_frame = ttk.Frame(self.frame)
        self.details_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Create scrollable text widget for notifications
        self.notification_text = tk.Text(self.details_frame, height=8, wrap=tk.WORD, font=("Arial", 9))
        scrollbar = ttk.Scrollbar(self.details_frame, orient=tk.VERTICAL, command=self.notification_text.yview)
        self.notification_text.configure(yscrollcommand=scrollbar.set)

        self.notification_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure text tags for different urgency levels
        self.notification_text.tag_configure("overdue_multiple", foreground="red", font=("Arial", 9, "bold"))
        self.notification_text.tag_configure("overdue_1_day", foreground="orange", font=("Arial", 9, "bold"))
        self.notification_text.tag_configure("due_today", foreground="blue", font=("Arial", 9, "bold"))
        self.notification_text.tag_configure("due_tomorrow", foreground="green")
        self.notification_text.tag_configure("header", font=("Arial", 10, "bold"))

        # Initial load
        self.refresh_notifications()

    def pack(self, **kwargs):
        """Pack the notification panel"""
        self.frame.pack(**kwargs)

    def grid(self, **kwargs):
        """Grid the notification panel"""
        self.frame.grid(**kwargs)

    def refresh_notifications(self):
        """Refresh notification display"""
        try:
            notifications = self.db.get_return_notifications()

            # Clear existing content
            self.notification_text.delete(1.0, tk.END)

            # Clear summary frame
            for widget in self.summary_frame.winfo_children():
                widget.destroy()

            # Display summary
            total = notifications['total_count']
            overdue_multiple = len(notifications['overdue_multiple'])
            overdue_1_day = len(notifications['overdue_1_day'])
            due_today = len(notifications['due_today'])
            due_tomorrow = len(notifications['due_tomorrow'])

            # Summary labels with color coding
            if overdue_multiple > 0:
                ttk.Label(self.summary_frame, text=f"🔴 متأخر عدة أيام: {overdue_multiple}",
                         foreground="red").pack(side=tk.LEFT, padx=5)

            if overdue_1_day > 0:
                ttk.Label(self.summary_frame, text=f"🟠 متأخر يوم: {overdue_1_day}",
                         foreground="orange").pack(side=tk.LEFT, padx=5)

            if due_today > 0:
                ttk.Label(self.summary_frame, text=f"🔵 مستحق اليوم: {due_today}",
                         foreground="blue").pack(side=tk.LEFT, padx=5)

            if due_tomorrow > 0:
                ttk.Label(self.summary_frame, text=f"🟢 مستحق غداً: {due_tomorrow}",
                         foreground="green").pack(side=tk.LEFT, padx=5)

            if total == 0:
                ttk.Label(self.summary_frame, text="✅ لا توجد تنبيهات",
                         foreground="green").pack(side=tk.LEFT, padx=5)

            # Display detailed notifications
            if total > 0:
                self.display_notification_category("🔴 متأخر عدة أيام:", notifications['overdue_multiple'], "overdue_multiple")
                self.display_notification_category("🟠 متأخر يوم واحد:", notifications['overdue_1_day'], "overdue_1_day")
                self.display_notification_category("🔵 مستحق اليوم:", notifications['due_today'], "due_today")
                self.display_notification_category("🟢 مستحق غداً:", notifications['due_tomorrow'], "due_tomorrow")
            else:
                self.notification_text.insert(tk.END, "✅ لا توجد معدات مستحقة الإرجاع حالياً\n", "header")

        except Exception as e:
            self.notification_text.delete(1.0, tk.END)
            self.notification_text.insert(tk.END, f"خطأ في تحديث التنبيهات: {str(e)}\n", "overdue_multiple")

    def display_notification_category(self, title: str, notifications: List[Dict], tag: str):
        """Display a category of notifications"""
        if not notifications:
            return

        self.notification_text.insert(tk.END, f"\n{title}\n", "header")

        for notification in notifications:
            customer_info = f"العميل: {notification['customer_name']} ({notification['customer_phone']})"
            equipment_info = f"المعدة: {notification['equipment_name']} - {notification['equipment_model']}"
            date_info = f"تاريخ الإرجاع: {notification['return_date']}"

            notification_text = f"• {customer_info}\n  {equipment_info}\n  {date_info}\n\n"
            self.notification_text.insert(tk.END, notification_text, tag)

    def update_overdue_status(self):
        """Update overdue rental status"""
        try:
            updated_count = self.db.auto_update_overdue_status()
            if updated_count > 0:
                messagebox.showinfo("تحديث", f"تم تحديث {updated_count} إيجار إلى حالة متأخر")
                self.refresh_notifications()
            else:
                messagebox.showinfo("تحديث", "لا توجد إيجارات تحتاج تحديث")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث الحالات: {str(e)}")

    def auto_refresh(self):
        """Auto-refresh notifications every 5 minutes"""
        self.refresh_notifications()
        # Schedule next refresh (5 minutes = 300000 milliseconds)
        self.parent.after(300000, self.auto_refresh)
