#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def debug_equipment_route():
    """محاكاة route المعدات لفحص البيانات"""
    print("🔍 فحص route المعدات")
    print("=" * 50)

    try:
        config = get_config()
        conn = sqlite3.connect(config.DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # الحصول على جميع المعدات الداخلية (نفس الكود من route)
        cursor.execute('''
            SELECT id, name, model, serial_number, category, status,
                   price, daily_rental_price, description, created_at
            FROM equipment
            ORDER BY created_at DESC
        ''')

        equipment_list = []
        for row in cursor.fetchall():
            equipment_list.append({
                'id': row['id'],
                'name': row['name'],
                'category': row['category'] or 'غير محدد',
                'model': row['model'] or '',
                'serial_number': row['serial_number'] or '',
                'daily_price': float(row['daily_rental_price'] or 0),
                'status': row['status'],
                'description': row['description'] or '',
                'created_date': row['created_at'],
                'source': 'internal',  # مصدر المعدة
                'supplier': 'داخلي',
                'total_rentals': 0,
                'active_rentals': 0,
                'total_revenue': 0.0
            })

        # الحصول على المعدات الخارجية وإضافتها للقائمة
        cursor.execute('''
            SELECT ee.id, ee.equipment_name, ee.equipment_model, ee.equipment_category,
                   ee.supplier_name, ee.customer_daily_price, ee.status,
                   ee.rental_start_date, o.name as office_name
            FROM external_equipment ee
            LEFT JOIN offices o ON ee.office_id = o.id
            WHERE ee.status = 'Active'
            ORDER BY ee.equipment_name
        ''')

        for row in cursor.fetchall():
            equipment_list.append({
                'id': f"ext_{row['id']}",  # معرف مميز للمعدات الخارجية
                'name': row['equipment_name'],
                'category': row['equipment_category'] or 'غير محدد',
                'model': row['equipment_model'] or '',
                'serial_number': '',  # المعدات الخارجية لا تحتوي على رقم تسلسلي
                'daily_price': float(row['customer_daily_price'] or 0),
                'status': 'Available',  # المعدات الخارجية النشطة متاحة
                'description': f"مستقطبة من: {row['supplier_name']}",
                'created_date': row['rental_start_date'],
                'source': 'external',  # مصدر المعدة
                'supplier': row['office_name'] or row['supplier_name'],
                'total_rentals': 0,
                'active_rentals': 0,
                'total_revenue': 0.0
            })

        print(f"🔍 تم تحميل {len(equipment_list)} معدة")
        if len(equipment_list) > 0:
            print(f"🔍 أول معدة: {equipment_list[0]['name']} - {equipment_list[0]['category']}")
            
            print("\n📋 عينة من المعدات:")
            for i, eq in enumerate(equipment_list[:5]):
                print(f"{i+1}. {eq['name']} ({eq['source']}) - {eq['category']} - {eq['status']}")
        else:
            print("⚠️ لا توجد معدات في القائمة!")

        # إحصائيات عامة (داخلية + خارجية)
        cursor.execute('SELECT COUNT(*) FROM equipment')
        internal_equipment = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM external_equipment WHERE status = "Active"')
        external_equipment = cursor.fetchone()[0]
        
        total_equipment = internal_equipment + external_equipment

        cursor.execute('SELECT COUNT(*) FROM equipment WHERE status = "Available"')
        internal_available = cursor.fetchone()[0]
        
        # المعدات الخارجية النشطة تعتبر متاحة
        available_equipment = internal_available + external_equipment

        print(f"\n📊 الإحصائيات:")
        print(f"   - المعدات الداخلية: {internal_equipment}")
        print(f"   - المعدات الخارجية: {external_equipment}")
        print(f"   - إجمالي المعدات: {total_equipment}")
        print(f"   - المعدات المتاحة: {available_equipment}")

        # إحصائيات الفئات (دمج الداخلية والخارجية)
        categories_dict = {}

        # الفئات الداخلية
        cursor.execute('''
            SELECT category, COUNT(*) as count, COALESCE(SUM(daily_rental_price), 0) as value
            FROM equipment
            GROUP BY category
        ''')
        
        for row in cursor.fetchall():
            category_name = row['category'] or 'غير محدد'
            categories_dict[category_name] = {
                'count': row['count'],
                'value': float(row['value'])
            }
        
        # الفئات الخارجية
        cursor.execute('''
            SELECT equipment_category, COUNT(*) as count, COALESCE(SUM(customer_daily_price), 0) as value
            FROM external_equipment
            WHERE status = 'Active'
            GROUP BY equipment_category
        ''')
        
        for row in cursor.fetchall():
            category_name = row['equipment_category'] or 'غير محدد'
            if category_name in categories_dict:
                categories_dict[category_name]['count'] += row['count']
                categories_dict[category_name]['value'] += float(row['value'])
            else:
                categories_dict[category_name] = {
                    'count': row['count'],
                    'value': float(row['value'])
                }

        print(f"\n📂 الفئات:")
        for category_name, data in sorted(categories_dict.items(), key=lambda x: x[1]['count'], reverse=True):
            print(f"   - {category_name}: {data['count']} معدة - {data['value']} ج.م")

        conn.close()

        # محاكاة البيانات المرسلة للقالب
        template_data = {
            'equipment': equipment_list,
            'stats': {
                'total_equipment': total_equipment,
                'available_equipment': available_equipment,
            },
            'categories': list(categories_dict.keys()),
            'categories_data': [{'name': k, 'count': v['count'], 'value': v['value']} 
                              for k, v in categories_dict.items()]
        }

        print(f"\n📤 البيانات المرسلة للقالب:")
        print(f"   - equipment: {len(template_data['equipment'])} عنصر")
        print(f"   - categories: {len(template_data['categories'])} فئة")
        print(f"   - stats: {template_data['stats']}")

        return template_data

    except Exception as e:
        print(f"❌ خطأ في فحص route المعدات: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    debug_equipment_route()
