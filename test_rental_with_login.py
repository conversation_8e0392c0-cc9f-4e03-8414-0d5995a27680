#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def test_rental_with_login():
    """اختبار إنشاء الإيجار مع تسجيل الدخول"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 اختبار إنشاء الإيجار مع تسجيل الدخول")
    print("=" * 60)
    
    # إنشاء session
    session = requests.Session()
    
    # 1. تسجيل الدخول
    print("1️⃣ تسجيل الدخول...")
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return
    
    # 2. اختبار API العملاء للتأكد من session
    print("\n2️⃣ اختبار session مع API العملاء...")
    try:
        response = session.get(f"{base_url}/api/customers")
        if response.status_code == 200:
            customers_data = response.json()
            print(f"✅ تم جلب {customers_data.get('count', 0)} عميل")
        else:
            print(f"❌ فشل في جلب العملاء: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في جلب العملاء: {e}")
        return
    
    # 3. اختبار إنشاء الإيجار
    print("\n3️⃣ إنشاء إيجار جديد...")
    
    today = datetime.now()
    tomorrow = today + timedelta(days=1)
    
    rental_data = {
        "customer_id": 1,  # أول عميل متاح
        "rental_date": today.strftime('%Y-%m-%d'),
        "return_date": tomorrow.strftime('%Y-%m-%d'),
        "notes": "إيجار تجريبي مع session صحيح",
        "equipment": [
            {
                "id": 1,  # كاميرا Canon EOS R6 - متاحة
                "daily_price": 100.0
            }
        ],
        "external_equipment": []
    }
    
    print(f"📅 تاريخ الإيجار: {rental_data['rental_date']}")
    print(f"📅 تاريخ الإرجاع: {rental_data['return_date']}")
    print(f"👤 العميل: {rental_data['customer_id']}")
    print(f"📦 المعدات: {len(rental_data['equipment'])}")
    
    try:
        response = session.post(
            f"{base_url}/api/rental/create",
            json=rental_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📡 استجابة الخادم: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ تم إنشاء الإيجار بنجاح!")
                print(f"   📋 رقم الإيجار: {result.get('rental_id')}")
                print(f"   💰 المبلغ الإجمالي: {result.get('total_amount')} ج.م")
                print(f"   📅 عدد الأيام: {result.get('days')}")
                print(f"   📝 الرسالة: {result.get('message', '')}")
            else:
                print(f"❌ فشل في إنشاء الإيجار: {result.get('error')}")
        elif response.status_code == 401:
            print("❌ خطأ 401: غير مصرح (مشكلة في session)")
        elif response.status_code == 400:
            result = response.json()
            print(f"❌ خطأ 400: بيانات غير صحيحة - {result.get('error')}")
        elif response.status_code == 500:
            try:
                result = response.json()
                print(f"❌ خطأ 500: خطأ في الخادم - {result.get('error')}")
            except:
                print(f"❌ خطأ 500: خطأ في الخادم - {response.text[:200]}...")
        else:
            print(f"❌ خطأ غير متوقع: {response.status_code}")
            print(f"   📝 النص: {response.text[:200]}...")
                
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
    
    # 4. اختبار بيانات خاطئة
    print("\n4️⃣ اختبار بيانات خاطئة...")
    invalid_data = rental_data.copy()
    invalid_data['rental_date'] = None
    
    try:
        response = session.post(
            f"{base_url}/api/rental/create",
            json=invalid_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ تم رفض البيانات الخاطئة: {result.get('error')}")
        else:
            print(f"⚠️ لم يتم رفض البيانات الخاطئة: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

def main():
    print("🚀 اختبار إصلاح خطأ strptime مع تسجيل دخول صحيح")
    print("=" * 70)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_rental_with_login()
    
    print("\n" + "=" * 70)
    print("✅ انتهى الاختبار")
    print("🌐 للوصول للنظام: http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
