#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أدوات الطباعة والتصدير
Print and Export Utilities
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import os
from typing import List, Dict
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from font_downloader import ArabicFontDownloader

class PrintManager:
    """مدير الطباعة والتصدير"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # التأكد من وجود الخطوط العربية
            current_dir = os.path.dirname(os.path.abspath(__file__))
            system_dir = os.path.dirname(current_dir)
            fonts_dir = os.path.join(system_dir, "static", "fonts")

            font_downloader = ArabicFontDownloader(fonts_dir)
            downloaded_fonts = font_downloader.list_downloaded_fonts()

            # إذا لم توجد خطوط، حاول تحميلها
            if not downloaded_fonts:
                print("لا توجد خطوط عربية، جاري التحميل...")
                font_downloader.setup_fonts_for_system()
                downloaded_fonts = font_downloader.list_downloaded_fonts()

            # قائمة الخطوط العربية المتاحة (بترتيب الأولوية)
            arabic_fonts = []

            # إضافة الخطوط المحملة
            for font_info in downloaded_fonts:
                arabic_fonts.append(font_info['path'])

            # إضافة خطوط النظام كاحتياطي
            arabic_fonts.extend([
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "/System/Library/Fonts/Arial.ttf",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
            ])

            # تسجيل الخطوط العربية
            self.arabic_font = None
            self.arabic_bold_font = None

            for font_path in arabic_fonts:
                if os.path.exists(font_path):
                    try:
                        font_name = os.path.splitext(os.path.basename(font_path))[0]
                        registered_name = f'Arabic-{font_name}'

                        pdfmetrics.registerFont(TTFont(registered_name, font_path))

                        if not self.arabic_font:
                            self.arabic_font = registered_name
                            print(f"تم تسجيل الخط العربي الأساسي: {font_path}")

                        # البحث عن النسخة العريضة
                        bold_variants = [
                            font_path.replace('.ttf', '-Bold.ttf'),
                            font_path.replace('.ttf', 'b.ttf'),
                            font_path.replace('.TTF', 'B.TTF'),
                            font_path.replace('Regular', 'Bold')
                        ]

                        for bold_path in bold_variants:
                            if os.path.exists(bold_path):
                                try:
                                    bold_name = f'{registered_name}-Bold'
                                    pdfmetrics.registerFont(TTFont(bold_name, bold_path))
                                    if not self.arabic_bold_font:
                                        self.arabic_bold_font = bold_name
                                        print(f"تم تسجيل الخط العربي العريض: {bold_path}")
                                    break
                                except:
                                    continue

                    except Exception as e:
                        print(f"فشل في تسجيل الخط {font_path}: {e}")
                        continue

            # إذا لم يتم العثور على خط عربي، استخدم الافتراضي
            if not self.arabic_font:
                self.arabic_font = 'Helvetica'
                self.arabic_bold_font = 'Helvetica-Bold'
                print("تحذير: لم يتم العثور على خط عربي مناسب، سيتم استخدام Helvetica")
            else:
                if not self.arabic_bold_font:
                    self.arabic_bold_font = self.arabic_font
                print(f"الخط العربي المستخدم: {self.arabic_font}")
                print(f"الخط العربي العريض: {self.arabic_bold_font}")

        except Exception as e:
            print(f"خطأ في إعداد الخطوط: {e}")
            self.arabic_font = 'Helvetica'
            self.arabic_bold_font = 'Helvetica-Bold'
    
    def print_available_equipment_list(self, parent=None):
        """طباعة قائمة المعدات المتاحة"""
        try:
            # الحصول على المعدات المتاحة
            equipment_list = self.db.get_equipment()
            available_equipment = [eq for eq in equipment_list if eq['status'] == 'Available']

            if not available_equipment:
                if parent:
                    messagebox.showinfo("تنبيه", "لا توجد معدات متاحة للطباعة")
                else:
                    print("تنبيه: لا توجد معدات متاحة للطباعة")
                return None

            # إنشاء اسم ملف تلقائي للاختبار
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"equipment_list_{timestamp}.pdf"

            if parent:
                # اختيار مكان الحفظ في الواجهة الرسومية
                filename = filedialog.asksaveasfilename(
                    title="حفظ قائمة المعدات المتاحة",
                    defaultextension=".pdf",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    initialfile=filename
                )

                if not filename:
                    return None

            # إنشاء ملف PDF
            result = self.create_equipment_pdf(available_equipment, filename)

            if parent and result:
                # عرض رسالة نجاح مع خيار فتح الملف
                open_file = messagebox.askyesno(
                    "نجح",
                    f"تم إنشاء قائمة المعدات المتاحة بنجاح!\n\nالملف: {filename}\n\nهل تريد فتح الملف؟"
                )

                if open_file:
                    os.startfile(filename)

            return result

        except Exception as e:
            error_msg = f"فشل في طباعة قائمة المعدات: {str(e)}"
            if parent:
                messagebox.showerror("خطأ", error_msg)
            else:
                print(f"خطأ: {error_msg}")
            return None
    
    def create_equipment_pdf(self, equipment_list: List[Dict], filename: str):
        """إنشاء ملف PDF لقائمة المعدات"""
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        
        # إعداد الأنماط
        styles = getSampleStyleSheet()
        
        # عنوان التقرير
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # توسيط
            textColor=colors.darkblue,
            fontName=self.arabic_bold_font
        )

        title = Paragraph("قائمة المعدات المتاحة للتأجير", title_style)
        story.append(title)

        # تاريخ التقرير
        date_style = ParagraphStyle(
            'DateStyle',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=20,
            alignment=1,
            fontName=self.arabic_font
        )

        current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        date_text = Paragraph(f"تاريخ التقرير: {current_date}", date_style)
        story.append(date_text)
        
        # إحصائيات سريعة
        stats_data = self.get_equipment_stats(equipment_list)
        stats_table = Table([
            ["إجمالي المعدات المتاحة", str(len(equipment_list))],
            ["الكاميرات", str(stats_data['cameras'])],
            ["العدسات", str(stats_data['lenses'])],
            ["معدات الإضاءة", str(stats_data['lighting'])]
        ])
        
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_bold_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 20))
        
        # جدول المعدات
        # رؤوس الأعمدة
        headers = ["#", "اسم المعدة", "الموديل", "النوع", "الرقم التسلسلي", "السعر اليومي"]
        
        # بيانات الجدول
        table_data = [headers]
        
        for i, equipment in enumerate(equipment_list, 1):
            row = [
                str(i),
                equipment.get('name', ''),
                equipment.get('model', ''),
                equipment.get('category', ''),
                equipment.get('serial_number', ''),
                f"{equipment.get('daily_rate', 0):.2f}"
            ]
            table_data.append(row)
        
        # إنشاء الجدول
        table = Table(table_data, colWidths=[0.5*inch, 2*inch, 1.5*inch, 1.2*inch, 1.5*inch, 1*inch])
        
        # تنسيق الجدول
        table.setStyle(TableStyle([
            # تنسيق الرأس
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), self.arabic_bold_font),
            ('FONTSIZE', (0, 0), (-1, 0), 10),

            # تنسيق البيانات
            ('FONTNAME', (0, 1), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),

            # الحدود
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(table)
        
        # ملاحظة في النهاية
        story.append(Spacer(1, 30))
        note_style = ParagraphStyle(
            'NoteStyle',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.grey,
            alignment=1,
            fontName=self.arabic_font
        )

        note = Paragraph("هذه القائمة تحتوي على المعدات المتاحة للتأجير فقط", note_style)
        story.append(note)
        
        # بناء المستند
        doc.build(story)

        # التحقق من إنشاء الملف وإرجاع المسار
        if os.path.exists(filename):
            print(f"✅ تم إنشاء تقرير المعدات بنجاح: {filename}")
            return filename
        else:
            print(f"❌ فشل في إنشاء التقرير: {filename}")
            return None
    
    def get_equipment_stats(self, equipment_list: List[Dict]) -> Dict:
        """الحصول على إحصائيات المعدات"""
        stats = {
            'cameras': 0,
            'lenses': 0,
            'lighting': 0
        }
        
        for equipment in equipment_list:
            category = equipment.get('category', '').lower()
            if 'كاميرا' in category:
                stats['cameras'] += 1
            elif 'عدسة' in category:
                stats['lenses'] += 1
            elif 'إضاءة' in category:
                stats['lighting'] += 1
        
        return stats

class PrintDialog:
    """نافذة خيارات الطباعة"""
    
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db = db_manager
        self.print_manager = PrintManager(db_manager)
        self.result = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """إنشاء نافذة الطباعة"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("خيارات الطباعة")
        self.dialog.geometry("400x300")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="اختر نوع التقرير للطباعة", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # خيارات الطباعة
        options_frame = ttk.LabelFrame(main_frame, text="خيارات الطباعة", padding="10")
        options_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # زر طباعة قائمة المعدات المتاحة
        equipment_btn = ttk.Button(
            options_frame,
            text="📋 طباعة قائمة المعدات المتاحة",
            command=self.print_available_equipment,
            width=30
        )
        equipment_btn.pack(pady=10)
        
        # معلومات إضافية
        info_label = ttk.Label(
            options_frame,
            text="• سيتم إنشاء ملف PDF جاهز للطباعة\n• يحتوي على المعدات المتاحة فقط\n• مرتب حسب النوع والاسم",
            justify=tk.LEFT,
            foreground="gray"
        )
        info_label.pack(pady=10)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="إغلاق", command=self.close).pack(side=tk.RIGHT, padx=(5, 0))
    
    def print_available_equipment(self):
        """طباعة قائمة المعدات المتاحة"""
        self.print_manager.print_available_equipment_list(self.dialog)
    
    def close(self):
        """إغلاق النافذة"""
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
        return self.result
