{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-users-cog me-2 text-primary"></i>
                        إدارة المستخدمين والصلاحيات
                    </h1>
                    <p class="text-muted mb-0">إدارة المستخدمين وصلاحياتهم في النظام</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                    </button>
                    <button class="btn btn-success ms-2" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>قائمة المستخدمين
                        <span class="badge bg-primary ms-2">{{ users|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>الدور</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>
                                        <strong>{{ user.username }}</strong>
                                    </td>
                                    <td>{{ user.full_name }}</td>
                                    <td>
                                        {% if user.role == 'admin' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-crown me-1"></i>مدير النظام
                                        </span>
                                        {% elif user.role == 'manager' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-user-tie me-1"></i>مدير فرعي
                                        </span>
                                        {% elif user.role == 'employee' %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-user me-1"></i>موظف
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-question me-1"></i>{{ user.role }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                        {% else %}
                                        <span class="badge bg-secondary">معطل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            {{ user.created_at or 'غير محدد' }}
                                        </small>
                                    </td>
                                    <td>
                                        {% if user.username != 'admin' %}
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser({{ user.id }}, '{{ user.username }}', '{{ user.full_name }}', '{{ user.role }}', {{ user.is_active|lower }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning me-1" onclick="managePermissions({{ user.id }})" title="إدارة الصلاحيات">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        {% if user.is_active %}
                                        <button class="btn btn-sm btn-outline-secondary me-1" onclick="toggleUserStatus({{ user.id }}, false)" title="تعطيل">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% else %}
                                        <button class="btn btn-sm btn-outline-success me-1" onclick="toggleUserStatus({{ user.id }}, true)" title="تفعيل">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }}, '{{ user.username }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% else %}
                                        <span class="badge bg-warning">مدير النظام الرئيسي</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Permissions Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>دليل الصلاحيات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">صلاحيات المدير:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>جميع صلاحيات النظام</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة المستخدمين</li>
                                <li><i class="fas fa-check text-success me-2"></i>إدارة الصلاحيات</li>
                                <li><i class="fas fa-check text-success me-2"></i>النسخ الاحتياطي</li>
                                <li><i class="fas fa-check text-success me-2"></i>إعدادات النظام</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-info">صلاحيات الموظف (قابلة للتخصيص):</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-eye text-info me-2"></i>عرض المعدات والعملاء</li>
                                <li><i class="fas fa-plus text-info me-2"></i>إضافة إيجارات جديدة</li>
                                <li><i class="fas fa-edit text-info me-2"></i>تعديل البيانات (حسب الصلاحية)</li>
                                <li><i class="fas fa-chart-bar text-info me-2"></i>عرض التقارير</li>
                                <li><i class="fas fa-times text-danger me-2"></i>لا يمكن حذف البيانات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <!-- معلومات تسجيل الدخول -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-key me-2"></i>معلومات تسجيل الدخول
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-1"></i>اسم المستخدم:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="newUsername" required
                                   placeholder="أدخل اسم المستخدم">
                            <div class="form-text">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-lock me-1"></i>كلمة المرور:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control" id="newPassword" required
                                   placeholder="أدخل كلمة مرور قوية">
                            <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                        </div>
                    </div>

                    <!-- المعلومات الشخصية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-id-card me-2"></i>المعلومات الشخصية
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-signature me-1"></i>الاسم الكامل:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="newFullName" required
                                   placeholder="أدخل الاسم الكامل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-envelope me-1"></i>البريد الإلكتروني:
                            </label>
                            <input type="email" class="form-control" id="newEmail"
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-phone me-1"></i>رقم الهاتف:
                            </label>
                            <input type="tel" class="form-control" id="newPhone"
                                   placeholder="01234567890">
                        </div>
                    </div>

                    <!-- الدور والصلاحيات -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-warning border-bottom pb-2 mb-3">
                                <i class="fas fa-shield-alt me-2"></i>الدور والصلاحيات
                            </h6>
                        </div>
                        <div class="col-md-8 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user-tag me-1"></i>الدور:
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="newRole" required onchange="showRolePermissions()">
                                <option value="">اختر الدور</option>
                                <option value="admin">👑 مدير النظام - صلاحيات كاملة</option>
                                <option value="manager">👔 مدير فرعي - صلاحيات إدارية محدودة</option>
                                <option value="employee">👤 موظف - صلاحيات أساسية</option>
                            </select>
                            <div class="form-text">اختر الدور المناسب حسب مستوى الصلاحيات المطلوبة</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-1"></i>حالة الحساب:
                            </label>
                            <select class="form-select" id="newIsActive">
                                <option value="true">✅ نشط</option>
                                <option value="false">❌ معطل</option>
                            </select>
                        </div>
                    </div>

                    <!-- معاينة الصلاحيات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info" id="rolePermissionsPreview" style="display: none;">
                                <h6><i class="fas fa-info-circle me-2"></i>صلاحيات هذا الدور:</h6>
                                <div id="permissionsList"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-success" onclick="saveUser()">
                    <i class="fas fa-save me-1"></i>حفظ المستخدم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>تعديل بيانات المستخدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">

                    <!-- معلومات أساسية -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-warning border-bottom pb-2 mb-3">
                                <i class="fas fa-info-circle me-2"></i>المعلومات الأساسية
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-1"></i>اسم المستخدم:
                            </label>
                            <input type="text" class="form-control" id="editUsername" readonly
                                   style="background-color: #f8f9fa;">
                            <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-signature me-1"></i>الاسم الكامل:
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="editFullName" required
                                   placeholder="أدخل الاسم الكامل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-envelope me-1"></i>البريد الإلكتروني:
                            </label>
                            <input type="email" class="form-control" id="editEmail"
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-phone me-1"></i>رقم الهاتف:
                            </label>
                            <input type="tel" class="form-control" id="editPhone"
                                   placeholder="01234567890">
                        </div>
                    </div>

                    <!-- الدور والحالة -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-shield-alt me-2"></i>الدور والحالة
                            </h6>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user-tag me-1"></i>الدور:
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="editRole" required onchange="showEditRolePermissions()">
                                <option value="admin">👑 مدير النظام - صلاحيات كاملة</option>
                                <option value="manager">👔 مدير فرعي - صلاحيات إدارية محدودة</option>
                                <option value="employee">👤 موظف - صلاحيات أساسية</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-toggle-on me-1"></i>حالة الحساب:
                            </label>
                            <select class="form-select" id="editIsActive">
                                <option value="true">✅ نشط</option>
                                <option value="false">❌ معطل</option>
                            </select>
                        </div>
                    </div>

                    <!-- معاينة الصلاحيات -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info" id="editRolePermissionsPreview">
                                <h6><i class="fas fa-info-circle me-2"></i>صلاحيات هذا الدور:</h6>
                                <div id="editPermissionsList"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-warning" onclick="updateUser()">
                    <i class="fas fa-save me-1"></i>حفظ التعديلات
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Show add user modal
    function showAddUserModal() {
        const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
        modal.show();
    }
    
    // Show role permissions preview
    function showRolePermissions() {
        const role = document.getElementById('newRole').value;
        const preview = document.getElementById('rolePermissionsPreview');
        const permissionsList = document.getElementById('permissionsList');

        if (!role) {
            preview.style.display = 'none';
            return;
        }

        let permissions = [];

        if (role === 'admin') {
            permissions = [
                '✅ جميع صلاحيات النظام',
                '✅ إدارة المستخدمين والأدوار',
                '✅ إدارة المعدات (إضافة، تعديل، حذف)',
                '✅ إدارة العملاء (إضافة، تعديل، حذف)',
                '✅ إدارة الإيجارات (إضافة، تعديل، حذف)',
                '✅ عرض وتصدير التقارير',
                '✅ إعدادات النظام',
                '✅ النسخ الاحتياطي والاستعادة'
            ];
        } else if (role === 'manager') {
            permissions = [
                '✅ إدارة المعدات (إضافة، تعديل، حذف)',
                '✅ إدارة العملاء (إضافة، تعديل، حذف)',
                '✅ إدارة الإيجارات (إضافة، تعديل، حذف)',
                '✅ عرض وتصدير التقارير',
                '✅ بعض إعدادات النظام',
                '❌ إدارة المستخدمين',
                '❌ النسخ الاحتياطي'
            ];
        } else if (role === 'employee') {
            permissions = [
                '✅ عرض المعدات',
                '✅ إدارة العملاء (إضافة، تعديل)',
                '✅ إدارة الإيجارات (إضافة، تعديل، إرجاع)',
                '✅ عرض التقارير الأساسية',
                '❌ حذف البيانات الحساسة',
                '❌ إدارة المستخدمين',
                '❌ إعدادات النظام'
            ];
        }

        permissionsList.innerHTML = permissions.map(p => `<small class="d-block">${p}</small>`).join('');
        preview.style.display = 'block';
    }

    // Save user
    function saveUser() {
        const data = {
            username: document.getElementById('newUsername').value.trim(),
            full_name: document.getElementById('newFullName').value.trim(),
            password: document.getElementById('newPassword').value,
            role: document.getElementById('newRole').value,
            email: document.getElementById('newEmail').value.trim() || null,
            phone: document.getElementById('newPhone').value.trim() || null,
            is_active: document.getElementById('newIsActive').value === 'true'
        };

        // التحقق من الحقول المطلوبة
        if (!data.username || !data.full_name || !data.password || !data.role) {
            showAlert('danger', 'يرجى ملء جميع الحقول المطلوبة (*)');
            return;
        }

        // التحقق من اسم المستخدم
        if (data.username.includes(' ')) {
            showAlert('danger', 'اسم المستخدم لا يجب أن يحتوي على مسافات');
            return;
        }

        if (data.username.length < 3) {
            showAlert('danger', 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
            return;
        }

        // التحقق من كلمة المرور
        if (data.password.length < 6) {
            showAlert('danger', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        // التحقق من البريد الإلكتروني إذا تم إدخاله
        if (data.email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showAlert('danger', 'يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
        }

        // التحقق من رقم الهاتف إذا تم إدخاله
        if (data.phone) {
            const phoneRegex = /^[0-9+\-\s()]+$/;
            if (!phoneRegex.test(data.phone)) {
                showAlert('danger', 'يرجى إدخال رقم هاتف صحيح');
                return;
            }
        }

        fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();

                // مسح النموذج
                document.getElementById('addUserForm').reset();
                document.getElementById('rolePermissionsPreview').style.display = 'none';

                location.reload();
            } else {
                showAlert('danger', data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في إضافة المستخدم');
        });
    }
    
    // Show edit role permissions preview
    function showEditRolePermissions() {
        const role = document.getElementById('editRole').value;
        const permissionsList = document.getElementById('editPermissionsList');

        let permissions = [];

        if (role === 'admin') {
            permissions = [
                '✅ جميع صلاحيات النظام',
                '✅ إدارة المستخدمين والأدوار',
                '✅ إدارة المعدات (إضافة، تعديل، حذف)',
                '✅ إدارة العملاء (إضافة، تعديل، حذف)',
                '✅ إدارة الإيجارات (إضافة، تعديل، حذف)',
                '✅ عرض وتصدير التقارير',
                '✅ إعدادات النظام',
                '✅ النسخ الاحتياطي والاستعادة'
            ];
        } else if (role === 'manager') {
            permissions = [
                '✅ إدارة المعدات (إضافة، تعديل، حذف)',
                '✅ إدارة العملاء (إضافة، تعديل، حذف)',
                '✅ إدارة الإيجارات (إضافة، تعديل، حذف)',
                '✅ عرض وتصدير التقارير',
                '✅ بعض إعدادات النظام',
                '❌ إدارة المستخدمين',
                '❌ النسخ الاحتياطي'
            ];
        } else if (role === 'employee') {
            permissions = [
                '✅ عرض المعدات',
                '✅ إدارة العملاء (إضافة، تعديل)',
                '✅ إدارة الإيجارات (إضافة، تعديل، إرجاع)',
                '✅ عرض التقارير الأساسية',
                '❌ حذف البيانات الحساسة',
                '❌ إدارة المستخدمين',
                '❌ إعدادات النظام'
            ];
        }

        permissionsList.innerHTML = permissions.map(p => `<small class="d-block">${p}</small>`).join('');
    }

    // Edit user
    function editUser(id, username, fullName, role, isActive) {
        // ملء النموذج بالبيانات الحالية
        document.getElementById('editUserId').value = id;
        document.getElementById('editUsername').value = username;
        document.getElementById('editFullName').value = fullName;
        document.getElementById('editRole').value = role;
        document.getElementById('editIsActive').value = isActive.toString();
        document.getElementById('editEmail').value = '';
        document.getElementById('editPhone').value = '';

        // عرض صلاحيات الدور الحالي
        showEditRolePermissions();

        // إظهار النموذج
        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        modal.show();
    }

    // Update user
    function updateUser() {
        const id = document.getElementById('editUserId').value;
        const data = {
            full_name: document.getElementById('editFullName').value.trim(),
            role: document.getElementById('editRole').value,
            is_active: document.getElementById('editIsActive').value === 'true',
            email: document.getElementById('editEmail').value.trim() || null,
            phone: document.getElementById('editPhone').value.trim() || null
        };

        if (!data.full_name) {
            showAlert('danger', 'يرجى إدخال الاسم الكامل');
            return;
        }

        // التحقق من البريد الإلكتروني إذا تم إدخاله
        if (data.email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showAlert('danger', 'يرجى إدخال بريد إلكتروني صحيح');
                return;
            }
        }

        // التحقق من رقم الهاتف إذا تم إدخاله
        if (data.phone) {
            const phoneRegex = /^[0-9+\-\s()]+$/;
            if (!phoneRegex.test(data.phone)) {
                showAlert('danger', 'يرجى إدخال رقم هاتف صحيح');
                return;
            }
        }

        fetch(`/api/users/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                location.reload();
            } else {
                showAlert('danger', data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في تعديل المستخدم');
        });
    }

    // Manage permissions
    function managePermissions(id) {
        showAlert('info', 'ميزة إدارة الصلاحيات المتقدمة قيد التطوير');
    }

    // Toggle user status
    function toggleUserStatus(id, activate) {
        const action = activate ? 'تفعيل' : 'تعطيل';
        if (confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
            const data = {
                is_active: activate
            };

            fetch(`/api/users/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    location.reload();
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', `حدث خطأ في ${action} المستخدم`);
            });
        }
    }

    // Delete user
    function deleteUser(id, username) {
        if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
            fetch(`/api/users/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    location.reload();
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ في حذف المستخدم');
            });
        }
    }
</script>
{% endblock %}
