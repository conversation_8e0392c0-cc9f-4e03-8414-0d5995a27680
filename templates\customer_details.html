{% extends "base.html" %}

{% block title %}تفاصيل العميل - {{ customer.name }}{% endblock %}

{% block extra_css %}
<style>
    .customer-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 4px solid;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-card.total { border-left-color: #007bff; }
    .stat-card.active { border-left-color: #28a745; }
    .stat-card.completed { border-left-color: #17a2b8; }
    .stat-card.overdue { border-left-color: #dc3545; }
    .stat-card.spent { border-left-color: #ffc107; }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .rental-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        transition: transform 0.3s ease;
    }
    
    .rental-card:hover {
        transform: translateY(-2px);
    }
    
    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
    }
    
    .status-active { background-color: #d4edda; color: #155724; }
    .status-returned { background-color: #d1ecf1; color: #0c5460; }
    .status-overdue { background-color: #f8d7da; color: #721c24; }
    
    .customer-photo {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 1rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 2rem;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -1.5rem;
        top: 0.5rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007bff;
        border: 3px solid white;
        box-shadow: 0 0 0 3px #dee2e6;
    }
    
    .equipment-badge {
        display: inline-block;
        padding: 0.3rem 0.6rem;
        margin: 0.2rem;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 15px;
        font-size: 0.8rem;
    }

    /* تحسين عرض الإيجارات النشطة */
    .border-left-warning {
        border-left: 4px solid #ffc107 !important;
    }

    .badge.bg-active {
        background-color: #28a745 !important;
    }

    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #212529 !important;
    }

    .badge.bg-overdue {
        background-color: #dc3545 !important;
    }

    .rental-info {
        background: #f8f9fa;
        padding: 8px;
        border-radius: 6px;
        margin-bottom: 10px;
    }

    .rental-info small {
        margin-bottom: 2px;
    }

    .card.h-100 {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card.h-100:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Customer Header -->
    <div class="customer-header">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                {% if customer.photo_path %}
                <img src="{{ url_for('static', filename='uploads/customers/' + customer.photo_path) }}" 
                     alt="صورة العميل" class="customer-photo">
                {% else %}
                <div class="customer-photo d-flex align-items-center justify-content-center" 
                     style="background: rgba(255,255,255,0.2);">
                    <i class="fas fa-user fa-3x"></i>
                </div>
                {% endif %}
            </div>
            <div class="col-md-8">
                <h1 class="mb-2">{{ customer.name }}</h1>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><i class="fas fa-phone me-2"></i>{{ customer.phone }}</p>
                        {% if customer.national_id %}
                        <p class="mb-1"><i class="fas fa-id-card me-2"></i>{{ customer.national_id }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><i class="fas fa-calendar me-2"></i>عميل منذ: {{ customer.created_date[:10] }}</p>
                        {% if stats.last_rental %}
                        <p class="mb-1"><i class="fas fa-clock me-2"></i>آخر إيجار: {{ stats.last_rental.rental_date }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-2 text-center">
                <a href="{{ url_for('customers') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stat-card total">
                <div class="stat-number">{{ stats.total_rentals }}</div>
                <div class="stat-label">إجمالي الإيجارات</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card active">
                <div class="stat-number">{{ stats.active_rentals }}</div>
                <div class="stat-label">إيجارات نشطة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card completed">
                <div class="stat-number">{{ stats.completed_rentals }}</div>
                <div class="stat-label">إيجارات مكتملة</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card overdue">
                <div class="stat-number">{{ stats.overdue_rentals }}</div>
                <div class="stat-label">إيجارات متأخرة</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stat-card spent">
                <div class="stat-number">{{ "%.2f"|format(stats.total_spent) }}</div>
                <div class="stat-label">إجمالي المبلغ المدفوع (جنيه)</div>
            </div>
        </div>
    </div>

    <!-- Current Equipment -->
    {% if stats.current_equipment %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-camera me-2"></i>المعدات المستأجرة حالياً
                        <span class="badge bg-dark ms-2">{{ stats.current_equipment|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for equipment in stats.current_equipment %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 border-left-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            {% if equipment.get('is_multi') %}
                                            <i class="fas fa-layer-group me-1 text-success" title="إيجار متعدد"></i>
                                            {% else %}
                                            <i class="fas fa-video me-1 text-warning"></i>
                                            {% endif %}
                                            {{ equipment.equipment_name }}
                                        </h6>
                                        <span class="badge bg-{{ equipment.status_class }}">
                                            {% if equipment.get('is_multi') %}
                                            متعدد: {{ equipment.status_text }}
                                            {% else %}
                                            {{ equipment.status_text }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    <p class="card-text text-muted small mb-2">{{ equipment.equipment_model }}</p>
                                    <div class="rental-info">
                                        <small class="text-muted d-block">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            تاريخ الإيجار: {{ equipment.rental_date }}
                                        </small>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-calendar-check me-1"></i>
                                            تاريخ الإرجاع: {{ equipment.return_date }}
                                        </small>
                                        <small class="text-muted d-block">
                                            <i class="fas fa-money-bill me-1"></i>
                                            السعر: {{ equipment.price }} جنيه
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        {% if equipment.get('is_multi') %}
                                        <button class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewMultiRentalDetails({{ equipment.rental_group_id }})">
                                            <i class="fas fa-eye"></i> تفاصيل المجموعة
                                        </button>
                                        <button class="btn btn-sm btn-outline-warning me-1"
                                                onclick="returnMultiRentalItem({{ equipment.rental_group_id }}, {{ equipment.item_id }})">
                                            <i class="fas fa-undo"></i> إرجاع هذه المعدة
                                        </button>
                                        {% else %}
                                        <button class="btn btn-sm btn-outline-primary me-1"
                                                onclick="viewRentalDetails({{ equipment.id }})">
                                            <i class="fas fa-eye"></i> تفاصيل
                                        </button>
                                        <button class="btn btn-sm btn-outline-success"
                                                onclick="returnRental({{ equipment.id }})">
                                            <i class="fas fa-undo"></i> إرجاع
                                        </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>المعدات المستأجرة حالياً
                    </h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">لا توجد معدات مستأجرة حالياً</h6>
                    <p class="text-muted">جميع المعدات المستأجرة من هذا العميل تم إرجاعها</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Rentals History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>تاريخ الإيجارات
                        <span class="badge bg-primary ms-2">{{ rentals|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if rentals %}
                    <div class="timeline">
                        {% for rental in rentals %}
                        <div class="timeline-item">
                            <div class="rental-card card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h6 class="mb-1">{{ rental.equipment_name }}</h6>
                                            <small class="text-muted">{{ rental.equipment_model }}</small>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">تاريخ الإيجار</small>
                                            <div>{{ rental.rental_date }}</div>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">تاريخ الإرجاع</small>
                                            <div>{{ rental.return_date }}</div>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">المبلغ</small>
                                            <div class="fw-bold">{{ rental.price }} جنيه</div>
                                        </div>
                                        <div class="col-md-2">
                                            {% if rental.status == 'Active' %}
                                            <span class="status-badge status-active">نشط</span>
                                            {% elif rental.status == 'Returned' %}
                                            <span class="status-badge status-returned">مُرجع</span>
                                            {% elif rental.status == 'Overdue' %}
                                            <span class="status-badge status-overdue">متأخر</span>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-1">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="viewRentalDetails({{ rental.id }})">
                                                        <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                                    </a></li>
                                                    {% if rental.status == 'Active' %}
                                                    <li><a class="dropdown-item" href="#" onclick="returnRental({{ rental.id }})">
                                                        <i class="fas fa-undo me-2"></i>إرجاع المعدة
                                                    </a></li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    {% if rental.notes %}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <small class="text-muted">
                                                <i class="fas fa-sticky-note me-1"></i>{{ rental.notes }}
                                            </small>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إيجارات لهذا العميل</h5>
                        <p class="text-muted">يمكنك إنشاء إيجار جديد من صفحة الإيجارات</p>
                        <a href="{{ url_for('rentals') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إنشاء إيجار جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function viewRentalDetails(rentalId) {
        // يمكن إضافة نافذة منبثقة لعرض تفاصيل الإيجار
        alert('عرض تفاصيل الإيجار رقم: ' + rentalId);
    }
    
    function returnRental(rentalId) {
        if (confirm('هل أنت متأكد من إرجاع هذه المعدة؟')) {
            // إرسال طلب إرجاع المعدة
            fetch(`/api/rentals/${rentalId}/return`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }
    }

    function viewMultiRentalDetails(rentalGroupId) {
        fetch(`/api/multi-rentals/${rentalGroupId}/items`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const items = data.items;
                    let itemsHtml = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>المعدة</th><th>السعر اليومي</th><th>عدد الأيام</th><th>المجموع الفرعي</th><th>الحالة</th></tr></thead><tbody>';

                    let totalCost = 0;
                    items.forEach(item => {
                        const statusBadge = item.status === 'Active' ?
                            '<span class="badge bg-success">نشط</span>' :
                            '<span class="badge bg-secondary">مُرجع</span>';

                        itemsHtml += `
                            <tr>
                                <td>${item.equipment_name}</td>
                                <td>${item.rental_price} ج.م</td>
                                <td>${item.days_count}</td>
                                <td>${item.subtotal} ج.م</td>
                                <td>${statusBadge}</td>
                            </tr>
                        `;
                        totalCost += item.subtotal;
                    });

                    itemsHtml += `</tbody><tfoot><tr class="table-success"><th colspan="3">الإجمالي</th><th>${totalCost.toFixed(2)} ج.م</th><th></th></tr></tfoot></table></div>`;

                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <h6><i class="fas fa-layer-group me-2"></i>تفاصيل الإيجار المتعدد #MR${rentalGroupId}</h6>
                        ${itemsHtml}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;

                    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في عرض تفاصيل الإيجار المتعدد');
            });
    }

    function returnMultiRentalItem(rentalGroupId, itemId) {
        if (confirm('هل أنت متأكد من إرجاع هذه المعدة من الإيجار المتعدد؟')) {
            fetch(`/api/multi-rentals/${rentalGroupId}/return`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    returned_items: [{
                        item_id: itemId,
                        condition: 'Good',
                        notes: ''
                    }]
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في إرجاع المعدة');
            });
        }
    }
</script>
{% endblock %}
