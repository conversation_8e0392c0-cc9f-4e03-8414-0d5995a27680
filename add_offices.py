#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from config.settings import get_config

def add_offices():
    """إضافة بيانات المكاتب"""
    config = get_config()
    conn = sqlite3.connect(config.DATABASE_PATH)
    cursor = conn.cursor()

    # إضافة بيانات المكاتب
    offices = [
        ('استوديو الضوء الذهبي', 'شارع التحرير، القاهرة', '0223456789', 'محمد أحمد', 'supplier'),
        ('مكتب الرؤية الإبداعية', 'شارع الهرم، الجيزة', '0233456789', 'سارة محمد', 'partner'),
        ('فرع الإسكندرية', 'كورنيش الإسكندرية', '0334567890', 'أحمد علي', 'branch'),
        ('استوديو النور المتقدم', 'مدينة نصر، القاهرة', '0225678901', 'فاطمة حسن', 'supplier'),
        ('مركز التصوير المحترف', 'المعادي، القاهرة', '0227890123', 'خالد محمود', 'partner')
    ]

    for name, address, phone, contact, office_type in offices:
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO offices (name, address, phone, contact_person, office_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, address, phone, contact, office_type))
            print(f'تم إضافة المكتب: {name}')
        except Exception as e:
            print(f'خطأ في إضافة المكتب {name}: {e}')

    conn.commit()
    conn.close()
    print('تم إنشاء بيانات المكاتب بنجاح')

if __name__ == '__main__':
    add_offices()
