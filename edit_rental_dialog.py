#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تعديل الإيجارات
Edit Rental Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import font
from datetime import datetime, date
from typing import Dict, List, Optional

class EditRentalDialog:
    """نافذة تعديل الإيجار"""
    
    def __init__(self, parent, db_manager, user_manager, rental_data: Dict):
        self.parent = parent
        self.db = db_manager
        self.user_manager = user_manager
        self.rental_data = rental_data
        self.result = None
        
        # إعداد الخط العربي
        try:
            self.arabic_font = font.Font(family="Arial Unicode MS", size=10)
        except:
            self.arabic_font = font.Font(family="Arial", size=10)
        
        self.create_dialog()
        self.load_data()
    
    def create_dialog(self):
        """إنشاء نافذة الحوار"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(f"تعديل الإيجار رقم {self.rental_data['id']}")
        self.dialog.geometry("600x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.geometry(f"+{self.parent.winfo_rootx() + 50}+{self.parent.winfo_rooty() + 50}")
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text=f"تعديل الإيجار رقم {self.rental_data['id']}", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = ttk.LabelFrame(main_frame, text="بيانات الإيجار", padding="15")
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # تكوين الشبكة
        form_frame.columnconfigure(1, weight=1)
        
        # العميل (للعرض فقط)
        ttk.Label(form_frame, text="العميل:", font=self.arabic_font).grid(row=0, column=0, sticky=tk.W, pady=5)
        self.customer_label = ttk.Label(form_frame, text="", font=self.arabic_font, foreground="blue")
        self.customer_label.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # المعدة الحالية (للعرض فقط)
        ttk.Label(form_frame, text="المعدة الحالية:", font=self.arabic_font).grid(row=1, column=0, sticky=tk.W, pady=5)
        self.current_equipment_label = ttk.Label(form_frame, text="", font=self.arabic_font, foreground="blue")
        self.current_equipment_label.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # اختيار معدة جديدة
        ttk.Label(form_frame, text="تغيير المعدة:", font=self.arabic_font).grid(row=2, column=0, sticky=tk.W, pady=5)
        
        equipment_frame = ttk.Frame(form_frame)
        equipment_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        equipment_frame.columnconfigure(0, weight=1)
        
        self.equipment_var = tk.StringVar()
        self.equipment_combo = ttk.Combobox(equipment_frame, textvariable=self.equipment_var,
                                           state="readonly", font=self.arabic_font)
        self.equipment_combo.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(equipment_frame, text="إبقاء نفس المعدة", 
                  command=self.keep_same_equipment).grid(row=0, column=1, padx=(5, 0))
        
        # تاريخ الإيجار
        ttk.Label(form_frame, text="تاريخ الإيجار:", font=self.arabic_font).grid(row=3, column=0, sticky=tk.W, pady=5)
        self.rental_date_entry = ttk.Entry(form_frame, font=self.arabic_font)
        self.rental_date_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # تاريخ الإرجاع
        ttk.Label(form_frame, text="تاريخ الإرجاع:", font=self.arabic_font).grid(row=4, column=0, sticky=tk.W, pady=5)
        self.return_date_entry = ttk.Entry(form_frame, font=self.arabic_font)
        self.return_date_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # السعر
        ttk.Label(form_frame, text="السعر:", font=self.arabic_font).grid(row=5, column=0, sticky=tk.W, pady=5)
        self.price_entry = ttk.Entry(form_frame, font=self.arabic_font)
        self.price_entry.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # الملاحظات
        ttk.Label(form_frame, text="الملاحظات:", font=self.arabic_font).grid(row=6, column=0, sticky=tk.W+tk.N, pady=5)
        self.notes_text = tk.Text(form_frame, height=4, width=40, font=self.arabic_font)
        self.notes_text.grid(row=6, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # شريط التمرير للملاحظات
        notes_scrollbar = ttk.Scrollbar(form_frame, orient=tk.VERTICAL, command=self.notes_text.yview)
        notes_scrollbar.grid(row=6, column=2, sticky=(tk.N, tk.S), pady=5)
        self.notes_text.configure(yscrollcommand=notes_scrollbar.set)
        
        # تلميحات التاريخ
        ttk.Label(form_frame, text="(YYYY-MM-DD)", font=("Arial", 8)).grid(row=3, column=2, sticky=tk.W, padx=5)
        ttk.Label(form_frame, text="(YYYY-MM-DD)", font=("Arial", 8)).grid(row=4, column=2, sticky=tk.W, padx=5)
        
        # معلومات التعديل
        info_frame = ttk.LabelFrame(main_frame, text="معلومات التعديل", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.modification_info = ttk.Label(info_frame, text="", font=self.arabic_font, foreground="gray")
        self.modification_info.pack()
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        ttk.Button(buttons_frame, text="حفظ التعديلات", 
                  command=self.save_changes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(buttons_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.RIGHT)
    
    def load_data(self):
        """تحميل بيانات الإيجار"""
        # عرض معلومات العميل
        self.customer_label.config(text=f"{self.rental_data.get('customer_name', 'غير محدد')}")
        
        # عرض المعدة الحالية
        current_equipment = f"{self.rental_data.get('equipment_name', 'غير محدد')} - {self.rental_data.get('equipment_model', '')}"
        self.current_equipment_label.config(text=current_equipment)
        
        # تحميل المعدات المتاحة
        self.load_available_equipment()
        
        # تحميل البيانات الحالية
        self.rental_date_entry.insert(0, self.rental_data.get('rental_date', ''))
        self.return_date_entry.insert(0, self.rental_data.get('return_date', ''))
        self.price_entry.insert(0, str(self.rental_data.get('price', 0)))
        
        # تحميل الملاحظات
        notes = self.rental_data.get('notes', '')
        if notes:
            self.notes_text.insert("1.0", notes)
        
        # معلومات التعديل
        current_user = self.user_manager.current_user['full_name']
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.modification_info.config(text=f"سيتم تسجيل التعديل بواسطة: {current_user} في {current_time}")
    
    def load_available_equipment(self):
        """تحميل المعدات المتاحة"""
        equipment_list = self.db.get_equipment()
        
        # المعدات المتاحة + المعدة الحالية
        available_equipment = []
        current_equipment_id = self.rental_data.get('equipment_id')
        
        for eq in equipment_list:
            if eq['status'] == 'Available' or eq['id'] == current_equipment_id:
                display_text = f"{eq['name']} - {eq['model']} ({eq['serial_number']})"
                available_equipment.append((eq['id'], display_text))
        
        self.equipment_combo['values'] = [item[1] for item in available_equipment]
        self.equipment_data = {item[1]: item[0] for item in available_equipment}
        
        # تحديد المعدة الحالية كافتراضية
        current_display = None
        for eq_id, display in available_equipment:
            if eq_id == current_equipment_id:
                current_display = display
                break
        
        if current_display:
            self.equipment_combo.set(current_display)
    
    def keep_same_equipment(self):
        """الإبقاء على نفس المعدة"""
        current_equipment_id = self.rental_data.get('equipment_id')
        for display, eq_id in self.equipment_data.items():
            if eq_id == current_equipment_id:
                self.equipment_combo.set(display)
                break
    
    def save_changes(self):
        """حفظ التعديلات"""
        try:
            # التحقق من البيانات
            rental_date = self.rental_date_entry.get().strip()
            return_date = self.return_date_entry.get().strip()
            price_text = self.price_entry.get().strip()
            notes = self.notes_text.get("1.0", tk.END).strip()
            
            if not rental_date or not return_date or not price_text:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            try:
                price = float(price_text)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                return
            
            # الحصول على المعدة المختارة
            selected_equipment = self.equipment_combo.get()
            if not selected_equipment:
                messagebox.showerror("خطأ", "يرجى اختيار معدة")
                return
            
            equipment_id = self.equipment_data.get(selected_equipment)
            if not equipment_id:
                messagebox.showerror("خطأ", "معدة غير صحيحة")
                return
            
            # تحديث الإيجار
            success = self.update_rental(equipment_id, rental_date, return_date, price, notes)
            
            if success:
                messagebox.showinfo("نجح", "تم تحديث الإيجار بنجاح")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الإيجار")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
    
    def update_rental(self, equipment_id: int, rental_date: str, return_date: str, 
                     price: float, notes: str) -> bool:
        """تحديث بيانات الإيجار"""
        try:
            import sqlite3
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            rental_id = self.rental_data['id']
            current_equipment_id = self.rental_data.get('equipment_id')
            
            # إذا تغيرت المعدة، نحتاج لتحديث حالة المعدات
            if equipment_id != current_equipment_id:
                # إرجاع المعدة القديمة لحالة متاح
                cursor.execute("UPDATE equipment SET status = 'Available' WHERE id = ?", 
                             (current_equipment_id,))
                
                # تحديث المعدة الجديدة لحالة مؤجر
                cursor.execute("UPDATE equipment SET status = 'Rented' WHERE id = ?", 
                             (equipment_id,))
            
            # تحديث بيانات الإيجار
            cursor.execute("""
                UPDATE rentals 
                SET equipment_id = ?, rental_date = ?, return_date = ?, price = ?, notes = ?
                WHERE id = ?
            """, (equipment_id, rental_date, return_date, price, notes, rental_id))
            
            conn.commit()
            conn.close()
            
            # تسجيل التعديل في سجل النشاطات
            user_id = self.user_manager.current_user['id']
            self.user_manager._log_activity(user_id, "RENTAL_MODIFIED", 
                                          f"تم تعديل الإيجار رقم {rental_id}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث الإيجار: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return False
    
    def cancel(self):
        """إلغاء التعديل"""
        self.result = False
        self.dialog.destroy()
    
    def show(self):
        """عرض النافذة"""
        self.dialog.wait_window()
        return self.result
