{% extends "base.html" %}

{% block title %}النسخ الاحتياطي{% endblock %}

{% block extra_css %}
<style>
    .backup-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .backup-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        margin-bottom: 1.5rem;
    }
    
    .backup-card:hover {
        transform: translateY(-5px);
    }
    
    .backup-item {
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        background: white;
        transition: all 0.3s ease;
    }
    
    .backup-item:hover {
        border-color: #007bff;
        box-shadow: 0 2px 10px rgba(0,123,255,0.1);
    }
    
    .backup-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .backup-details {
        flex-grow: 1;
    }
    
    .backup-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .file-size {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .backup-date {
        color: #495057;
        font-weight: 500;
    }
    
    .create-backup-btn {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .create-backup-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(40,167,69,0.3);
        color: white;
    }
    
    .auto-backup-card {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        color: white;
        border-radius: 15px;
    }
    
    .backup-stats {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .progress-container {
        display: none;
        margin-top: 1rem;
    }
    
    .backup-icon {
        font-size: 1.2rem;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="backup-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-shield-alt me-3"></i>النسخ الاحتياطي
                </h1>
                <p class="mb-0">إدارة النسخ الاحتياطية لحماية بيانات النظام</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn create-backup-btn" onclick="createBackup()">
                    <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="backup-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ backup_files|length }}</div>
                    <div class="stat-label">إجمالي النسخ</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number" id="totalSize">
                        {{ "%.1f"|format(backup_files|sum(attribute='size_mb')) }}
                    </div>
                    <div class="stat-label">الحجم الإجمالي (MB)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">
                        {% if backup_files %}
                        {{ backup_files[0].date.strftime('%d/%m') }}
                        {% else %}
                        --
                        {% endif %}
                    </div>
                    <div class="stat-label">آخر نسخة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number text-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-label">حالة النظام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container" id="progressContainer">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-cog fa-spin me-2"></i>جاري إنشاء النسخة الاحتياطية...
                </h6>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%" id="backupProgress">
                    </div>
                </div>
                <small class="text-muted mt-2 d-block">يرجى الانتظار حتى اكتمال العملية</small>
            </div>
        </div>
    </div>

    <!-- Backup Files -->
    <div class="row">
        <div class="col-md-8">
            <div class="backup-card card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-archive me-2"></i>النسخ الاحتياطية المتاحة
                        <span class="badge bg-primary ms-2">{{ backup_files|length }}</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if backup_files %}
                    <div id="backupFilesList">
                        {% for backup in backup_files %}
                        <div class="backup-item" id="backup-{{ loop.index }}">
                            <div class="backup-info">
                                <div class="backup-details">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-file-archive backup-icon text-success"></i>
                                        <strong>{{ backup.name }}</strong>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="backup-date">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ backup.date.strftime('%Y-%m-%d %H:%M:%S') }}
                                            </small>
                                        </div>
                                        <div class="col-md-6">
                                            <small class="file-size">
                                                <i class="fas fa-hdd me-1"></i>
                                                {{ backup.size_mb }} MB
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="backup-actions">
                                    <a href="/api/backup/download/{{ backup.name }}" 
                                       class="btn btn-sm btn-outline-primary" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    {% if session.role == 'admin' %}
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteBackup('{{ backup.name }}')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                        <p class="text-muted">قم بإنشاء أول نسخة احتياطية لحماية بياناتك</p>
                        <button class="btn btn-success" onclick="createBackup()">
                            <i class="fas fa-plus me-2"></i>إنشاء نسخة احتياطية
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Auto Backup Settings -->
            <div class="backup-card card auto-backup-card">
                <div class="card-header border-0">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-robot me-2"></i>النسخ التلقائي
                    </h6>
                </div>
                <div class="card-body">
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="autoBackupEnabled">
                        <label class="form-check-label text-white" for="autoBackupEnabled">
                            تفعيل النسخ التلقائي
                        </label>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-white">تكرار النسخ:</label>
                        <select class="form-select" id="backupFrequency">
                            <option value="daily">يومياً</option>
                            <option value="weekly">أسبوعياً</option>
                            <option value="monthly">شهرياً</option>
                        </select>
                    </div>
                    <button class="btn btn-light btn-sm w-100">
                        <i class="fas fa-save me-2"></i>حفظ الإعدادات
                    </button>
                </div>
            </div>

            <!-- Backup Tips -->
            <div class="backup-card card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>نصائح مهمة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            قم بإنشاء نسخ احتياطية بانتظام
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            احتفظ بنسخ في أماكن متعددة
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            اختبر استعادة النسخ دورياً
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check text-success me-2"></i>
                            احذف النسخ القديمة لتوفير المساحة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إنشاء نسخة احتياطية
    async function createBackup() {
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('backupProgress');
        
        try {
            // إظهار شريط التقدم
            progressContainer.style.display = 'block';
            
            // محاكاة التقدم
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress > 90) progress = 90;
                progressBar.style.width = progress + '%';
            }, 200);
            
            const response = await fetch('/api/backup/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const result = await response.json();
            
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            
            setTimeout(() => {
                progressContainer.style.display = 'none';
                progressBar.style.width = '0%';
                
                if (result.success) {
                    showAlert('success', result.message);
                    location.reload();
                } else {
                    showAlert('danger', result.error);
                }
            }, 1000);
            
        } catch (error) {
            console.error('Error:', error);
            progressContainer.style.display = 'none';
            showAlert('danger', 'حدث خطأ في إنشاء النسخة الاحتياطية');
        }
    }
    
    // حذف نسخة احتياطية
    async function deleteBackup(filename) {
        if (!confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${filename}"؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
            return;
        }
        
        try {
            const response = await fetch(`/api/backup/delete/${filename}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                showAlert('success', result.message);
                location.reload();
            } else {
                showAlert('danger', result.error);
            }
            
        } catch (error) {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ في حذف النسخة الاحتياطية');
        }
    }
    
    // تحديث الإحصائيات
    function updateStats() {
        // يمكن إضافة تحديث دوري للإحصائيات هنا
    }
    
    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(updateStats, 30000);
    });
</script>
{% endblock %}
