{% extends "base.html" %}

{% block title %}إدارة الإيجارات - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-handshake me-2 text-primary"></i>
                        إدارة الإيجارات
                    </h1>
                    <p class="text-muted mb-0">إدارة ومتابعة جميع الإيجارات</p>
                </div>
                <div>
                    <div class="btn-group me-2" role="group">
                        <button class="btn btn-primary" onclick="showAddRentalModal()">
                            <i class="fas fa-plus me-2"></i>إيجار واحد
                        </button>
                        <a href="{{ url_for('create_multi_rental') }}" class="btn btn-success">
                            <i class="fas fa-layer-group me-2"></i>إيجار متعدد
                        </a>
                    </div>
                    <button class="btn btn-outline-info" onclick="location.reload()">
                        <i class="fas fa-sync me-2"></i>تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-tabs" id="rentalTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="unified-tab" data-bs-toggle="tab" data-bs-target="#unified" type="button" role="tab">
                        <i class="fas fa-list-alt me-2"></i>جميع الإيجارات
                        <span class="badge bg-primary ms-2">{{ all_rentals_unified|length if all_rentals_unified else 0 }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="active-unified-tab" data-bs-toggle="tab" data-bs-target="#active-unified" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>النشطة
                        <span class="badge bg-warning ms-2">{{ active_rentals_unified|length if active_rentals_unified else 0 }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="overdue-tab" data-bs-toggle="tab" data-bs-target="#overdue" type="button" role="tab">
                        <i class="fas fa-exclamation-triangle me-2"></i>المتأخرة
                        <span class="badge bg-danger ms-2">{{ overdue_rentals|length }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab">
                        <i class="fas fa-clock me-2"></i>الفردية النشطة
                        <span class="badge bg-info ms-2">{{ active_rentals|length }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="multi-tab" data-bs-toggle="tab" data-bs-target="#multi" type="button" role="tab">
                        <i class="fas fa-layer-group me-2"></i>المتعددة
                        <span class="badge bg-success ms-2">{{ multi_rentals|length if multi_rentals else 0 }}</span>
                    </button>
                </li>
            </ul>
        </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content" id="rentalTabContent">
        <!-- Unified All Rentals -->
        <div class="tab-pane fade show active" id="unified" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-alt me-2"></i>جميع الإيجارات (موحدة)
                        </h5>
                        <div>
                            <input type="text" class="form-control" id="searchUnifiedRentals" placeholder="البحث..." onkeyup="filterUnifiedRentals()">
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if all_rentals_unified %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="unifiedRentalsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الإيجار</th>
                                    <th>النوع</th>
                                    <th>العميل</th>
                                    <th>المعدة/المعدات</th>
                                    <th>تاريخ الإيجار</th>
                                    <th>تاريخ الإرجاع</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rental in all_rentals_unified %}
                                <tr>
                                    <td>
                                        <strong>
                                            {% if rental.rental_type == 'multi' %}
                                                #MR{{ rental.id }}
                                            {% else %}
                                                #{{ rental.id }}
                                            {% endif %}
                                        </strong>
                                    </td>
                                    <td>
                                        {% if rental.rental_type == 'multi' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-layer-group me-1"></i>متعدد
                                            </span>
                                        {% else %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-camera me-1"></i>فردي
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <i class="fas fa-user me-1"></i>{{ rental.customer_name }}
                                        {% if rental.customer_phone %}
                                            <br><small class="text-muted">{{ rental.customer_phone }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rental.rental_type == 'multi' %}
                                            <i class="fas fa-layer-group me-1"></i>
                                        {% else %}
                                            <i class="fas fa-camera me-1"></i>
                                        {% endif %}
                                        {{ rental.equipment_name }}
                                        {% if rental.items_count > 1 %}
                                            <br><small class="text-muted">{{ rental.items_count }} معدات</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ rental.rental_date }}</td>
                                    <td>{{ rental.return_date }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ rental.total_price }} ج.م</span>
                                    </td>
                                    <td>
                                        {% if rental.status == 'Active' %}
                                        <span class="badge bg-warning">نشط</span>
                                        {% elif rental.status == 'Returned' %}
                                        <span class="badge bg-success">مُرجع</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ rental.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rental.rental_type == 'multi' %}
                                            <button class="btn btn-sm btn-outline-info me-1" onclick="viewMultiRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="printMultiRentalInvoice({{ rental.id }})" title="طباعة الفاتورة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            {% if rental.status == 'Active' %}
                                            <button class="btn btn-sm btn-outline-success" onclick="returnMultiRental({{ rental.id }})" title="إرجاع المعدات">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            {% endif %}
                                        {% else %}
                                            <button class="btn btn-sm btn-outline-info me-1" onclick="viewRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="printRentalInvoice({{ rental.id }})" title="طباعة الفاتورة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            {% if rental.status == 'Active' %}
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editRental({{ rental.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="returnRental({{ rental.id }})" title="إرجاع المعدة">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                        <h5>لا توجد إيجارات</h5>
                        <p class="text-muted">لم يتم إنشاء أي إيجارات بعد</p>
                        <div class="btn-group" role="group">
                            <button class="btn btn-primary" onclick="showAddRentalModal()">
                                <i class="fas fa-plus me-2"></i>إيجار واحد
                            </button>
                            <a href="{{ url_for('create_multi_rental') }}" class="btn btn-success">
                                <i class="fas fa-layer-group me-2"></i>إيجار متعدد
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Active Unified Rentals -->
        <div class="tab-pane fade" id="active-unified" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>الإيجارات النشطة (موحدة)
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_rentals_unified %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-warning">
                                <tr>
                                    <th>رقم الإيجار</th>
                                    <th>النوع</th>
                                    <th>العميل</th>
                                    <th>المعدة/المعدات</th>
                                    <th>تاريخ الإيجار</th>
                                    <th>تاريخ الإرجاع</th>
                                    <th>السعر</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rental in active_rentals_unified %}
                                <tr>
                                    <td>
                                        <strong>
                                            {% if rental.rental_type == 'multi' %}
                                                #MR{{ rental.id }}
                                            {% else %}
                                                #{{ rental.id }}
                                            {% endif %}
                                        </strong>
                                    </td>
                                    <td>
                                        {% if rental.rental_type == 'multi' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-layer-group me-1"></i>متعدد
                                            </span>
                                        {% else %}
                                            <span class="badge bg-info">
                                                <i class="fas fa-camera me-1"></i>فردي
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <i class="fas fa-user me-1"></i>{{ rental.customer_name }}
                                    </td>
                                    <td>
                                        {% if rental.rental_type == 'multi' %}
                                            <i class="fas fa-layer-group me-1"></i>
                                        {% else %}
                                            <i class="fas fa-camera me-1"></i>
                                        {% endif %}
                                        {{ rental.equipment_name }}
                                    </td>
                                    <td>{{ rental.rental_date }}</td>
                                    <td>{{ rental.return_date }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ rental.total_price }} ج.م</span>
                                    </td>
                                    <td>
                                        {% if rental.rental_type == 'multi' %}
                                            <button class="btn btn-sm btn-outline-success me-1" onclick="returnMultiRental({{ rental.id }})" title="إرجاع المعدات">
                                                <i class="fas fa-undo"></i> إرجاع
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="printMultiRentalInvoice({{ rental.id }})" title="طباعة الفاتورة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewMultiRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        {% else %}
                                            <button class="btn btn-sm btn-outline-success me-1" onclick="returnRental({{ rental.id }})" title="إرجاع المعدة">
                                                <i class="fas fa-undo"></i> إرجاع
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary me-1" onclick="printRentalInvoice({{ rental.id }})" title="طباعة الفاتورة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editRental({{ rental.id }})" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" onclick="viewRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>لا توجد إيجارات نشطة</h5>
                        <p class="text-muted">جميع المعدات مُرجعة أو لا توجد إيجارات حالياً</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Active Individual Rentals -->
        <div class="tab-pane fade" id="active" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>الإيجارات النشطة
                    </h5>
                </div>
                <div class="card-body">
                    {% if active_rentals %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الإيجار</th>
                                    <th>العميل</th>
                                    <th>المعدة</th>
                                    <th>تاريخ الإيجار</th>
                                    <th>تاريخ الإرجاع</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rental in active_rentals %}
                                <tr>
                                    <td><strong>#{{ rental.id }}</strong></td>
                                    <td>
                                        <i class="fas fa-user me-1"></i>{{ rental.customer_name }}
                                    </td>
                                    <td>
                                        <i class="fas fa-camera me-1"></i>{{ rental.equipment_name }}
                                    </td>
                                    <td>{{ rental.rental_date }}</td>
                                    <td>{{ rental.return_date }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ rental.price }} ج.م</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">نشط</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-success me-1" onclick="returnRental({{ rental.id }})" title="إرجاع المعدة">
                                            <i class="fas fa-undo"></i> إرجاع
                                        </button>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editRental({{ rental.id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-info" onclick="viewRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>لا توجد إيجارات نشطة</h5>
                        <p class="text-muted">جميع المعدات مُرجعة أو لا توجد إيجارات حالياً</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Overdue Rentals -->
        <div class="tab-pane fade" id="overdue" role="tabpanel">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>الإيجارات المتأخرة
                    </h5>
                </div>
                <div class="card-body">
                    {% if overdue_rentals %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هناك {{ overdue_rentals|length }} إيجار متأخر يحتاج متابعة فورية!
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-danger">
                                <tr>
                                    <th>رقم الإيجار</th>
                                    <th>العميل</th>
                                    <th>المعدة</th>
                                    <th>تاريخ الإرجاع المحدد</th>
                                    <th>الأيام المتأخرة</th>
                                    <th>السعر</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rental in overdue_rentals %}
                                <tr class="table-danger">
                                    <td><strong>#{{ rental.id }}</strong></td>
                                    <td>
                                        <i class="fas fa-user me-1"></i>{{ rental.customer_name }}
                                        <br><small class="text-muted">{{ rental.customer_phone or '' }}</small>
                                    </td>
                                    <td>
                                        <i class="fas fa-camera me-1"></i>{{ rental.equipment_name }}
                                    </td>
                                    <td>{{ rental.return_date }}</td>
                                    <td>
                                        <span class="badge bg-danger">{{ rental.days_overdue }} يوم</span>
                                    </td>
                                    <td>{{ rental.price }} ج.م</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="contactCustomer('{{ rental.customer_phone }}')" title="اتصال بالعميل">
                                            <i class="fas fa-phone"></i> اتصال
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="returnRental({{ rental.id }})" title="إرجاع المعدة">
                                            <i class="fas fa-undo"></i> إرجاع
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>لا توجد إيجارات متأخرة</h5>
                        <p class="text-muted">جميع الإيجارات في الوقت المحدد</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Multi Rentals -->
        <div class="tab-pane fade" id="multi" role="tabpanel">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-layer-group me-2"></i>الإيجارات المتعددة
                    </h5>
                </div>
                <div class="card-body">
                    {% if multi_rentals %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-success">
                                <tr>
                                    <th>رقم المجموعة</th>
                                    <th>العميل</th>
                                    <th>عدد المعدات</th>
                                    <th>تاريخ الإيجار</th>
                                    <th>تاريخ الإرجاع</th>
                                    <th>إجمالي السعر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rental in multi_rentals %}
                                <tr>
                                    <td><strong>#MR{{ rental.id }}</strong></td>
                                    <td>
                                        <i class="fas fa-user me-1"></i>{{ rental.customer_name }}
                                        <br><small class="text-muted">{{ rental.customer_phone or '' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ rental.items_count }} معدة</span>
                                    </td>
                                    <td>{{ rental.rental_date }}</td>
                                    <td>{{ rental.return_date }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ rental.total_price }} ج.م</span>
                                    </td>
                                    <td>
                                        {% if rental.status == 'Active' %}
                                        <span class="badge bg-warning">نشط</span>
                                        {% elif rental.status == 'Returned' %}
                                        <span class="badge bg-success">مُرجع</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ rental.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info me-1" onclick="viewMultiRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if rental.status == 'Active' %}
                                        <button class="btn btn-sm btn-outline-success" onclick="returnMultiRental({{ rental.id }})" title="إرجاع المعدات">
                                            <i class="fas fa-undo"></i> إرجاع
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                        <h5>لا توجد إيجارات متعددة</h5>
                        <p class="text-muted">لم يتم إنشاء أي إيجارات متعددة بعد</p>
                        <a href="{{ url_for('create_multi_rental') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>إنشاء إيجار متعدد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- All Rentals -->
        <div class="tab-pane fade" id="all" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>جميع الإيجارات
                        </h5>
                        <div>
                            <input type="text" class="form-control" id="searchAllRentals" placeholder="البحث..." onkeyup="filterAllRentals()">
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="allRentalsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الإيجار</th>
                                    <th>العميل</th>
                                    <th>المعدة</th>
                                    <th>تاريخ الإيجار</th>
                                    <th>تاريخ الإرجاع</th>
                                    <th>السعر</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rental in all_rentals %}
                                <tr>
                                    <td><strong>#{{ rental.id }}</strong></td>
                                    <td>{{ rental.customer_name }}</td>
                                    <td>{{ rental.equipment_name }}</td>
                                    <td>{{ rental.rental_date }}</td>
                                    <td>{{ rental.return_date }}</td>
                                    <td>{{ rental.price }} ج.م</td>
                                    <td>
                                        {% if rental.status == 'Active' %}
                                        <span class="badge bg-warning">نشط</span>
                                        {% elif rental.status == 'Returned' %}
                                        <span class="badge bg-success">مُرجع</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ rental.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-info me-1" onclick="viewRentalDetails({{ rental.id }})" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if rental.status == 'Active' %}
                                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editRental({{ rental.id }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="returnRental({{ rental.id }})" title="إرجاع المعدة">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Rental Modal -->
<div class="modal fade" id="addRentalModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء إيجار جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addRentalForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العميل:</label>
                                <select class="form-select" id="customerId" required>
                                    <option value="">اختر العميل</option>
                                    <!-- سيتم تحميل العملاء ديناميكياً -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المعدة:</label>
                                <select class="form-select" id="equipmentId" required>
                                    <option value="">اختر المعدة</option>
                                    <!-- سيتم تحميل المعدات المتاحة ديناميكياً -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الإيجار:</label>
                                <input type="date" class="form-control" id="rentalDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الإرجاع:</label>
                                <input type="date" class="form-control" id="returnDate" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">السعر (ج.م):</label>
                                <input type="number" class="form-control" id="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الملاحظات:</label>
                                <textarea class="form-control" id="notes" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveRental()">
                    <i class="fas fa-save me-2"></i>حفظ الإيجار
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Filter all rentals
    function filterAllRentals() {
        const searchInput = document.getElementById('searchAllRentals').value.toLowerCase();
        const rows = document.querySelectorAll('#allRentalsTable tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (!searchInput || text.includes(searchInput)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    
    // Show add rental modal
    function showAddRentalModal() {
        // تحميل العملاء والمعدات
        loadCustomersAndEquipment();

        // تعيين التاريخ الحالي
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('rentalDate').value = today;

        const modal = new bootstrap.Modal(document.getElementById('addRentalModal'));
        modal.show();
    }

    // Load customers and equipment
    function loadCustomersAndEquipment() {
        // تحميل العملاء
        fetch('/api/customers')
            .then(response => response.json())
            .then(customers => {
                const customerSelect = document.getElementById('customerId');
                customerSelect.innerHTML = '<option value="">اختر العميل</option>';
                customers.forEach(customer => {
                    customerSelect.innerHTML += `<option value="${customer.id}">${customer.name} - ${customer.phone}</option>`;
                });
            })
            .catch(error => console.error('خطأ في تحميل العملاء:', error));

        // تحميل المعدات المتاحة
        fetch('/api/equipment')
            .then(response => response.json())
            .then(equipment => {
                const equipmentSelect = document.getElementById('equipmentId');
                equipmentSelect.innerHTML = '<option value="">اختر المعدة</option>';
                equipment.filter(eq => eq.status === 'Available').forEach(eq => {
                    equipmentSelect.innerHTML += `<option value="${eq.id}">${eq.name} - ${eq.model}</option>`;
                });
            })
            .catch(error => console.error('خطأ في تحميل المعدات:', error));
    }

    // Save rental
    function saveRental() {
        const data = {
            customer_id: parseInt(document.getElementById('customerId').value),
            equipment_id: parseInt(document.getElementById('equipmentId').value),
            rental_date: document.getElementById('rentalDate').value,
            return_date: document.getElementById('returnDate').value,
            price: parseFloat(document.getElementById('price').value),
            notes: document.getElementById('notes').value.trim()
        };

        // التحقق من البيانات
        if (!data.customer_id || !data.equipment_id || !data.rental_date || !data.return_date || !data.price) {
            showAlert('danger', 'يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (new Date(data.return_date) <= new Date(data.rental_date)) {
            showAlert('danger', 'تاريخ الإرجاع يجب أن يكون بعد تاريخ الإيجار');
            return;
        }

        fetch('/api/rentals', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('addRentalModal')).hide();
                location.reload();
            } else {
                showAlert('danger', data.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في إنشاء الإيجار');
        });
    }
    
    // Return rental
    function returnRental(id) {
        if (confirm('هل أنت متأكد من إرجاع هذا الإيجار؟\n\nسيتم تحديث حالة المعدة إلى "متاح" تلقائياً.')) {
            const today = new Date().toISOString().split('T')[0];

            const data = {
                action: 'return',
                actual_return_date: today
            };

            fetch(`/api/rentals/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    location.reload();
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ في إرجاع الإيجار');
            });
        }
    }

    // Edit rental
    function editRental(id) {
        showAlert('info', 'ميزة تعديل الإيجارات قيد التطوير - يمكن استخدام التطبيق المكتبي للتعديل');
    }

    // View rental details
    function viewRentalDetails(id) {
        // جلب تفاصيل الإيجار وعناصر المخزون
        Promise.all([
            fetch(`/api/rentals/${id}`),
            fetch(`/api/rentals/${id}/inventory`)
        ])
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(([rentalData, inventoryData]) => {
            if (rentalData.success) {
                const rental = rentalData.rental;

                let details = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات الإيجار</h6>
                            <strong>رقم الإيجار:</strong> #${rental.id}<br>
                            <strong>العميل:</strong> ${rental.customer_name}<br>
                            <strong>المعدة:</strong> ${rental.equipment_name}<br>
                            <strong>تاريخ الإيجار:</strong> ${rental.rental_date}<br>
                            <strong>تاريخ الإرجاع:</strong> ${rental.return_date}<br>
                            <strong>السعر:</strong> ${rental.price} ج.م<br>
                            <strong>الحالة:</strong> ${rental.status}<br>
                            <strong>الملاحظات:</strong> ${rental.notes || 'لا توجد ملاحظات'}
                        </div>
                        <div class="col-md-6">
                `;

                // إضافة عناصر المخزون إذا كانت موجودة
                if (inventoryData.success && inventoryData.items && inventoryData.items.length > 0) {
                    details += `
                        <h6><i class="fas fa-boxes me-2"></i>عناصر المخزون المرفقة</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>العنصر</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    let totalInventoryCost = 0;
                    inventoryData.items.forEach(item => {
                        const itemCost = (item.rental_price || 0) * (item.quantity || 0);
                        totalInventoryCost += itemCost;

                        let statusBadge = '';
                        switch(item.status) {
                            case 'Active':
                                statusBadge = '<span class="badge bg-success">نشط</span>';
                                break;
                            case 'Returned':
                                statusBadge = '<span class="badge bg-primary">مُرجع</span>';
                                break;
                            case 'Damaged':
                                statusBadge = '<span class="badge bg-warning">تالف</span>';
                                break;
                            case 'Lost':
                                statusBadge = '<span class="badge bg-danger">مفقود</span>';
                                break;
                            default:
                                statusBadge = '<span class="badge bg-secondary">' + item.status + '</span>';
                        }

                        details += `
                            <tr>
                                <td>${item.item_name}</td>
                                <td>${item.quantity}</td>
                                <td>${itemCost.toFixed(2)} ج.م</td>
                                <td>${statusBadge}</td>
                            </tr>
                        `;
                    });

                    details += `
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="2">إجمالي المخزون</th>
                                        <th>${totalInventoryCost.toFixed(2)} ج.م</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    `;
                } else {
                    details += `
                        <h6><i class="fas fa-boxes me-2"></i>عناصر المخزون</h6>
                        <p class="text-muted">لا توجد عناصر مخزون مرفقة مع هذا الإيجار</p>
                    `;
                }

                details += `
                        </div>
                    </div>
                `;

                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-info alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <h6><i class="fas fa-info-circle me-2"></i>تفاصيل الإيجار #${rental.id}</h6>
                    ${details}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
            } else {
                showAlert('danger', rentalData.error);
            }
        })
        .catch(error => {
            showAlert('danger', 'حدث خطأ في عرض تفاصيل الإيجار');
        });
    }
    
    // Contact customer
    function contactCustomer(phone) {
        if (phone) {
            window.open(`tel:${phone}`, '_self');
        } else {
            showAlert('warning', 'رقم الهاتف غير متوفر');
        }
    }
    
    // View multi rental details
    function viewMultiRentalDetails(rentalGroupId) {
        fetch(`/api/multi-rentals/${rentalGroupId}/items`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const items = data.items;
                    let itemsHtml = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>المعدة</th><th>السعر اليومي</th><th>عدد الأيام</th><th>المجموع الفرعي</th><th>الحالة</th></tr></thead><tbody>';

                    let totalCost = 0;
                    items.forEach(item => {
                        totalCost += item.subtotal;
                        itemsHtml += `
                            <tr>
                                <td>${item.equipment_name} - ${item.equipment_model}</td>
                                <td>${item.rental_price} ج.م</td>
                                <td>${item.days_count}</td>
                                <td>${item.subtotal} ج.م</td>
                                <td>
                                    ${item.status === 'Active' ? '<span class="badge bg-warning">نشط</span>' : '<span class="badge bg-success">مُرجع</span>'}
                                </td>
                            </tr>
                        `;
                    });

                    itemsHtml += `</tbody><tfoot><tr class="table-success"><th colspan="3">الإجمالي</th><th>${totalCost.toFixed(2)} ج.م</th><th></th></tr></tfoot></table></div>`;

                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-info alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <h6><i class="fas fa-layer-group me-2"></i>تفاصيل الإيجار المتعدد #MR${rentalGroupId}</h6>
                        ${itemsHtml}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;

                    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
                } else {
                    showAlert('danger', data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'حدث خطأ في عرض تفاصيل الإيجار المتعدد');
            });
    }

    // Return multi rental
    function returnMultiRental(rentalGroupId) {
        if (confirm('هل أنت متأكد من إرجاع جميع معدات هذا الإيجار؟\n\nسيتم تحديث حالة جميع المعدات إلى "متاح" تلقائياً.')) {
            // الحصول على عناصر الإيجار أولاً
            fetch(`/api/multi-rentals/${rentalGroupId}/items`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const items = data.items;
                        const returnedItems = items.map(item => ({
                            item_id: item.id,
                            condition: 'Good',
                            notes: ''
                        }));

                        // إرجاع الإيجار المتعدد
                        fetch(`/api/multi-rentals/${rentalGroupId}/return`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                returned_items: returnedItems
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showAlert('success', 'تم إرجاع جميع المعدات بنجاح');
                                location.reload();
                            } else {
                                showAlert('danger', data.error);
                            }
                        })
                        .catch(error => {
                            showAlert('danger', 'حدث خطأ في إرجاع الإيجار المتعدد');
                        });
                    } else {
                        showAlert('danger', data.error);
                    }
                })
                .catch(error => {
                    showAlert('danger', 'حدث خطأ في الحصول على تفاصيل الإيجار');
                });
        }
    }

    // Filter unified rentals
    function filterUnifiedRentals() {
        const input = document.getElementById('searchUnifiedRentals');
        const filter = input.value.toLowerCase();
        const table = document.getElementById('unifiedRentalsTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;

            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }

            row.style.display = found ? '' : 'none';
        }
    }

    // Auto-refresh overdue rentals every 5 minutes
    setInterval(function() {
        if (document.getElementById('overdue-tab').classList.contains('active')) {
            // تحديث الإيجارات المتأخرة
            console.log('تحديث الإيجارات المتأخرة...');
        }
    }, 300000); // 5 minutes

    // دوال طباعة الفواتير
    function printRentalInvoice(rentalId) {
        // فتح الفاتورة في نافذة جديدة للتحميل
        window.open(`/rental/${rentalId}/invoice`, '_blank');
    }

    function printMultiRentalInvoice(rentalGroupId) {
        // فتح الفاتورة في نافذة جديدة للتحميل
        window.open(`/multi-rental/${rentalGroupId}/invoice`, '_blank');
    }
</script>
{% endblock %}
