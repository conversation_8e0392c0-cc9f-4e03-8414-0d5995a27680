#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def test_equipment_api():
    """اختبار API المعدات"""
    base_url = "http://127.0.0.1:5000"
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار API المعدات
        print("🔍 اختبار API المعدات...")
        response = session.get(f"{base_url}/api/equipment/all")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API المعدات يعمل")
            print(f"📊 عدد المعدات: {data.get('count', 0)}")
            
            if 'equipment' in data and len(data['equipment']) > 0:
                print("✅ يحتوي على بيانات المعدات")
                print(f"📋 عينة من المعدات:")
                for i, eq in enumerate(data['equipment'][:5]):
                    source = eq.get('source', 'unknown')
                    print(f"   {i+1}. ID:{eq['id']} - {eq['name']} ({source}) - {eq['category']}")
            else:
                print("❌ لا يحتوي على بيانات المعدات")
                
        else:
            print(f"❌ خطأ في API المعدات: {response.status_code}")
            print(f"Response: {response.text}")
    else:
        print("❌ فشل تسجيل الدخول")

if __name__ == "__main__":
    test_equipment_api()
