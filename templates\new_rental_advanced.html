{% extends "base.html" %}

{% block title %}إنشاء إيجار جديد - نظام إدارة تأجير المعدات{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">
                        <i class="fas fa-plus-circle me-2 text-success"></i>
                        إنشاء إيجار جديد
                    </h1>
                    <p class="text-muted mb-0">إضافة إيجار جديد للمعدات</p>
                </div>
                <div>
                    <button class="btn btn-outline-secondary" onclick="window.location.href='{{ url_for('rentals') }}'">
                        <i class="fas fa-arrow-left me-2"></i>العودة للإيجارات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rental Form -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>بيانات الإيجار
                    </h5>
                </div>
                <div class="card-body">
                    <form id="rentalForm">
                        <!-- Customer Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">العميل *</label>
                                <select class="form-select" id="customerId" required>
                                    <option value="">اختر العميل...</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">تاريخ الإيجار *</label>
                                <input type="date" class="form-control" id="rentalDate" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">تاريخ الإرجاع المتوقع *</label>
                                <input type="date" class="form-control" id="returnDate" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">ملاحظات</label>
                                <input type="text" class="form-control" id="notes" placeholder="ملاحظات إضافية...">
                            </div>
                        </div>

                        <!-- Equipment Selection -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">
                                    <i class="fas fa-camera me-2"></i>اختيار المعدات
                                </h6>
                                
                                <!-- Equipment Filters -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">فلتر حسب الفئة:</label>
                                        <select class="form-select" id="categoryFilter" onchange="filterEquipment()">
                                            <option value="">جميع الفئات</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">البحث:</label>
                                        <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن معدة..." onkeyup="filterEquipment()">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-secondary w-100" onclick="clearFilters()">
                                            <i class="fas fa-times me-2"></i>مسح الفلاتر
                                        </button>
                                    </div>
                                </div>

                                <!-- Available Equipment -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">المعدات المتاحة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table id="availableEquipmentTable" class="table table-hover table-sm">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th width="50">اختيار</th>
                                                        <th>اسم المعدة</th>
                                                        <th>الموديل</th>
                                                        <th>الفئة</th>
                                                        <th>السعر اليومي</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td colspan="6" class="text-center text-muted">
                                                            <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل المعدات...
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Selected Equipment -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">المعدات المختارة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="selectedEquipmentList">
                                            <p class="text-muted text-center">لم يتم اختيار أي معدات بعد</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- External Equipment Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="fw-bold mb-3">
                                    <i class="fas fa-external-link-alt me-2"></i>معدات الاستقطاب الخارجية
                                </h6>
                                
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">المعدات الخارجية المتاحة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table id="externalEquipmentTable" class="table table-hover table-sm">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th width="50">اختيار</th>
                                                        <th>اسم المعدة</th>
                                                        <th>المورد</th>
                                                        <th>التكلفة اليومية</th>
                                                        <th>السعر للعميل</th>
                                                        <th>هامش الربح</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td colspan="6" class="text-center text-muted">
                                                            <i class="fas fa-spinner fa-spin me-2"></i>جاري تحميل المعدات الخارجية...
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Total and Submit -->
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="fw-bold">ملخص الإيجار:</h6>
                                        <div id="rentalSummary">
                                            <p class="mb-1">عدد المعدات: <span id="totalItems">0</span></p>
                                            <p class="mb-1">عدد الأيام: <span id="totalDays">0</span></p>
                                            <p class="mb-0 fw-bold">الإجمالي: <span id="totalAmount">0.00</span> ج.م</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-save me-2"></i>إنشاء الإيجار
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedEquipment = [];
let selectedExternalEquipment = [];
let availableEquipment = [];
let externalEquipment = [];

// Load data when page loads
document.addEventListener('DOMContentLoaded', function() {
    // استخدام البيانات المرسلة من الخادم بدلاً من API calls
    initializeWithServerData();
    setDefaultDates();
});

// Initialize with server data
function initializeWithServerData() {
    // تحميل العملاء
    {% if customers %}
    const customersData = {{ customers | tojson }};
    loadCustomersFromData(customersData);
    {% else %}
    loadCustomers(); // fallback to API
    {% endif %}

    // تحميل المعدات المتاحة
    {% if available_equipment %}
    const equipmentData = {{ available_equipment | tojson }};
    availableEquipment = equipmentData;
    updateAvailableEquipmentTable(equipmentData);
    updateCategoryFilter(equipmentData);
    console.log('✅ تم تحميل', equipmentData.length, 'معدة داخلية من الخادم');
    {% else %}
    loadAvailableEquipment(); // fallback to API
    {% endif %}

    // تحميل المعدات الخارجية
    {% if external_equipment %}
    const externalData = {{ external_equipment | tojson }};
    externalEquipment = externalData;
    updateExternalEquipmentTable(externalData);
    console.log('✅ تم تحميل', externalData.length, 'معدة خارجية من الخادم');
    {% else %}
    loadExternalEquipment(); // fallback to API
    {% endif %}
}

// Load customers from server data
function loadCustomersFromData(customers) {
    const select = document.getElementById('customerId');
    select.innerHTML = '<option value="">اختر العميل...</option>';

    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = `${customer.name} - ${customer.phone}`;
        select.appendChild(option);
    });
    console.log('✅ تم تحميل', customers.length, 'عميل من الخادم');
}

// Set default dates
function setDefaultDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    document.getElementById('rentalDate').value = today.toISOString().split('T')[0];
    document.getElementById('returnDate').value = tomorrow.toISOString().split('T')[0];
}

// Load customers
function loadCustomers() {
    fetch('/api/customers')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('customerId');
            select.innerHTML = '<option value="">اختر العميل...</option>';
            
            if (data.customers) {
                data.customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = `${customer.name} - ${customer.phone}`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل العملاء:', error);
            showAlert('danger', 'فشل في تحميل العملاء');
        });
}

// Load available equipment
function loadAvailableEquipment() {
    fetch('/api/equipment/available')
        .then(response => response.json())
        .then(data => {
            if (Array.isArray(data)) {
                availableEquipment = data;
                updateAvailableEquipmentTable(data);
                updateCategoryFilter(data);
            } else {
                console.error('خطأ في تحميل المعدات:', data.error);
                showAlert('danger', 'فشل في تحميل المعدات المتاحة');
            }
        })
        .catch(error => {
            console.error('خطأ في الشبكة:', error);
            showAlert('danger', 'خطأ في الاتصال بالخادم');
        });
}

// Load external equipment
function loadExternalEquipment() {
    fetch('/api/external-equipment/available')
        .then(response => response.json())
        .then(data => {
            if (data.equipment) {
                externalEquipment = data.equipment;
                updateExternalEquipmentTable(data.equipment);
            } else {
                console.error('خطأ في تحميل المعدات الخارجية:', data.error);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المعدات الخارجية:', error);
        });
}

// Update available equipment table
function updateAvailableEquipmentTable(equipment) {
    const tbody = document.querySelector('#availableEquipmentTable tbody');
    tbody.innerHTML = '';

    if (equipment.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد معدات متاحة</td></tr>';
        return;
    }

    equipment.forEach(eq => {
        const row = `
            <tr data-category="${eq.category}" data-name="${eq.name.toLowerCase()}">
                <td>
                    <input type="checkbox" class="form-check-input" onchange="toggleEquipment(${eq.id}, this)">
                </td>
                <td><strong>${eq.name}</strong></td>
                <td>${eq.model || ''}</td>
                <td><span class="badge bg-info">${eq.category}</span></td>
                <td>${eq.daily_price} ج.م</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewEquipmentDetails(${eq.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Update external equipment table
function updateExternalEquipmentTable(equipment) {
    const tbody = document.querySelector('#externalEquipmentTable tbody');
    tbody.innerHTML = '';

    if (equipment.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد معدات خارجية متاحة</td></tr>';
        return;
    }

    equipment.forEach(eq => {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input" onchange="toggleExternalEquipment(${eq.id}, this)">
                </td>
                <td><strong>${eq.equipment_name}</strong></td>
                <td>${eq.supplier_name}</td>
                <td>${eq.daily_rental_cost} ج.م</td>
                <td>${eq.customer_daily_price} ج.م</td>
                <td>${eq.profit_margin} ج.م</td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Update category filter
function updateCategoryFilter(equipment) {
    const categories = [...new Set(equipment.map(eq => eq.category))];
    const select = document.getElementById('categoryFilter');
    
    select.innerHTML = '<option value="">جميع الفئات</option>';
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        select.appendChild(option);
    });
}

// Filter equipment
function filterEquipment() {
    const categoryFilter = document.getElementById('categoryFilter').value;
    const searchFilter = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('#availableEquipmentTable tbody tr');
    
    rows.forEach(row => {
        const category = row.dataset.category;
        const name = row.dataset.name;
        
        const categoryMatch = !categoryFilter || category === categoryFilter;
        const nameMatch = !searchFilter || (name && name.includes(searchFilter));
        
        if (categoryMatch && nameMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Clear filters
function clearFilters() {
    document.getElementById('categoryFilter').value = '';
    document.getElementById('searchInput').value = '';
    filterEquipment();
}

// Toggle equipment selection
function toggleEquipment(equipmentId, checkbox) {
    if (checkbox.checked) {
        const equipment = availableEquipment.find(eq => eq.id === equipmentId);
        if (equipment) {
            selectedEquipment.push(equipment);
        }
    } else {
        selectedEquipment = selectedEquipment.filter(eq => eq.id !== equipmentId);
    }
    updateSelectedEquipmentList();
    updateRentalSummary();
}

// Toggle external equipment selection
function toggleExternalEquipment(equipmentId, checkbox) {
    if (checkbox.checked) {
        const equipment = externalEquipment.find(eq => eq.id === equipmentId);
        if (equipment) {
            selectedExternalEquipment.push(equipment);
        }
    } else {
        selectedExternalEquipment = selectedExternalEquipment.filter(eq => eq.id !== equipmentId);
    }
    updateRentalSummary();
}

// Update selected equipment list
function updateSelectedEquipmentList() {
    const container = document.getElementById('selectedEquipmentList');
    
    if (selectedEquipment.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لم يتم اختيار أي معدات بعد</p>';
        return;
    }
    
    let html = '';
    selectedEquipment.forEach(eq => {
        html += `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <strong>${eq.name}</strong>
                    <small class="text-muted d-block">${eq.category} - ${eq.daily_price} ج.م/يوم</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeSelectedEquipment(${eq.id})">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Remove selected equipment
function removeSelectedEquipment(equipmentId) {
    selectedEquipment = selectedEquipment.filter(eq => eq.id !== equipmentId);
    
    // Uncheck the checkbox
    const checkbox = document.querySelector(`#availableEquipmentTable input[onchange*="${equipmentId}"]`);
    if (checkbox) {
        checkbox.checked = false;
    }
    
    updateSelectedEquipmentList();
    updateRentalSummary();
}

// Update rental summary
function updateRentalSummary() {
    const rentalDate = new Date(document.getElementById('rentalDate').value);
    const returnDate = new Date(document.getElementById('returnDate').value);
    
    let days = 1;
    if (rentalDate && returnDate && returnDate > rentalDate) {
        days = Math.ceil((returnDate - rentalDate) / (1000 * 60 * 60 * 24));
    }
    
    const totalItems = selectedEquipment.length + selectedExternalEquipment.length;
    let totalAmount = 0;
    
    // Calculate internal equipment cost
    selectedEquipment.forEach(eq => {
        totalAmount += eq.daily_price * days;
    });
    
    // Calculate external equipment cost
    selectedExternalEquipment.forEach(eq => {
        totalAmount += eq.customer_daily_price * days;
    });
    
    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('totalDays').textContent = days;
    document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
}

// View equipment details
function viewEquipmentDetails(equipmentId) {
    // Implementation for viewing equipment details
    alert('عرض تفاصيل المعدة: ' + equipmentId);
}

// Show alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Handle form submission
document.getElementById('rentalForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (selectedEquipment.length === 0 && selectedExternalEquipment.length === 0) {
        showAlert('warning', 'يرجى اختيار معدة واحدة على الأقل');
        return;
    }
    
    const formData = {
        customer_id: document.getElementById('customerId').value,
        rental_date: document.getElementById('rentalDate').value,
        return_date: document.getElementById('returnDate').value,
        notes: document.getElementById('notes').value,
        equipment: selectedEquipment.map(eq => ({
            id: eq.id,
            daily_price: eq.daily_price
        })),
        external_equipment: selectedExternalEquipment.map(eq => ({
            id: eq.id,
            daily_price: eq.customer_daily_price
        }))
    };
    
    fetch('/api/rental/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'تم إنشاء الإيجار بنجاح');
            setTimeout(() => {
                window.location.href = '/rentals';
            }, 2000);
        } else {
            showAlert('danger', data.error || 'فشل في إنشاء الإيجار');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('danger', 'حدث خطأ في إنشاء الإيجار');
    });
});

// Update summary when dates change
document.getElementById('rentalDate').addEventListener('change', updateRentalSummary);
document.getElementById('returnDate').addEventListener('change', updateRentalSummary);
</script>
{% endblock %}
