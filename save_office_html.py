#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def save_office_html():
    """حفظ محتوى HTML لصفحة تفاصيل المكتب"""
    base_url = "http://127.0.0.1:5000"
    
    # إنشاء session
    session = requests.Session()
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data)
    
    if response.status_code == 200 and 'dashboard' in response.url:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # اختبار صفحة تفاصيل المكتب 3
        office_id = 3
        print(f"🔍 جلب محتوى صفحة تفاصيل المكتب {office_id}...")
        response = session.get(f"{base_url}/offices/{office_id}")
        
        if response.status_code == 200:
            content = response.text
            
            # حفظ المحتوى في ملف
            with open('office_details_debug.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم حفظ محتوى HTML في office_details_debug.html")
            print(f"📊 حجم المحتوى: {len(content)} حرف")
            
            # البحث عن التعليقات التشخيصية
            if 'Debug: Equipment count' in content:
                print("✅ يحتوي على تعليقات تشخيصية")
                
                # استخراج قيم التشخيص
                import re
                debug_matches = re.findall(r'<!-- Debug: (.*?) -->', content)
                for match in debug_matches:
                    print(f"🔍 {match}")
            else:
                print("❌ لا يحتوي على تعليقات تشخيصية")
                
        else:
            print(f"❌ خطأ في صفحة تفاصيل المكتب: {response.status_code}")
            
    else:
        print("❌ فشل تسجيل الدخول")

if __name__ == "__main__":
    save_office_html()
