#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

def test_all_pages():
    """اختبار جميع الصفحات المشكوك فيها"""
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 اختبار شامل لجميع الصفحات")
    print("=" * 60)
    
    # إنشاء session للحفاظ على تسجيل الدخول
    session = requests.Session()
    
    # 1. تسجيل الدخول
    print("1️⃣ تسجيل الدخول...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    try:
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'dashboard' in response.url:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل تسجيل الدخول: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في تسجيل الدخول: {e}")
        return
    
    # قائمة الصفحات للاختبار
    pages_to_test = [
        ("/equipment", "صفحة المعدات العامة"),
        ("/customers", "صفحة العملاء"),
        ("/offices", "صفحة المكاتب"),
        ("/offices/1", "تفاصيل المكتب الأول"),
        ("/new_rental", "صفحة إنشاء الإيجار"),
    ]
    
    results = {}
    
    for url, name in pages_to_test:
        print(f"\n🔍 اختبار {name}...")
        try:
            response = session.get(f"{base_url}{url}")
            if response.status_code == 200:
                content = response.text
                
                # فحص محتوى الصفحة
                analysis = analyze_page_content(content, name)
                results[url] = {
                    'status': 'success',
                    'analysis': analysis
                }
                
                print(f"✅ {name}: يعمل")
                for key, value in analysis.items():
                    print(f"   📊 {key}: {value}")
                    
            elif response.status_code == 302:
                print(f"🔄 {name}: إعادة توجيه")
                results[url] = {'status': 'redirect'}
            else:
                print(f"❌ {name}: خطأ {response.status_code}")
                results[url] = {'status': 'error', 'code': response.status_code}
                
        except Exception as e:
            print(f"❌ {name}: خطأ - {e}")
            results[url] = {'status': 'exception', 'error': str(e)}
    
    # تحليل النتائج
    print("\n" + "=" * 60)
    print("📊 تحليل النتائج")
    print("=" * 60)
    
    working_pages = [url for url, result in results.items() if result['status'] == 'success']
    broken_pages = [url for url, result in results.items() if result['status'] != 'success']
    
    print(f"✅ الصفحات التي تعمل: {len(working_pages)}")
    for url in working_pages:
        print(f"   - {url}")
    
    print(f"\n❌ الصفحات التي لا تعمل: {len(broken_pages)}")
    for url in broken_pages:
        print(f"   - {url}: {results[url]['status']}")
    
    return results

def analyze_page_content(content, page_name):
    """تحليل محتوى الصفحة"""
    analysis = {}
    
    # فحص عام
    analysis['حجم المحتوى'] = f"{len(content)} حرف"
    analysis['يحتوي على جدول'] = 'table' in content
    analysis['يحتوي على بيانات'] = 'tbody' in content
    
    # فحص خاص بكل صفحة
    if 'المعدات' in page_name:
        analysis['صفوف المعدات'] = content.count('<tr data-category=')
        analysis['يحتوي على معدات'] = 'كاميرا' in content or 'عدسة' in content
        analysis['يحتوي على فلاتر'] = 'filterEquipment' in content
        
    elif 'العملاء' in page_name:
        analysis['صفوف العملاء'] = content.count('<tr data-customer=')
        analysis['يحتوي على عملاء'] = 'customer-name' in content
        analysis['يحتوي على بحث'] = 'searchCustomers' in content
        
    elif 'المكاتب' in page_name:
        analysis['صفوف المكاتب'] = content.count('<tr data-office=')
        analysis['روابط المكاتب'] = content.count('/offices/')
        analysis['يحتوي على مكاتب'] = 'office-name' in content
        
    elif 'تفاصيل المكتب' in page_name:
        analysis['يحتوي على تفاصيل'] = 'office-details' in content
        analysis['يحتوي على معدات المكتب'] = 'معدات المكتب' in content or 'المعدات المستقطبة' in content
        
    elif 'إنشاء الإيجار' in page_name:
        analysis['يحتوي على نموذج'] = 'rentalForm' in content
        analysis['يحتوي على معدات متاحة'] = 'المعدات المتاحة' in content
        analysis['يحتوي على معدات خارجية'] = 'المعدات الخارجية' in content
    
    return analysis

def main():
    print("🚀 اختبار شامل لجميع صفحات النظام")
    print("=" * 70)
    print(f"⏰ وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = test_all_pages()
    
    print("\n" + "=" * 70)
    print("✅ انتهى الاختبار")
    print("🌐 للوصول للنظام: http://127.0.0.1:5000")

if __name__ == "__main__":
    main()
